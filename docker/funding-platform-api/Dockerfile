FROM node:22.14 AS build

WORKDIR /app

COPY --chown=node:node --chmod=755 package*.json tsconfig* yarn.lock ./
RUN yarn install
COPY --chown=node:node . .
RUN yarn build

FROM node:22.14-alpine AS shipment
WORKDIR /app
COPY --from=build /app/dist ./dist
COPY --from=build /app/package*.json /app/yarn.lock ./
RUN yarn install
RUN apk update && apk add --no-cache postgresql16-client

EXPOSE 3000
CMD [ "node", "dist/main.js" ]
