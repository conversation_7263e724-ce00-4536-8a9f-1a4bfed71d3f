name: Feature ✨

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - 'release/**'

jobs:
  env:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/env.yaml@release/add-default-pipelines

  tests:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/tests.yaml@release/add-default-pipelines
    needs:
      - env
    secrets: inherit

  e2e-tests:
    runs-on: ubuntu-latest
    needs:
      - env
      - tests
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.14.0'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Run e2e tests
        run: yarn test:e2e
        env:
          POSTGRES_MIGRATIONS_RUN: 'true'
          NODE_ENV: 'test'

  build-and-push:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/build-and-push.yaml@release/add-default-pipelines
    strategy:
      matrix:
        svc: ${{ fromJson(needs.env.outputs.services) }}
    with:
      app: ${{ needs.env.outputs.app }}
      services: ${{ matrix.svc }}
      tag: ${{ needs.env.outputs.tag }}
      environment: 'development'
      dockerfile: ./docker/${{ matrix.svc }}/Dockerfile
    secrets:
      aws_access_key_id: ${{ secrets.GA_ACCESS_KEY }}
      aws_secret_access_key: ${{ secrets.GA_SECRET_KEY }}
      aws_region: ${{ secrets.AWS_REGION }}
      ecr_registry: ${{ secrets.ECR_REGISTRY }}
      ecr_registry_id: ${{ secrets.ECR_REGISTRY_ID }}
    needs:
      - env
      - tests
      - e2e-tests

  deploy:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/deploy.yaml@release/add-default-pipelines
    strategy:
      max-parallel: 0
      matrix:
        svc: ${{ fromJson(needs.env.outputs.services) }}
    with:
      app: ${{ needs.env.outputs.app }}
      services: ${{ matrix.svc }}
      tag: ${{ needs.env.outputs.tag }}
      environment: 'development'
    secrets:
      GITOPS_GITHUB_APPLICATION_PRIVATE_KEY: ${{ secrets.GITOPS_GITHUB_APPLICATION_PRIVATE_KEY }}
    needs:
      - env
      - build-and-push
