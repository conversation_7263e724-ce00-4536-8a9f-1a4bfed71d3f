name: Main 🚀

# on:
#   push:
#     branches:
#       - main

on:
  pull_request:
    branches:
      - main
    types:
      - closed
    paths-ignore:
      # - '.github/**'
      - 'catalog-info.yaml'
      - 'Dockerfile'
      - 'docker-compose.yaml'
      - 'docs/**'
      - 'mkdocs.yaml'
      - 'README.md'

jobs:
  env:
    if: github.event.pull_request.merged == true
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/env.yaml@release/add-default-pipelines

  fetch-tag:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/fetch-tag.yaml@release/add-default-pipelines
    strategy:
      max-parallel: 0
      matrix:
        svc: ${{ fromJson(needs.env.outputs.services) }}
    with:
      app: ${{ needs.env.outputs.app }}
      environment: 'qa'
      services: ${{ matrix.svc }}
    secrets:
      GITOPS_GITHUB_APPLICATION_PRIVATE_KEY: ${{ secrets.GITOPS_GITHUB_APPLICATION_PRIVATE_KEY }}
    needs:
      - env

  deploy:
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/deploy.yaml@release/add-default-pipelines
    strategy:
      max-parallel: 0
      matrix:
        svc: ${{ fromJson(needs.env.outputs.services) }}
    with:
      app: ${{ needs.env.outputs.app }}
      services: ${{ matrix.svc }}
      tag: ${{ needs.fetch-tag.outputs.fetched-tag }}
      environment: 'production'
    secrets:
      GITOPS_GITHUB_APPLICATION_PRIVATE_KEY: ${{ secrets.GITOPS_GITHUB_APPLICATION_PRIVATE_KEY }}
    needs:
      - fetch-tag
      - env

  create-release:
    if: github.event.pull_request.merged == true
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/release-it.yaml@release/add-default-pipelines
    secrets:
      THG_DEPLOY_TOKEN: ${{ secrets.THG_DEPLOY_TOKEN }}
    needs:
      - env
      - fetch-tag
      - deploy

  update-release-branches:
    name: Update release/* and hotfix/* branches with main 🔄
    if: github.event.pull_request.merged == true
    uses: Swiss-Digital-Assets-Institute/.github/.github/workflows/update-release-branches.yaml@release/add-default-pipelines
    secrets:
      THG_DEPLOY_TOKEN: ${{ secrets.THG_DEPLOY_TOKEN }}
    needs:
      - create-release
