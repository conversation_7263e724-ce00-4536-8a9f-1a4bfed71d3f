import request from 'supertest';

import { GrantApplication } from '../src/grant-application/entities/grant-application.entity';
import { HttpStatus, INestApplication, ValidationPipe } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { createTestData, createTestGrantProgram, createTestUsers } from './test.utils';
import { getRepositoryToken } from '@nestjs/typeorm';

import { AuthModule } from '../src/auth/auth.module';
import { BusinessCategory } from '../src/grant-call/enums/business-category.enum';
import { ConfigModule } from '@nestjs/config';
import { GrantCall } from '../src/grant-call/entities/grant-call.entity';
import { GrantCallModule } from '../src/grant-call/grant-call.module';
import { GrantProgram } from '../src/grant-program/entities/grant-program.entity';
import { GrantProgramModule } from '../src/grant-program/grant-program.module';
import { HederaService } from '../src/hedera/hedera.service';
import { JwtScope } from '../src/auth/auth.service';
import { JwtService } from '@nestjs/jwt';
import { KeyManagementService } from '../src/key-management/key-management.service';
import { MailerService } from '@nestjs-modules/mailer';
import { Repository } from 'typeorm';
import { User } from '../src/auth/entities/user.entity';

import { GrantCategory } from '../src/grant-call/enums/grant-category.enum';
import { DatabaseModule } from '../src/database/database.module';
import { WorkflowState } from '../src/workflow/entities/workflow-state.entity';
import { WorkflowTemplate } from '../src/workflow/entities/workflow-template.entity';
import { WorkflowStepDefinition } from '../src/workflow/entities/workflow-step-definition.entity';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from '../src/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../src/auth/guards/roles.guard';

jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

describe('GrantCall (e2e)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let grantProgramRepo: Repository<GrantProgram>;
  let grantCallRepo: Repository<GrantCall>;
  let applicationRepo: Repository<GrantApplication>;
  let jwtService: JwtService;
  let workflowStateRepo: Repository<WorkflowState>;
  let workflowTemplateRepo: Repository<WorkflowTemplate>;
  let workflowStepDefRepo: Repository<WorkflowStepDefinition>;

  let sessionToken: string;
  let grantCallSlug: string; // slug of a grant call created in the test
  let testUsers: User[];
  let grantProgram: GrantProgram;
  const grantProgramSlug = 'test-grant-program';
  const testUser1 = { email: '<EMAIL>', displayName: 'Test User 1', address: '0.0.1780959' };
  const testUser2 = { email: '<EMAIL>', displayName: 'Test User 2', address: '0.0.2780959' };
  const testUser3 = { email: '<EMAIL>', displayName: 'Test User 3', address: '0.0.3780959' };

  beforeAll(async () => {
    // Setup the testing module
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({ isGlobal: true }),
        DatabaseModule,
        AuthModule,
        GrantProgramModule,
        GrantCallModule,
      ],
      providers: [
        {
          provide: APP_GUARD,
          useExisting: JwtAuthGuard,
        },
        JwtAuthGuard,
        {
          provide: APP_GUARD,
          useExisting: RolesGuard,
        },
        RolesGuard,
      ],
    })
      .overrideProvider(MailerService)
      .useValue({ sendMail: jest.fn() })
      .overrideProvider(HederaService)
      .useValue({
        createTopic: jest.fn().mockResolvedValue({ toString: () => 'mock-topic-id' }),
        submitMessage: jest.fn(),
        getMessagesFromTopic: jest.fn(),
      })
      .overrideProvider(KeyManagementService)
      .useValue({
        signMessage: jest.fn().mockResolvedValue(Buffer.from('mocked-signature')),
      })
      .compile();

    app = moduleFixture.createNestApplication();
    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    grantProgramRepo = moduleFixture.get<Repository<GrantProgram>>(getRepositoryToken(GrantProgram));
    grantCallRepo = moduleFixture.get<Repository<GrantCall>>(getRepositoryToken(GrantCall));
    applicationRepo = moduleFixture.get<Repository<GrantApplication>>(getRepositoryToken(GrantApplication));
    jwtService = moduleFixture.get<JwtService>(JwtService);
    workflowStateRepo = moduleFixture.get<Repository<WorkflowState>>(getRepositoryToken(WorkflowState));
    workflowTemplateRepo = moduleFixture.get<Repository<WorkflowTemplate>>(getRepositoryToken(WorkflowTemplate));
    workflowStepDefRepo = moduleFixture.get<Repository<WorkflowStepDefinition>>(
      getRepositoryToken(WorkflowStepDefinition),
    );

    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
    await app.init();

    testUsers = await createTestUsers([testUser1, testUser2, testUser3], userRepository);
    grantProgram = await createTestGrantProgram(grantProgramSlug, testUsers[1], grantProgramRepo);
    // Create session token for the testUser[0]
    sessionToken = jwtService.sign({
      payload: { id: testUsers[0].id, role: testUsers[0].role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
    });
  }, 60000);

  afterAll(async () => {
    await app.close();
  });

  describe('Create Grant Call', () => {
    it('should create a grant call with all required fields', async () => {
      const createGrantCall = {
        grantProgramSlug: grantProgram.grantProgramSlug,
        name: 'Test Grant Call',
        description: 'This is a test grant call',
        businessCategory: BusinessCategory.STARTUP,
        categories: [GrantCategory.SUSTAINABILITY],
        totalGrantAmount: 100000,
        openForApplicationStart: '2024-10-01T00:00:00Z',
        openForApplicationEnd: '2024-10-31T23:59:59Z',
        communityVotingTime1: 604800,
        communityVotingTime2: 259200,
        grantDistribution: [60, 25, 15],
        screeningFormUrl: 'https://forms.clickup.com/12345/f/abc-123',
        dueDiligenceFormUrl: 'https://sharing.clickup.com/12345/f/def-456',
        interviewSchedulingUrl: 'https://calendly.com/your-org/30min',
        townHallSchedulingUrl: 'https://calendly.com/your-org/town-hall-q1',
      };
      const res = await request(app.getHttpServer())
        .post('/grant-call')
        .send(createGrantCall)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toBe(HttpStatus.CREATED);
      expect(res.body).toHaveProperty('grantCallSlug');
      grantCallSlug = res.body.grantCallSlug;
      // Fetch from DB and check
      const call = await grantCallRepo.findOneOrFail({ where: { grantCallSlug } });
      expect(call.name).toBe(createGrantCall.name);
      expect(call.businessCategory).toBe(createGrantCall.businessCategory);
      expect(call.totalGrantAmount).toBe(createGrantCall.totalGrantAmount);
    });

    it('should fail validation if required fields are missing', async () => {
      const invalidDto = { name: '', description: '' };
      const res = await request(app.getHttpServer())
        .post('/grant-call')
        .send(invalidDto)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toBe(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toBeDefined();
    });

    it('should fail if grantDistribution does not sum to 100', async () => {
      const invalidDto = {
        grantProgramSlug: grantProgram.grantProgramSlug,
        name: 'Invalid Grant Call',
        description: 'desc',
        businessCategory: BusinessCategory.STARTUP,
        categories: [GrantCategory.SUSTAINABILITY],
        totalGrantAmount: 100000,
        openForApplicationStart: '2024-10-01T00:00:00Z',
        openForApplicationEnd: '2024-10-31T23:59:59Z',
        communityVotingTime1: 604800,
        communityVotingTime2: 259200,
        grantDistribution: [50, 30, 10], // sums to 90
        screeningFormUrl: 'https://forms.clickup.com/12345/f/abc-123',
        dueDiligenceFormUrl: 'https://sharing.clickup.com/12345/f/def-456',
      };
      const res = await request(app.getHttpServer())
        .post('/grant-call')
        .send(invalidDto)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toBe(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toContain('Sum of distribution percentages must be exactly 100% (calculated: 90.00%)');
    });
  });

  describe('Get Grant Call', () => {
    it('should get the created grant call', async () => {
      const res = await request(app.getHttpServer()).get(`/grant-call/${grantCallSlug}`);
      expect(res.status).toBe(HttpStatus.OK);
      expect(res.body).toHaveProperty('grantCallSlug');
      expect(res.body.grantCallSlug).toBe(grantCallSlug);
      expect(res.body).toHaveProperty('status');
      expect(res.body).toHaveProperty('grantProgram');
    });
  });

  describe('Update Grant Call', () => {
    it('should update the name and description', async () => {
      const updateDto = {
        name: 'Updated Grant Call',
        description: 'Updated description',
        screeningFormUrl: 'https://forms.clickup.com/12345/f/abc-123',
        dueDiligenceFormUrl: 'https://sharing.clickup.com/12345/f/def-456',
      };
      const res = await request(app.getHttpServer())
        .patch(`/grant-call/${grantCallSlug}`)
        .send(updateDto)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toBe(HttpStatus.OK);
      expect(res.body).toHaveProperty('grantCallSlug');
      expect(res.body.grantCallSlug).toContain('test-grant-call');
      // Check DB
      const updated = await grantCallRepo.findOneOrFail({ where: { grantCallSlug: res.body.grantCallSlug } });
      expect(updated.name).toBe(updateDto.name);
      expect(updated.description).toBe(updateDto.description);
    });

    it('should fail if name is empty', async () => {
      const updateDto = { name: '', description: 'desc' };
      const res = await request(app.getHttpServer())
        .patch(`/grant-call/${grantCallSlug}`)
        .send(updateDto)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toBe(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toContain('name should not be empty');
    });
  });

  describe('fetch grant applications', () => {
    beforeEach(async () => {
      await applicationRepo.delete({});
      await grantCallRepo.delete({});
      await grantProgramRepo.delete({});
      await workflowStateRepo.delete({});
      await userRepository.delete({});
    });

    it('should return grant applications for grant call filtered by status', async () => {
      const { call1, grantCallCoordinator1 } = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });

      const sessionToken = jwtService.sign({
        payload: {
          id: grantCallCoordinator1.id,
          role: grantCallCoordinator1.role,
          scope: JwtScope.SESSION_TOKEN,
          emailVerified: true,
        },
      });
      // Fetch all applications for the grant call
      const res = await request(app.getHttpServer())
        .get(`/grant-call/${call1.grantCallSlug}/grant-applications`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toBe(HttpStatus.OK);
      expect(Array.isArray(res.body)).toBe(true);
      expect(res.body.length).toBe(2);
      res.body.forEach((app) => expect(app.grantCallSlug).toBe(call1.grantCallSlug));
    });

    it('should return grant applications sorted by creation date', async () => {
      const { call1, grantCallCoordinator1 } = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });

      const sessionToken = jwtService.sign({
        payload: {
          id: grantCallCoordinator1.id,
          role: grantCallCoordinator1.role,
          scope: JwtScope.SESSION_TOKEN,
          emailVerified: true,
        },
      });
      // Fetch all applications in ascending order
      let res = await request(app.getHttpServer())
        .get(`/grant-call/${call1.grantCallSlug}/grant-applications?sort=createdAt&direction=ASC`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toBe(HttpStatus.OK);
      expect(Array.isArray(res.body)).toBe(true);
      let applications = res.body;
      let sorted = applications
        .slice()
        .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      expect(sorted).toEqual(applications);
      // Fetch all applications in descending order
      res = await request(app.getHttpServer())
        .get(`/grant-call/${call1.grantCallSlug}/grant-applications?sort=createdAt&direction=DESC`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toBe(HttpStatus.OK);
      expect(Array.isArray(res.body)).toBe(true);
      applications = res.body;
      sorted = applications.slice().sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      expect(sorted).toEqual(applications);
    });
  });
});
