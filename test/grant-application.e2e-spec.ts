import { GrantApplication } from '../src/grant-application/entities/grant-application.entity';
import { HttpStatus, INestApplication, ValidationPipe } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { createTestData, createTestGrantCallOpenForApplications } from './test.utils';
import { getRepositoryToken } from '@nestjs/typeorm';

import { AuthModule } from '../src/auth/auth.module';
import { ConfigModule } from '@nestjs/config';
import { GrantApplicationModule } from '../src/grant-application/grant-application.module';
import { GrantCall } from '../src/grant-call/entities/grant-call.entity';
import { GrantCallModule } from '../src/grant-call/grant-call.module';
import { GrantProgram } from '../src/grant-program/entities/grant-program.entity';
import { GrantProgramModule } from '../src/grant-program/grant-program.module';
import { HederaService } from '../src/hedera/hedera.service';
import { JwtService } from '@nestjs/jwt';
import { KeyManagementService } from '../src/key-management/key-management.service';
import { Repository } from 'typeorm';
import { User } from '../src/auth/entities/user.entity';
import { Role } from '../src/auth/role.enum';
import { WorkflowState } from '../src/workflow/entities/workflow-state.entity';
import { WorkflowStepDefinition } from '../src/workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../src/workflow/entities/workflow-template.entity';
import request from 'supertest';
import { GrantCategory } from '../src/grant-call/enums/grant-category.enum';
import { BusinessCategory } from '../src/grant-call/enums/business-category.enum';
import { JwtScope } from '../src/auth/auth.service';
import { CreateGrantApplicationDto, FindGrantApplicationResponseDto } from '../src/grant-application/dto';
import { WorkflowStatus } from '../src/workflow/enums/workflow-status.enum';
import { StageCode } from '../src/workflow/enums/stage-code.enum';
import { DatabaseModule } from '../src/database/database.module';
import { BalanceAlertService } from '../src/balance-alerting/balance-alert.service';
import { NotificationsService } from '../src/notifications/notifications.service';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from '../src/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../src/auth/guards/roles.guard';

jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

describe('GrantApplication (e2e)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let grantProgramRepo: Repository<GrantProgram>;
  let grantCallRepo: Repository<GrantCall>;
  let applicationRepo: Repository<GrantApplication>;
  let jwtService: JwtService;
  let sessionToken: string;
  let testUsers: User[];
  let grantProgram: GrantProgram;
  let grantCall: GrantCall;
  let workflowTemplateRepo: Repository<WorkflowTemplate>;
  let workflowStepDefRepo: Repository<WorkflowStepDefinition>;
  let workflowStateRepo: Repository<WorkflowState>;

  const testUser1 = { email: '<EMAIL>', displayName: 'Test User 1', address: '0.0.1780959' };
  const testUser2 = { email: '<EMAIL>', displayName: 'Test User 2', address: '0.0.2780959' };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({ isGlobal: true }),
        DatabaseModule,
        AuthModule,
        GrantProgramModule,
        GrantCallModule,
        GrantApplicationModule,
      ],
      providers: [
        {
          provide: APP_GUARD,
          useExisting: JwtAuthGuard,
        },
        JwtAuthGuard,
        {
          provide: APP_GUARD,
          useExisting: RolesGuard,
        },
        RolesGuard,
      ],
    })
      .overrideProvider(NotificationsService)
      .useValue({
        applicationCreated: jest.fn(),
        applicationTransitioned: jest.fn(),
        applicationStatusChange: jest.fn(),
      })
      .overrideProvider(HederaService)
      .useValue({
        createTopicWithRetry: jest.fn().mockResolvedValue({ toString: () => 'mock-topic-id' }),
        submitMessageWithRetry: jest.fn(),
        getMessagesFromTopic: jest.fn(),
      })
      .overrideProvider(KeyManagementService)
      .useValue({ signMessage: jest.fn().mockResolvedValue(Buffer.from('mocked-signature')) })
      .overrideProvider(BalanceAlertService)
      .useValue({ ensureSufficientSystemOperatorBalance: jest.fn().mockResolvedValue(undefined) })
      .compile();

    app = moduleFixture.createNestApplication();
    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    grantProgramRepo = moduleFixture.get<Repository<GrantProgram>>(getRepositoryToken(GrantProgram));
    grantCallRepo = moduleFixture.get<Repository<GrantCall>>(getRepositoryToken(GrantCall));
    applicationRepo = moduleFixture.get<Repository<GrantApplication>>(getRepositoryToken(GrantApplication));
    jwtService = moduleFixture.get<JwtService>(JwtService);
    workflowTemplateRepo = moduleFixture.get<Repository<WorkflowTemplate>>(getRepositoryToken(WorkflowTemplate));
    workflowStepDefRepo = moduleFixture.get<Repository<WorkflowStepDefinition>>(
      getRepositoryToken(WorkflowStepDefinition),
    );
    workflowStateRepo = moduleFixture.get<Repository<WorkflowState>>(getRepositoryToken(WorkflowState));

    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
    await app.init();
    testUsers = await Promise.all([
      userRepository.save(
        userRepository.create({
          email: testUser1.email,
          displayName: testUser1.displayName,
          addresses: [testUser1.address],
          emailVerified: true,
        }),
      ),
      userRepository.save(
        userRepository.create({
          email: testUser2.email,
          displayName: testUser2.displayName,
          addresses: [testUser2.address],
          emailVerified: true,
        }),
      ),
    ]);
    grantProgram = await grantProgramRepo.save(
      grantProgramRepo.create({
        grantProgramSlug: 'test-grant-program',
        name: 'Test Grant Program',
        description: 'This is a test grant program',
        scope: 'Test Scope',
        budget: 10000,
        grantorPublicProfileName: 'Test Grantor',
        grantorLogoURL: 'https://example.com/logo.jpg',
        grantProgramCoordinator: testUsers[0],
        grantorDescription: 'Test Grantor Description',
        grantorWebsite: 'https://example.com',
      }),
    );
    grantCall = await grantCallRepo.save(
      grantCallRepo.create({
        grantCallSlug: 'test-grant-call',
        grantProgram: grantProgram,
        name: 'Test Grant Call',
        description: 'This is a test grant call',
        businessCategory: BusinessCategory.STARTUP,
        categories: [GrantCategory.SUSTAINABILITY],
        totalGrantAmount: 10000,
        createdById: testUsers[0].id,
        createdBy: testUsers[0],
      }),
    );
    sessionToken = jwtService.sign({
      payload: { id: testUsers[0].id, role: testUsers[0].role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
    });
  }, 60000);

  afterAll(async () => {
    await app.close();
  });

  describe('create grant application', () => {
    it('should create grant application', async () => {
      const grantCall = await createTestGrantCallOpenForApplications(grantCallRepo);
      const creationDto: CreateGrantApplicationDto = {
        grantCallSlug: grantCall.grantCallSlug,
        title: 'My Grant Application',
        description: 'In this project we will build a rocket to the moon.',
        companyName: 'Example Company',
        companyCountry: 'United States',
        categories: [GrantCategory.SUSTAINABILITY],
        contactFullName: 'John Doe',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '+14 123 456 78 90',
      };

      const res = await request(app.getHttpServer())
        .post('/grant-application')
        .send(creationDto)
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(res.status).toEqual(HttpStatus.CREATED);
      expect(res.body).toBeDefined();
      expect(res.body.grantApplicationId).toBeDefined();

      const application = await applicationRepo.findOneOrFail({
        where: { id: res.body.grantApplicationId },
        relations: { grantCall: true },
      });

      expect(application.title).toBe(creationDto.title);
      expect(application.companyName).toBe(creationDto.companyName);
      expect(application.contactEmail).toBe(creationDto.contactEmail);
      expect(application.createdAt).toBeDefined();
      expect(application.grantCall.id).toEqual(grantCall.id);
    });

    it('should fail creating grant application with non-existent grant call', async () => {
      const creationDto: CreateGrantApplicationDto = {
        grantCallSlug: 'non-existent-grant-call',
        title: 'My Grant Application',
        description: 'In this project we will build a rocket to the moon.',
        companyName: 'Example Company',
        companyCountry: 'United States',
        categories: [GrantCategory.SUSTAINABILITY],
        contactFullName: 'John Doe',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '+14 123 456 78 90',
      };

      jest.spyOn(app.get(HederaService), 'submitMessageWithRetry').mockResolvedValueOnce(undefined);

      const res = await request(app.getHttpServer())
        .post('/grant-application')
        .send(creationDto)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Moving to next stage' });

      expect(res.status).toEqual(HttpStatus.NOT_FOUND);
      expect(res.body.message).toEqual(`Grant Call with slug "${creationDto.grantCallSlug}" not found.`);
    });

    it('should fail creating grant application if grant call is closed', async () => {
      await grantCallRepo.save(grantCall);

      const creationDto: CreateGrantApplicationDto = {
        grantCallSlug: grantCall.grantCallSlug,
        title: 'My Grant Application',
        description: 'In this project we will build a rocket to the moon.',
        companyName: 'Example Company',
        companyCountry: 'United States',
        categories: [GrantCategory.SUSTAINABILITY],
        contactFullName: 'John Doe',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '+14 123 456 78 90',
      };

      const res = await request(app.getHttpServer())
        .post('/grant-application')
        .send(creationDto)
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual(
        `Cannot create application: Grant Call "${grantCall.name}" is not open for applications.`,
      );
    });
  });

  describe('fetch grant applications', () => {
    beforeEach(async () => {
      await applicationRepo.delete({});
      await grantCallRepo.delete({});
      await grantProgramRepo.delete({});
      await workflowStateRepo.delete({});
    });

    it('should return correct grant application information', async () => {
      const { call1Application1 } = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });

      const res = await request(app.getHttpServer())
        .get(`/grant-application/${call1Application1.id}`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);

      const applicationDto: FindGrantApplicationResponseDto = res.body;
      expect(applicationDto.title).toEqual(call1Application1.title);
      expect(applicationDto.companyName).toEqual(call1Application1.companyName);
      expect(applicationDto.companyCountry).toEqual(call1Application1.companyCountry);
      expect(applicationDto.grantCallSlug).toEqual(call1Application1.grantCall.grantCallSlug);

      // Retrieve grantCall of the application
      const retrievedGrantCall = await grantCallRepo.findOne({
        where: { grantCallSlug: applicationDto.grantCallSlug },
        relations: { grantProgram: true },
      });

      expect(applicationDto.grantCallSlug).toEqual(retrievedGrantCall.grantCallSlug);
      expect(applicationDto.grantProgramSlug).toEqual(retrievedGrantCall.grantProgram.grantProgramSlug);
    });

    it('should fail fetching grant application if it does not exist', async () => {
      const res = await request(app.getHttpServer())
        .get(`/grant-application/123456`) // Non-existent application ID
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(res.status).toEqual(HttpStatus.NOT_FOUND);
      expect(res.body.message).toEqual('Grant Application with ID 123456 not found.');
    });

    it('should return all grant applications', async () => {
      const testData = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });

      const res = await request(app.getHttpServer())
        .get(`/grant-application`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);

      const applications: FindGrantApplicationResponseDto[] = res.body;
      // Check if all applications are returned
      expect(applications).toHaveLength(4);
      const applicationIds = applications.map((app) => app.id);
      expect(applicationIds).toContain(testData.call1Application1.id);
      expect(applicationIds).toContain(testData.call1Application2.id);
      expect(applicationIds).toContain(testData.call2Application1.id);
      expect(applicationIds).toContain(testData.call2Application2.id);
      // For the first application check some of the DTO values
      const app1 = applications.find((a) => a.id === testData.call1Application1.id);
      expect(app1.title).toEqual(testData.call1Application1.title);
      expect(app1.contactEmail).toEqual(testData.call1Application1.contactEmail);
      expect(app1.grantCallSlug).toEqual(testData.call1.grantCallSlug);
      // Check application stage DTOs
      const call1 = await grantCallRepo.findOne({
        relations: { grantProgram: true },
        where: { grantCallSlug: app1.grantCallSlug },
      });

      //Check grant call DTO
      expect(app1.grantCallSlug).toEqual(call1.grantCallSlug);
      // Check grant program DTO
      expect(app1.grantProgramSlug).toEqual(call1.grantProgram.grantProgramSlug);
      // Check the creator
      expect(app1.creator).toEqual({
        email: testData.call1Application1.createdBy.email,
        id: testData.call1Application1.createdBy.id,
        displayName: testData.call1Application1.createdBy.displayName,
      });
    });

    it('should return grant applications filtered by status codes', async () => {
      const { call1Application1 } = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });

      call1Application1.workflowState.status = WorkflowStatus.REJECTED;
      await applicationRepo.save(call1Application1);

      const res = await request(app.getHttpServer())
        .get(`/grant-application?statusCodes=${WorkflowStatus.IN_PROGRESS}`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      const applications: FindGrantApplicationResponseDto[] = res.body;
      // Check if all applications are returned
      expect(applications).toHaveLength(3);
      expect(applications.map((app) => app.id)).not.toContain(call1Application1.id);
    });

    it('should return grant applications filtered by my applications', async () => {
      const testData = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });

      const jwt = jwtService.sign({
        payload: {
          id: testData.call1Application1.createdBy.id,
          role: testData.call1Application1.createdBy.role,
          scope: JwtScope.SESSION_TOKEN,
          emailVerified: true,
        },
      });

      const res = await request(app.getHttpServer())
        .get(`/grant-application?myApplications=true`)
        .set('Authorization', `Bearer ${jwt}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body).toHaveLength(2);
      const applicationIds = res.body.map((app) => app.id);
      expect(applicationIds).toContain(testData.call1Application1.id);
      expect(applicationIds).toContain(testData.call2Application1.id);
    });

    it('should return grant applications filtered by grant call', async () => {
      const testData = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });

      // Fetch applications for grant call 2
      const res = await request(app.getHttpServer())
        .get(`/grant-application?grantCallSlug=${testData.call2.grantCallSlug}`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body).toHaveLength(2);
      const applicationIds = res.body.map((app) => app.id);
      expect(applicationIds).toContain(testData.call2Application1.id);
      expect(applicationIds).toContain(testData.call2Application2.id);
    });

    it('should return grant applications sorted by creation date', async () => {
      await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });

      // Fetch all applications in ascending order
      let res = await request(app.getHttpServer()).get(`/grant-application?sort=createdAt&direction=ASC`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body).toHaveLength(4);
      let applications = res.body;
      let sorted = applications
        .slice()
        .sort(
          (a: FindGrantApplicationResponseDto, b: FindGrantApplicationResponseDto) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
        );
      expect(sorted).toEqual(applications);

      // Fetch all applications in descending order
      res = await request(app.getHttpServer())
        .get(`/grant-application?sort=createdAt&direction=DESC`)
        .set('Authorization', `Bearer ${sessionToken}`);
      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body).toHaveLength(4);
      applications = res.body;
      sorted = applications
        .slice()
        .sort(
          (a: FindGrantApplicationResponseDto, b: FindGrantApplicationResponseDto) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        );
      expect(sorted).toEqual(applications);
    });
  });

  describe('move application to next stage', () => {
    it('should change status to APPROVED if last stage is passed', async () => {
      const { call1Application1, programCoordinator } = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });

      const jwt = jwtService.sign({
        payload: {
          id: programCoordinator.id,
          role: programCoordinator.role,
          scope: JwtScope.SESSION_TOKEN,
          emailVerified: true,
        },
      });

      // Move through all stages until final qualification
      const stages = [
        StageCode.GA_SCREENING,
        StageCode.GA_QUALIFICATION,
        StageCode.GA_INTERVIEW,
        StageCode.GA_DUE_DILIGENCE,
        StageCode.GA_TOWN_HALL,
        StageCode.GA_FINAL_QUALIFICATION,
      ];

      for (let i = 0; i < stages.length - 1; i++) {
        const res = await request(app.getHttpServer())
          .post(`/grant-application/${call1Application1.id}/transition`)
          .set('Authorization', `Bearer ${jwt}`)
          .send({ reason: 'Moving to next stage' });

        expect(res.body.stepDefinitionCode).toEqual(stages[i + 1]);
        expect(res.body.status).toEqual(WorkflowStatus.IN_PROGRESS);
      }

      // Verify final state
      const updatedApplication = await applicationRepo.findOneOrFail({
        where: { id: call1Application1.id },
        relations: { workflowState: { currentStepDefinition: true } },
      });

      expect(updatedApplication.workflowState.currentStepDefinition.code).toEqual(StageCode.GA_FINAL_QUALIFICATION);
      expect(updatedApplication.workflowState.status).toEqual(WorkflowStatus.IN_PROGRESS);
      expect(updatedApplication.workflowState.currentStepTransitionedAt).toBeDefined();
      expect(updatedApplication.workflowState.currentStepEndsAt).toBeNull();
    });

    it('should fail moving the application if user is not grant call member', async () => {
      const { call1Application1 } = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });

      // Try to move stage with non-member user
      const nonMemberToken = jwtService.sign({
        payload: { id: testUsers[1].id, role: testUsers[1].role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
      });

      const res = await request(app.getHttpServer())
        .post(`/grant-application/${call1Application1.id}/transition`)
        .set('Authorization', `Bearer ${nonMemberToken}`)
        .send({ reason: 'Moving to next stage' });

      expect(res.status).toEqual(HttpStatus.FORBIDDEN);
      expect(res.body.message).toContain('Forbidden resource');
    });
  });

  describe('withdraw application', () => {
    it('should withdraw application when invoked by creator', async () => {
      // Setup
      const { call1Application1 } = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });
      expect(call1Application1.workflowState.status).toEqual(WorkflowStatus.IN_PROGRESS);

      const jwt = jwtService.sign({
        payload: {
          id: call1Application1.createdBy.id,
          role: call1Application1.createdBy.role,
          scope: JwtScope.SESSION_TOKEN,
          emailVerified: true,
        },
      });

      const res = await request(app.getHttpServer())
        .patch(`/grant-application/${call1Application1.id}/withdraw`)
        .set('Authorization', `Bearer ${jwt}`)
        .send({ reason: 'Withdraw application' });

      expect(res.status).toEqual(HttpStatus.OK);

      const updatedApplication = await applicationRepo.findOneOrFail({
        where: { id: call1Application1.id },
        relations: { workflowState: true },
      });
      expect(updatedApplication.workflowState.status).toEqual(WorkflowStatus.WITHDRAWN);
    });

    it('should fail withdraw when application is not open', async () => {
      jest.spyOn(app.get(HederaService), 'submitMessageWithRetry').mockResolvedValueOnce(undefined);
      // Setup
      const { call1Application1 } = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });

      const jwt = jwtService.sign({
        payload: {
          id: call1Application1.createdBy.id,
          role: call1Application1.createdBy.role,
          scope: JwtScope.SESSION_TOKEN,
          emailVerified: true,
        },
      });

      call1Application1.workflowState.status = WorkflowStatus.REJECTED;
      await applicationRepo.save(call1Application1);
      let res = await request(app.getHttpServer())
        .patch(`/grant-application/${call1Application1.id}/withdraw`)
        .set('Authorization', `Bearer ${jwt}`)
        .send({ reason: 'Withdraw application' });
      expect(res.status).toEqual(HttpStatus.UNPROCESSABLE_ENTITY);
      expect(res.body.message).toEqual("Cannot change workflow status from existing terminal status 'REJECTED'.");

      call1Application1.workflowState.status = WorkflowStatus.WITHDRAWN;
      await applicationRepo.save(call1Application1);
      res = await request(app.getHttpServer())
        .patch(`/grant-application/${call1Application1.id}/withdraw`)
        .set('Authorization', `Bearer ${jwt}`)
        .send({ reason: 'Withdraw application' });
      expect(res.status).toEqual(HttpStatus.UNPROCESSABLE_ENTITY);
      expect(res.body.message).toEqual("Cannot change workflow status from existing terminal status 'WITHDRAWN'.");
    });

    it('should fail withdraw when user is not application creator', async () => {
      const { call1Application1 } = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });
      expect(call1Application1.workflowState.status).toEqual(WorkflowStatus.IN_PROGRESS);

      const res = await request(app.getHttpServer())
        .patch(`/grant-application/${call1Application1.id}/withdraw`)
        .set('Authorization', `Bearer ${sessionToken}`)
        .send({ reason: 'Withdraw application' });
      expect(res.status).toEqual(HttpStatus.FORBIDDEN);
      expect(res.body.message).toEqual('You do not have permission to withdraw this application.');
    });
  });

  describe('/grant-application/{applicationId}/pitch-file (PATCH)', () => {
    let grantApplication: GrantApplication;
    let jwtToken: string;
    const url = 'https://example.com/pitch.pdf';

    beforeAll(async () => {
      const { call1Application1, grantCallCoordinator1 } = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        applicationRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });
      grantApplication = call1Application1;

      jwtToken = jwtService.sign({
        payload: {
          id: grantCallCoordinator1.id,
          role: Role.COORDINATOR,
          scope: JwtScope.SESSION_TOKEN,
          emailVerified: true,
        },
      });
    });

    it('should update pitch file url', async () => {
      const updateResponse = await request(app.getHttpServer())
        .patch(`/grant-application/${grantApplication.id}/pitch-file`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ pitchFileUrl: url });

      expect(updateResponse.status).toEqual(HttpStatus.NO_CONTENT);

      const getResponse = await request(app.getHttpServer())
        .get(`/grant-application/${grantApplication.id}`)
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(getResponse.status).toEqual(HttpStatus.OK);
      expect(getResponse.body.pitchFileUrl).toEqual(url);
    });

    it('should clear the pitch file url when set to null', async () => {
      // first set it
      await request(app.getHttpServer())
        .patch(`/grant-application/${grantApplication.id}/pitch-file`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ pitchFileUrl: url });

      // then clear it
      const updateResponse = await request(app.getHttpServer())
        .patch(`/grant-application/${grantApplication.id}/pitch-file`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ pitchFileUrl: null });

      expect(updateResponse.status).toEqual(HttpStatus.NO_CONTENT);

      const getResponse = await request(app.getHttpServer())
        .get(`/grant-application/${grantApplication.id}`)
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(getResponse.status).toEqual(HttpStatus.OK);
      expect(getResponse.body.pitchFileUrl).toBeNull();
    });

    it('should throw NOT_FOUND when grant application not found', async () => {
      const updateResponse = await request(app.getHttpServer())
        .patch(`/grant-application/999999/pitch-file`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ pitchFileUrl: 'https://example.com' });

      expect(updateResponse.status).toEqual(HttpStatus.NOT_FOUND);
      expect(updateResponse.body.message).toEqual('Grant application not found');
    });

    it('should forbid non‑coordinator users', async () => {
      const jwtToken = jwtService.sign({
        payload: {
          id: grantApplication.createdBy.id,
          role: Role.COMMUNITY_MEMBER,
          scope: JwtScope.SESSION_TOKEN,
          emailVerified: true,
        },
      });

      const updateResponse = await request(app.getHttpServer())
        .patch(`/grant-application/${grantApplication.id}/pitch-file`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ pitchFileUrl: 'https://example.com' });

      expect(updateResponse.status).toEqual(HttpStatus.FORBIDDEN);
    });

    it('should return BAD_REQUEST for invalid URL', async () => {
      const updateResponse = await request(app.getHttpServer())
        .patch(`/grant-application/${grantApplication.id}/pitch-file`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ pitchFileUrl: 'not‑a‑url' });

      expect(updateResponse.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(updateResponse.body.message).toEqual(['Must be a valid http(s) URL']);

      const updateResponseTwo = await request(app.getHttpServer())
        .patch(`/grant-application/${grantApplication.id}/pitch-file`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ pitchFileUrl: '' });

      expect(updateResponseTwo.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(updateResponseTwo.body.message).toEqual(['Must be a valid http(s) URL']);
    });
  });
});
