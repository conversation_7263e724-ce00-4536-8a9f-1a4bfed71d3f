import { WorkflowState } from '../src/workflow/entities/workflow-state.entity';
import { ISendMailOptions, MailerService } from '@nestjs-modules/mailer';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { SentMessageInfo } from 'nodemailer';
import request from 'supertest';
import { Repository } from 'typeorm';
import { AuthModule } from '../src/auth/auth.module';
import { User } from '../src/auth/entities/user.entity';
import { GrantApplication } from '../src/grant-application/entities/grant-application.entity';
import { GrantApplicationModule } from '../src/grant-application/grant-application.module';
import { GrantCall } from '../src/grant-call/entities/grant-call.entity';
import { GrantCallModule } from '../src/grant-call/grant-call.module';
import { GrantProgram } from '../src/grant-program/entities/grant-program.entity';
import { GrantProgramModule } from '../src/grant-program/grant-program.module';
import { createTestData, generateSessionToken, generateSessionTokenEmailNotVerified, TestData } from './test.utils';
import { HederaService } from '../src/hedera/hedera.service';
import { KeyManagementService } from '../src/key-management/key-management.service';
import { WorkflowStepDefinition } from '../src/workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../src/workflow/entities/workflow-template.entity';
import { DatabaseModule } from '../src/database/database.module';
import { MailModule } from '../src/notifications/mail/mail.module';
import {
  AccountRecoveryContext,
  ApplicationApprovedContext,
  ApplicationCreatedContext,
  ApplicationRejectedContext,
  ApplicationTransitionedContext,
  ApplicationWithdrawnContext,
  EmailVerificationContext,
} from '../src/notifications/mail/mail.types';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from '../src/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../src/auth/guards/roles.guard';

jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

// Mock the MailerService such that we can retrieve the content of sent emails.
class MockMailerService extends MailerService {
  newGrantApplicationEmail: ApplicationCreatedContext;
  applicationCreatedEmails: ApplicationCreatedContext[] = [];
  recoveryEmail: AccountRecoveryContext;
  otpEmail: EmailVerificationContext;
  applicationApprovedEmails: ApplicationApprovedContext[] = [];
  applicationMovedEmails: ApplicationTransitionedContext[] = [];
  applicationRejectedEmails: ApplicationRejectedContext[] = [];
  applicationWithdrawnEmails: ApplicationWithdrawnContext[] = [];

  async sendMail(options: ISendMailOptions): Promise<SentMessageInfo> {
    if (options.template === './new-grant-application.template.hbs') {
      this.newGrantApplicationEmail = options.context as any;
    } else if (options.template === './user/account-recovery.hbs') {
      this.recoveryEmail = options.context as any;
    } else if (options.template === './user/user-verification.hbs') {
      this.otpEmail = options.context as any;
    } else if (options.template === './grant-application/created.hbs') {
      this.applicationCreatedEmails.push(options.context as any);
    } else if (options.template === './grant-application/approved.hbs') {
      this.applicationApprovedEmails.push(options.context as any);
    } else if (options.template === './grant-application/moved.hbs') {
      this.applicationMovedEmails.push(options.context as any);
    } else if (options.template === './grant-application/rejected.hbs') {
      this.applicationRejectedEmails.push(options.context as any);
    } else if (options.template === './grant-application/withdrawn.hbs') {
      this.applicationWithdrawnEmails.push(options.context as any);
    } else {
      throw new Error(`(Mock service) Unknown template: ${options.template}`);
    }
    return null;
  }

  clearEmails() {
    this.newGrantApplicationEmail = undefined;
    this.recoveryEmail = undefined;
    this.otpEmail = undefined;
    this.applicationCreatedEmails = [];
    this.applicationApprovedEmails = [];
    this.applicationMovedEmails = [];
    this.applicationRejectedEmails = [];
    this.applicationWithdrawnEmails = [];
  }
}

describe('Mail service (e2e)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let grantProgramRepo: Repository<GrantProgram>;
  let grantCallRepo: Repository<GrantCall>;
  let applicationRepo: Repository<GrantApplication>;
  let jwtService: JwtService;
  let mockMailerService: MockMailerService;
  let testData: TestData;
  let workflowTemplateRepo: Repository<WorkflowTemplate>;
  let workflowStepDefRepo: Repository<WorkflowStepDefinition>;
  let workflowStateRepo: Repository<WorkflowState>;

  beforeAll(async () => {
    // Setup the testing module
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({ isGlobal: true }),
        DatabaseModule,
        AuthModule,
        GrantProgramModule,
        GrantCallModule,
        GrantApplicationModule,
        MailModule,
      ],
      providers: [
        {
          provide: APP_GUARD,
          useExisting: JwtAuthGuard,
        },
        JwtAuthGuard,
        {
          provide: APP_GUARD,
          useExisting: RolesGuard,
        },
        RolesGuard,
      ],
    })
      .overrideProvider(MailerService)
      .useClass(MockMailerService)
      .overrideProvider(HederaService)
      .useValue({
        createTopicWithRetry: jest.fn().mockResolvedValue({ toString: () => 'mock-topic-id' }),
        submitMessageWithRetry: jest.fn(),
        getMessagesFromTopic: jest.fn(),
      })
      .overrideProvider(KeyManagementService)
      .useValue({
        signMessage: jest.fn().mockResolvedValue(Buffer.from('mocked-signature')),
      })
      .compile();
    app = moduleFixture.createNestApplication();
    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    grantProgramRepo = moduleFixture.get<Repository<GrantProgram>>(getRepositoryToken(GrantProgram));
    grantCallRepo = moduleFixture.get<Repository<GrantCall>>(getRepositoryToken(GrantCall));
    applicationRepo = moduleFixture.get<Repository<GrantApplication>>(getRepositoryToken(GrantApplication));
    jwtService = moduleFixture.get<JwtService>(JwtService);
    mockMailerService = moduleFixture.get<MockMailerService>(MailerService);
    workflowTemplateRepo = moduleFixture.get<Repository<WorkflowTemplate>>(getRepositoryToken(WorkflowTemplate));
    workflowStepDefRepo = moduleFixture.get<Repository<WorkflowStepDefinition>>(
      getRepositoryToken(WorkflowStepDefinition),
    );
    workflowStateRepo = moduleFixture.get<Repository<WorkflowState>>(getRepositoryToken(WorkflowState));

    // Enable validation pipe because the one in main.ts is not used in tests
    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    await app.init();
  }, 100000);

  beforeEach(async () => {
    testData = await createTestData({
      userRepository,
      grantProgramRepo,
      grantCallRepo,
      applicationRepo,
      workflowTemplateRepo,
      workflowStepDefRepo,
      workflowStateRepo,
    });
    mockMailerService.clearEmails();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should send OTP email correctly', async () => {
    // Authenticate the user and get the JWT token
    // Change the emailVerified flag to false on grantCallCoordinator1
    await userRepository.update(testData.grantCallCoordinator1.id, { emailVerified: false });
    const token = generateSessionTokenEmailNotVerified(jwtService, testData.grantCallCoordinator1);

    // Send OTP request
    const response = await request(app.getHttpServer())
      .post('/auth/send-otp')
      .set('Authorization', `Bearer ${token}`)
      .send();

    expect(response.status).toBe(201);
    expect(response.body).toEqual({
      success: true,
      message: 'OTP sent to the user.',
    });

    // Verify the email was sent with the correct context
    expect(mockMailerService.otpEmail.name).toBe(testData.grantCallCoordinator1.displayName);
    expect(mockMailerService.otpEmail.otp).toMatch(/^\d{6}$/);
  });

  it('should send recovery email correctly', async () => {
    // Authenticate the user and get the JWT token
    const token = generateSessionToken(jwtService, testData.grantCallCoordinator1);

    // Send recovery request
    const response = await request(app.getHttpServer())
      .post('/auth/recover-account')
      .set('Authorization', `Bearer ${token}`)
      .send({ email: testData.grantCallCoordinator1.email });

    expect(response.status).toBe(201);
    expect(response.body.success).toBeTruthy();

    // Verify the email was sent with the correct context
    expect(mockMailerService.recoveryEmail.name).toBe(testData.grantCallCoordinator1.displayName);
    expect(mockMailerService.recoveryEmail.token).not.toBe('');
  });
});
