import { GrantApplication } from '../src/grant-application/entities/grant-application.entity';
import { User } from '../src/auth/entities/user.entity';
import { Role } from '../src/auth/role.enum';

import { BusinessCategory } from '../src/grant-call/enums/business-category.enum';
import { GrantCall } from '../src/grant-call/entities/grant-call.entity';
import { GrantProgram } from '../src/grant-program/entities/grant-program.entity';
import { JwtScope } from '../src/auth/auth.service';
import { JwtService } from '@nestjs/jwt';
import { Repository } from 'typeorm/repository/Repository';
import { StageCode } from '../src/workflow/enums/stage-code.enum';
import { WorkflowEntityType } from '../src/workflow/enums/workflow-entity-type.enum';
import { WorkflowState } from '../src/workflow/entities/workflow-state.entity';
import { WorkflowStatus } from '../src/workflow/enums/workflow-status.enum';
import { WorkflowStepDefinition } from '../src/workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../src/workflow/entities/workflow-template.entity';
import { GrantCategory } from '../src/grant-call/enums/grant-category.enum';

export type TestUser = {
  email: string;
  displayName: string;
  address: string;
};

export function createTestUsers(testUsers: TestUser[], userRepository: Repository<User>): Promise<User[]> {
  return Promise.all(
    testUsers.map(async (testUser) => {
      const user = userRepository.create({
        email: testUser.email,
        displayName: testUser.displayName,
        addresses: [testUser.address],
        emailVerified: true,
        role: Role.COORDINATOR,
      });
      return userRepository.save(user);
    }),
  );
}

export function createTestUser(user: TestUser, userRepository: Repository<User>): Promise<User> {
  const newUser = userRepository.create({
    email: user.email,
    displayName: user.displayName,
    addresses: [user.address],
    emailVerified: true,
    role: Role.COORDINATOR,
  });
  return userRepository.save(newUser);
}

export async function createTestGrantProgram(
  slug: string,
  grantProgramCoordinator: User,
  grantProgramRepo: Repository<GrantProgram>,
): Promise<GrantProgram> {
  // Create workflow state first
  const entityManager = grantProgramRepo.manager;
  const wfStateRepo = entityManager.getRepository(WorkflowState);
  const stepDefRepo = entityManager.getRepository(WorkflowStepDefinition);
  const wfTemplateRepo = entityManager.getRepository(WorkflowTemplate);

  // Find default step definition for GP_OPEN
  const defaultOpenStepDef = await stepDefRepo.findOne({ where: { code: StageCode.GP_OPEN } });

  // Find workflow template for PROGRAM entity type
  const workflowTemplate = await wfTemplateRepo.findOneBy({
    entityType: WorkflowEntityType.PROGRAM,
  });

  // Create initial workflow state
  const initialWorkflowState = wfStateRepo.create({
    workflowTemplate,
    currentStepDefinitionId: defaultOpenStepDef.id,
    currentStepDefinition: defaultOpenStepDef,
    currentStepTransitionedAt: new Date(),
    currentStepEndsAt: null,
    status: WorkflowStatus.IN_PROGRESS,
  });

  await wfStateRepo.save(initialWorkflowState);

  // Create the grant program with the workflow state
  const program = grantProgramRepo.create({
    grantProgramSlug: slug,
    name: 'Test Grant Program',
    description: 'This is a test grant program',
    scope: 'Test Scope',
    budget: 10000,
    grantorPublicProfileName: 'Test Grantor',
    grantorLogoURL: 'https://example.com/logo.jpg',
    grantProgramCoordinator: grantProgramCoordinator,
    grantorDescription: 'Test Grantor Description',
    grantorWebsite: 'example.com',
    workflowState: initialWorkflowState,
  });

  return grantProgramRepo.save(program);
}

/**
 * Creates and stores a grant call instance.
 *
 * @param slug the slug of the grant call
 * @param grantProgram the grant program to which the grant call belongs
 * @param creatorUser the user who created the grant call
 * @param grantCallRepo the repository to save the grant call
 * @returns the created grant call
 */
export async function createTestGrantCall(
  slug: string,
  grantProgram: GrantProgram,
  creatorUser: User,
  grantCallRepo: Repository<GrantCall>,
): Promise<GrantCall> {
  const entityManager = grantCallRepo.manager;
  const wfStateRepo = entityManager.getRepository(WorkflowState);
  const stepDefRepo = entityManager.getRepository(WorkflowStepDefinition);
  const wfTemplateRepo = entityManager.getRepository(WorkflowTemplate);

  // Find default step definition for GP_OPEN
  const defaultOpenStepDef = await stepDefRepo.findOne({ where: { code: StageCode.GC_CLOSED } });

  // Find workflow template for PROGRAM entity type
  const workflowTemplate = await wfTemplateRepo.findOneBy({
    entityType: WorkflowEntityType.CALL,
  });

  // Create initial workflow state
  const initialWorkflowState = wfStateRepo.create({
    workflowTemplate,
    currentStepDefinitionId: defaultOpenStepDef.id,
    currentStepDefinition: defaultOpenStepDef,
    currentStepTransitionedAt: new Date(),
    currentStepEndsAt: null,
    status: WorkflowStatus.IN_PROGRESS,
  });

  await wfStateRepo.save(initialWorkflowState);

  const call = grantCallRepo.create({
    grantCallSlug: slug,
    grantProgram: grantProgram,
    name: 'Test Grant Call',
    description: 'This is a test grant call',
    businessCategory: BusinessCategory.STARTUP,
    createdById: creatorUser.id,
    createdBy: creatorUser,
    categories: ['Test Category'],
    totalGrantAmount: 10000,
    workflowState: initialWorkflowState,
  });

  return grantCallRepo.save(call);
}

/**
 * Creates and stores a grant call instance in 'open for application' status, along with its program and creator user.
 *
 * @param grantCallRepo the repository to save the grant call
 * @returns an object containing the created grant call, grant program, and creator user
 */
export async function createTestGrantCallOpenForApplications(grantCallRepo: Repository<GrantCall>): Promise<GrantCall> {
  const entityManager = grantCallRepo.manager;
  const wfStateRepo = entityManager.getRepository(WorkflowState);
  const stepDefRepo = entityManager.getRepository(WorkflowStepDefinition);
  const wfTemplateRepo = entityManager.getRepository(WorkflowTemplate);
  const grantProgramRepo = entityManager.getRepository(GrantProgram);
  const userRepo = entityManager.getRepository(User);

  // Create a test user
  const creatorUser = await userRepo.save(
    userRepo.create({
      email: `test-user-${Date.now()}@example.com`,
      displayName: 'Test User',
      addresses: [`0.0.${Math.floor(100000 + Math.random() * 900000)}`],
      emailVerified: true,
      role: Role.COMMUNITY_MEMBER,
    }),
  );

  // Create a test grant program
  const grantProgram = await grantProgramRepo.save(
    grantProgramRepo.create({
      grantProgramSlug: `test-grant-program-${Date.now()}`,
      name: 'Test Grant Program',
      description: 'This is a test grant program',
      scope: 'Test Scope',
      budget: 10000,
      grantorPublicProfileName: 'Test Grantor',
      grantorLogoURL: 'https://example.com/logo.jpg',
      grantProgramCoordinator: creatorUser,
      grantorDescription: 'Test Grantor Description',
      grantorWebsite: 'example.com',
    }),
  );

  // Find step definition for GC_OPEN_FOR_APPLICATIONS
  const openForAppsStepDef = await stepDefRepo.findOne({ where: { code: StageCode.GC_OPEN_FOR_APPLICATIONS } });

  // Find workflow template for CALL entity type
  const workflowTemplate = await wfTemplateRepo.findOneBy({
    entityType: WorkflowEntityType.CALL,
  });

  // Create initial workflow state
  const initialWorkflowState = wfStateRepo.create({
    workflowTemplate,
    currentStepDefinitionId: openForAppsStepDef.id,
    currentStepDefinition: openForAppsStepDef,
    currentStepTransitionedAt: new Date(),
    currentStepEndsAt: null,
    status: WorkflowStatus.IN_PROGRESS,
  });

  await wfStateRepo.save(initialWorkflowState);

  // Create a test slug
  const slug = `test-grant-call-${Date.now()}`;

  const grantCall = grantCallRepo.create({
    grantCallSlug: slug,
    grantProgram: grantProgram,
    name: 'Test Grant Call (Open for Applications)',
    description: 'This is a test grant call in open for applications status',
    businessCategory: BusinessCategory.STARTUP,
    createdById: creatorUser.id,
    createdBy: creatorUser,
    categories: ['Test Category'],
    totalGrantAmount: 10000,
    workflowState: initialWorkflowState,
  });

  await grantCallRepo.save(grantCall);

  return grantCall;
}

export function generateSessionToken(jwtService: JwtService, user: User) {
  return jwtService.sign({
    payload: { id: user.id, role: user.role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
  });
}

export function generateSessionTokenEmailNotVerified(jwtService: JwtService, user: User) {
  return jwtService.sign({
    payload: { id: user.id, role: user.role, scope: JwtScope.SESSION_TOKEN, emailVerified: false },
  });
}

export function createVerifiedUserRandomly(
  userRepository: Repository<User>,
  role: Role = Role.COORDINATOR,
): Promise<User> {
  const randomNumber = Math.floor(100000 + Math.random() * 900000);
  return userRepository.save(
    userRepository.create({
      email: `user${randomNumber}@example.com`,
      displayName: 'User ' + randomNumber,
      addresses: [`0.0.${randomNumber}`],
      emailVerified: true,
      role: role,
    }),
  );
}

export type TestData = {
  programCoordinator: User;
  grantProgram: GrantProgram;
  call1: GrantCall;
  grantCallCoordinator1: User;
  call2: GrantCall;
  grantCallCoordinator2: User;
  applicant1: User;
  applicant2: User;
  call1Application1?: GrantApplication;
  call1Application2?: GrantApplication;
  call2Application1?: GrantApplication;
  call2Application2?: GrantApplication;
};

export async function createTestData({
  userRepository,
  grantProgramRepo,
  grantCallRepo,
  applicationRepo,
  workflowTemplateRepo,
  workflowStepDefRepo,
  workflowStateRepo,
}: {
  userRepository: Repository<User>;
  grantProgramRepo: Repository<GrantProgram>;
  grantCallRepo: Repository<GrantCall>;
  applicationRepo?: Repository<GrantApplication>;
  workflowTemplateRepo: Repository<WorkflowTemplate>;
  workflowStepDefRepo: Repository<WorkflowStepDefinition>;
  workflowStateRepo: Repository<WorkflowState>;
}): Promise<TestData> {
  // Create users
  const programCoordinator = await userRepository.save(
    userRepository.create({
      email: `coordinator-${Date.now()}@example.com`,
      displayName: 'Coordinator',
      addresses: ['0.0.1001'],
      emailVerified: true,
      role: Role.COORDINATOR,
    }),
  );
  const grantCallCoordinator1 = await userRepository.save(
    userRepository.create({
      email: `call-coord1-${Date.now()}@example.com`,
      displayName: 'Call Coord 1',
      addresses: ['0.0.1002'],
      emailVerified: true,
      role: Role.COORDINATOR,
    }),
  );
  const grantCallCoordinator2 = await userRepository.save(
    userRepository.create({
      email: `call-coord2-${Date.now()}@example.com`,
      displayName: 'Call Coord 2',
      addresses: ['0.0.1003'],
      emailVerified: true,
      role: Role.COORDINATOR,
    }),
  );
  const applicant1 = await userRepository.save(
    userRepository.create({
      email: `applicant1-${Date.now()}@example.com`,
      displayName: 'Applicant 1',
      addresses: ['0.0.1004'],
      emailVerified: true,
      role: Role.COMMUNITY_MEMBER,
    }),
  );
  const applicant2 = await userRepository.save(
    userRepository.create({
      email: `applicant2-${Date.now()}@example.com`,
      displayName: 'Applicant 2',
      addresses: ['0.0.1005'],
      emailVerified: true,
      role: Role.COMMUNITY_MEMBER,
    }),
  );

  // Fetch workflow templates and step definitions
  const programTemplate = await workflowTemplateRepo.findOneBy({ entityType: WorkflowEntityType.PROGRAM });
  const callTemplate = await workflowTemplateRepo.findOneBy({ entityType: WorkflowEntityType.CALL });
  const appTemplate = await workflowTemplateRepo.findOneBy({ entityType: WorkflowEntityType.APPLICATION });
  const programStep = await workflowStepDefRepo.findOneBy({
    workflowTemplateId: programTemplate.id,
    code: StageCode.GP_OPEN,
  });
  const callStep = await workflowStepDefRepo.findOneBy({
    workflowTemplateId: callTemplate.id,
    code: StageCode.GC_OPEN_FOR_APPLICATIONS,
  });
  const appStepScreening = await workflowStepDefRepo.findOneBy({
    workflowTemplateId: appTemplate.id,
    code: StageCode.GA_SCREENING,
  });
  const appStepQualification = await workflowStepDefRepo.findOneBy({
    workflowTemplateId: appTemplate.id,
    code: StageCode.GA_QUALIFICATION,
  });

  // Create workflow states
  const programWfState = await workflowStateRepo.save(
    workflowStateRepo.create({
      workflowTemplate: programTemplate,
      currentStepDefinition: programStep,
      currentStepDefinitionId: programStep.id,
      status: WorkflowStatus.IN_PROGRESS,
      currentStepTransitionedAt: new Date(),
      currentStepEndsAt: null,
    }),
  );
  const call1WfState = await workflowStateRepo.save(
    workflowStateRepo.create({
      workflowTemplate: callTemplate,
      currentStepDefinition: callStep,
      currentStepDefinitionId: callStep.id,
      status: WorkflowStatus.IN_PROGRESS,
      currentStepTransitionedAt: new Date(),
      currentStepEndsAt: new Date(Date.now() + 10000),
    }),
  );
  const call2WfState = await workflowStateRepo.save(
    workflowStateRepo.create({
      workflowTemplate: callTemplate,
      currentStepDefinition: callStep,
      currentStepDefinitionId: callStep.id,
      status: WorkflowStatus.IN_PROGRESS,
      currentStepTransitionedAt: new Date(),
      currentStepEndsAt: new Date(Date.now() + 10000),
    }),
  );

  // Create grant program
  const grantProgram = await grantProgramRepo.save(
    grantProgramRepo.create({
      grantProgramSlug: `test-grant-program-${Date.now()}`,
      name: 'Test Grant Program',
      description: 'This is a test grant program',
      scope: 'Test Scope',
      budget: 10000,
      grantorPublicProfileName: 'Test Grantor',
      grantorLogoURL: 'https://example.com/logo.jpg',
      grantProgramCoordinator: programCoordinator,
      grantorDescription: 'Test Grantor Description',
      grantorWebsite: 'https://example.com',
      workflowState: programWfState,
      workflowStateId: programWfState.id,
    }),
  );

  // Create grant calls
  const call1 = await grantCallRepo.save(
    grantCallRepo.create({
      grantCallSlug: `test-grant-call-1-${Date.now()}`,
      grantProgram: grantProgram,
      name: 'Test Grant Call 1',
      description: 'This is a test grant call 1',
      businessCategory: BusinessCategory.STARTUP,
      categories: [GrantCategory.SUSTAINABILITY],
      totalGrantAmount: 10000,
      createdById: grantCallCoordinator1.id,
      createdBy: grantCallCoordinator1,
      workflowState: call1WfState,
      workflowStateId: call1WfState.id,
      stageSettings: [
        {
          stageUrl: 'https://example.com',
          workflowStepDefinitionId: call1WfState.currentStepDefinitionId,
          startDate: new Date(Date.now() - 10000),
          endDate: new Date(Date.now() + 10000),
        },
      ],
    }),
  );
  const call2 = await grantCallRepo.save(
    grantCallRepo.create({
      grantCallSlug: `test-grant-call-2-${Date.now()}`,
      grantProgram: grantProgram,
      name: 'Test Grant Call 2',
      description: 'This is a test grant call 2',
      businessCategory: BusinessCategory.STARTUP,
      categories: [GrantCategory.SUSTAINABILITY],
      totalGrantAmount: 20000,
      createdById: grantCallCoordinator2.id,
      createdBy: grantCallCoordinator2,
      workflowState: call2WfState,
      workflowStateId: call2WfState.id,
      stageSettings: [
        {
          stageUrl: 'https://example.com',
          workflowStepDefinitionId: call2WfState.currentStepDefinitionId,
          startDate: new Date(Date.now() - 10000),
          endDate: new Date(Date.now() + 10000),
        },
      ],
    }),
  );

  if (applicationRepo) {
    // Create applications for call1
    const call1Application1 = await applicationRepo.save(
      applicationRepo.create({
        title: 'App 1 Screening',
        description: 'desc',
        companyName: 'Company 1',
        companyCountry: 'Country 1',
        categories: [GrantCategory.SUSTAINABILITY],
        contactFullName: 'Contact 1',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '1234567890',
        actionTopicId: 'topic-1',
        votingTopicId: null,
        workflowState: await workflowStateRepo.save(
          workflowStateRepo.create({
            workflowTemplate: appTemplate,
            currentStepDefinition: appStepScreening,
            currentStepDefinitionId: appStepScreening.id,
            status: WorkflowStatus.IN_PROGRESS,
            currentStepTransitionedAt: new Date(),
            currentStepEndsAt: null,
          }),
        ),
        createdById: applicant1.id,
        createdBy: applicant1,
        grantCall: call1,
      }),
    );
    const call1Application2 = await applicationRepo.save(
      applicationRepo.create({
        title: 'App 2 Qualification',
        description: 'desc',
        companyName: 'Company 2',
        companyCountry: 'Country 2',
        categories: [GrantCategory.SUSTAINABILITY],
        contactFullName: 'Contact 2',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '0987654321',
        actionTopicId: 'topic-2',
        votingTopicId: null,
        workflowState: await workflowStateRepo.save(
          workflowStateRepo.create({
            workflowTemplate: appTemplate,
            currentStepDefinition: appStepQualification,
            currentStepDefinitionId: appStepQualification.id,
            status: WorkflowStatus.IN_PROGRESS,
            currentStepTransitionedAt: new Date(),
            currentStepEndsAt: null,
          }),
        ),
        createdById: applicant2.id,
        createdBy: applicant2,
        grantCall: call1,
      }),
    );

    // Create applications for call2
    const call2Application1 = await applicationRepo.save(
      applicationRepo.create({
        title: 'App 3 Screening',
        description: 'desc',
        companyName: 'Company 3',
        companyCountry: 'Country 3',
        categories: [GrantCategory.SUSTAINABILITY],
        contactFullName: 'Contact 3',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '1111111111',
        actionTopicId: 'topic-3',
        votingTopicId: null,
        workflowState: await workflowStateRepo.save(
          workflowStateRepo.create({
            workflowTemplate: appTemplate,
            currentStepDefinition: appStepScreening,
            currentStepDefinitionId: appStepScreening.id,
            status: WorkflowStatus.IN_PROGRESS,
            currentStepTransitionedAt: new Date(),
            currentStepEndsAt: null,
          }),
        ),
        createdById: applicant1.id,
        createdBy: applicant1,
        grantCall: call2,
      }),
    );
    const call2Application2 = await applicationRepo.save(
      applicationRepo.create({
        title: 'App 4 Qualification',
        description: 'desc',
        companyName: 'Company 4',
        companyCountry: 'Country 4',
        categories: [GrantCategory.SUSTAINABILITY],
        contactFullName: 'Contact 4',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '2222222222',
        actionTopicId: 'topic-4',
        votingTopicId: null,
        workflowState: await workflowStateRepo.save(
          workflowStateRepo.create({
            workflowTemplate: appTemplate,
            currentStepDefinition: appStepQualification,
            currentStepDefinitionId: appStepQualification.id,
            status: WorkflowStatus.IN_PROGRESS,
            currentStepTransitionedAt: new Date(),
            currentStepEndsAt: null,
          }),
        ),
        createdById: applicant2.id,
        createdBy: applicant2,
        grantCall: call2,
      }),
    );

    return {
      programCoordinator,
      grantProgram,
      call1,
      grantCallCoordinator1,
      call2,
      grantCallCoordinator2,
      applicant1,
      applicant2,
      call1Application1,
      call1Application2,
      call2Application1,
      call2Application2,
    };
  }

  return {
    programCoordinator,
    grantProgram,
    call1,
    grantCallCoordinator1,
    call2,
    grantCallCoordinator2,
    applicant1,
    applicant2,
  };
}
