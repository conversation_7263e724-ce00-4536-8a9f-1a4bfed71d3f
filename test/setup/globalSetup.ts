import { PostgreSqlContainer } from '@testcontainers/postgresql';
import { LocalstackContainer } from '@testcontainers/localstack';

const setup = async () => {
  const postgresContainer = await new PostgreSqlContainer('postgres:16-alpine').start();

  globalThis.postgresContainer = postgresContainer;

  process.env.POSTGRES_HOST = postgresContainer.getHost();
  process.env.POSTGRES_PORT = postgresContainer.getFirstMappedPort().toString();
  process.env.POSTGRES_DB = postgresContainer.getDatabase();
  process.env.POSTGRES_USER = postgresContainer.getUsername();
  process.env.POSTGRES_PASSWORD = postgresContainer.getPassword();
  process.env.POSTGRES_MIGRATIONS_RUN = 'true';

  const localStackContainer = await new LocalstackContainer('localstack/localstack:4.7.0')
    .withEnvironment({
      SERVICES: 'sns',
      EDGE_PORT: '4566',
    })
    .withExposedPorts(4566)
    .start();
  globalThis.localStackContainer = localStackContainer;

  process.env.USE_LOCALSTACK = 'true';
  process.env.LOCALSTACK_SNS_ENDPOINT = `http://${localStackContainer.getHost()}:${localStackContainer.getMappedPort(4566)}`;

  process.env.JWT_EXPIRATION_TIME = '1d';
  process.env.JWT_SECRET = 'test';

  process.env.PREVIEW_EMAIL = 'true';

  process.env.CHALLENGE_TOKEN_EXPIRATION_TIME = '5m';
  process.env.RECOVERY_TOKEN_EXPIRATION_TIME = '1d';

  process.env.OTP_EXPIRY_TIME_IN_MINUTES = '10';

  process.env.FRONTEND_BASE_URL = 'https://localhost:3000';

  process.env.HEDERA_ACCOUNT_ID = '0.0.0000000';
  process.env.MIRROR_NODE_URL = 'https://testnet.mirrornode.hedera.com';
};

export default setup;
