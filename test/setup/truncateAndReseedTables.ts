import { DataSource, Repository } from 'typeorm';
import dataSource from '../../src/database/typeorm.config';
import { StageCode } from '../../src/workflow/enums/stage-code.enum';
import { StageTransitionType } from '../../src/workflow/enums/stage-transition-type.enum';
import { WorkflowStepDefinition } from '../../src/workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../../src/workflow/entities/workflow-template.entity';
import { WorkflowEntityType } from '../../src/workflow/enums/workflow-entity-type.enum';

let connection: DataSource;

interface WorkflowStepSeedData {
  name: string;
  code: StageCode;
  sequenceNumber: number;
  transitionType: StageTransitionType;
  isTerminal: boolean;
  description?: string;
}

const grantProgramWorkflowSteps: WorkflowStepSeedData[] = [
  {
    name: 'Open',
    code: StageCode.GP_OPEN,
    sequenceNumber: 10,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
  {
    name: 'Finalized',
    code: StageCode.GP_FINALIZED,
    sequenceNumber: 100,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: true,
  },
];

const grantCallWorkflowSteps: WorkflowStepSeedData[] = [
  {
    name: 'Closed',
    code: StageCode.GC_CLOSED,
    sequenceNumber: 10,
    transitionType: StageTransitionType.AUTOMATIC,
    isTerminal: true,
  },
  {
    name: 'Open for Applications',
    code: StageCode.GC_OPEN_FOR_APPLICATIONS,
    sequenceNumber: 20,
    transitionType: StageTransitionType.AUTOMATIC,
    isTerminal: false,
  },
  {
    name: 'Screening',
    code: StageCode.GC_SCREENING,
    sequenceNumber: 30,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
  {
    name: 'Community Voting',
    code: StageCode.GC_COMMUNITY_VOTING,
    sequenceNumber: 40,
    transitionType: StageTransitionType.AUTOMATIC,
    isTerminal: false,
  },
  {
    name: 'Onboarding',
    code: StageCode.GC_ONBOARDING,
    sequenceNumber: 50,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
  {
    name: 'Final Community Voting',
    code: StageCode.GC_FINAL_COMMUNITY_VOTING,
    sequenceNumber: 60,
    transitionType: StageTransitionType.AUTOMATIC,
    isTerminal: false,
  },
  {
    name: 'Finalized',
    code: StageCode.GC_FINALIZED,
    sequenceNumber: 100,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: true,
  },
];

const grantApplicationWorkflowSteps: WorkflowStepSeedData[] = [
  {
    name: 'Screening',
    code: StageCode.GA_SCREENING,
    sequenceNumber: 10,
    transitionType: StageTransitionType.AUTOMATIC,
    isTerminal: false,
  },
  {
    name: 'Qualification',
    code: StageCode.GA_QUALIFICATION,
    sequenceNumber: 20,
    transitionType: StageTransitionType.AUTOMATIC,
    isTerminal: false,
  },
  {
    name: 'Interview',
    code: StageCode.GA_INTERVIEW,
    sequenceNumber: 30,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
  {
    name: 'Due Diligence',
    code: StageCode.GA_DUE_DILIGENCE,
    sequenceNumber: 40,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
  {
    name: 'Town Hall',
    code: StageCode.GA_TOWN_HALL,
    sequenceNumber: 50,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
  {
    name: 'Final Qualification',
    code: StageCode.GA_FINAL_QUALIFICATION,
    sequenceNumber: 60,
    transitionType: StageTransitionType.MANUAL,
    isTerminal: false,
  },
];

const grantProgramTemplateName = 'Default Grant Program Workflow';
const grantCallTemplateName = 'Default Grant Call Workflow';
const grantApplicationTemplateName = 'Default Grant Application Workflow';

async function seedWorkflow(
  templateRepo: Repository<WorkflowTemplate>,
  stepRepo: Repository<WorkflowStepDefinition>,
  templateName: string,
  entityType: WorkflowEntityType,
  stepsData: WorkflowStepSeedData[],
): Promise<void> {
  let template = await templateRepo.findOneBy({ name: templateName });

  if (!template) {
    template = templateRepo.create({
      name: templateName,
      entityType: entityType,
    });
    template = await templateRepo.save(template);

    const stepsToCreate = stepsData.map((stepData) =>
      stepRepo.create({
        ...stepData,
        workflowTemplateId: template.id,
        workflowTemplate: template,
      }),
    );

    await stepRepo.save(stepsToCreate);
    console.log(`   -> Added ${stepsToCreate.length} steps for ${templateName}.`);
  } else {
    console.log(` -> WorkflowTemplate "${templateName}" already exists. Skipping creation.`);
  }
}

beforeAll(async () => {
  connection = dataSource;
  if (!connection.isInitialized) {
    await connection.initialize();
  }
});

afterAll(async () => {
  console.log('Truncating tables');
  const entities = connection.entityMetadatas;
  const truncateQuery = entities.map((entity) => `TRUNCATE TABLE "${entity.tableName}" CASCADE;`).join('\n');

  await connection.query(truncateQuery);

  await seedWorkflow(
    connection.getRepository(WorkflowTemplate),
    connection.getRepository(WorkflowStepDefinition),
    grantProgramTemplateName,
    WorkflowEntityType.PROGRAM,
    grantProgramWorkflowSteps,
  );
  await seedWorkflow(
    connection.getRepository(WorkflowTemplate),
    connection.getRepository(WorkflowStepDefinition),
    grantCallTemplateName,
    WorkflowEntityType.CALL,
    grantCallWorkflowSteps,
  );
  await seedWorkflow(
    connection.getRepository(WorkflowTemplate),
    connection.getRepository(WorkflowStepDefinition),
    grantApplicationTemplateName,
    WorkflowEntityType.APPLICATION,
    grantApplicationWorkflowSteps,
  );

  if (connection.isInitialized) {
    await connection.destroy();
  }
});
