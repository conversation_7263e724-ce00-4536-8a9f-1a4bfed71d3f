import { StageCode } from '../src/workflow/enums/stage-code.enum';
import { WorkflowStatus } from '../src/workflow/enums/workflow-status.enum';
import { WorkflowEntityType } from '../src/workflow/enums/workflow-entity-type.enum';

jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

jest.mock('node:fs', () => ({
  existsSync: jest.fn().mockReturnValue(true),
}));

import { PutObjectCommandOutput, S3Client } from '@aws-sdk/client-s3';
import { HttpStatus, INestApplication, ValidationPipe } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import request from 'supertest';
import { Repository } from 'typeorm';
import { AuthModule } from '../src/auth/auth.module';
import { JwtScope } from '../src/auth/auth.service';
import { User } from '../src/auth/entities/user.entity';
import { Role } from '../src/auth/role.enum';
import { GrantCall } from '../src/grant-call/entities/grant-call.entity';
import { GrantCallModule } from '../src/grant-call/grant-call.module';
import { CreateGrantProgram, GrantProgramWithGrantCallCountDto, UpdateGrantProgram } from '../src/grant-program/dto';
import { GrantProgram } from '../src/grant-program/entities/grant-program.entity';
import { GrantProgramModule } from '../src/grant-program/grant-program.module';
import {
  createTestData,
  createTestGrantCall,
  createTestGrantProgram,
  createTestUsers,
  createVerifiedUserRandomly,
} from './test.utils';
import { HederaService } from '../src/hedera/hedera.service';
import { KeyManagementService } from '../src/key-management/key-management.service';
import { GrantProgramService } from '../src/grant-program/grant-program.service';
import { DatabaseModule } from '../src/database/database.module';
import { WorkflowState } from '../src/workflow/entities/workflow-state.entity';
import { WorkflowStepDefinition } from '../src/workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../src/workflow/entities/workflow-template.entity';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from '../src/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../src/auth/guards/roles.guard';
// Mock the s3 client to upload the images. This is a simple mock that just logs the upload request.
class MockS3Client extends S3Client {
  override async send(): Promise<PutObjectCommandOutput> {
    return {
      $metadata: { httpStatusCode: 200 },
    };
  }
}

describe('GrantProgram (e2e)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let grantProgramRepo: Repository<GrantProgram>;
  let grantProgramService: GrantProgramService;
  let grantCallRepo: Repository<GrantCall>;
  let jwtService: JwtService;
  let workflowTemplateRepo: Repository<WorkflowTemplate>;
  let workflowStepDefRepo: Repository<WorkflowStepDefinition>;
  let workflowStateRepo: Repository<WorkflowState>;
  let testUsers: User[];
  const testUser1 = { email: '<EMAIL>', displayName: 'Test User 1', address: '0.0.1780959' };
  const testUser2 = { email: '<EMAIL>', displayName: 'Test User 2', address: '0.0.2780959' };
  const testUser3 = { email: '<EMAIL>', displayName: 'Test User 3', address: '0.0.3780959' };

  beforeAll(async () => {
    // Setup the testing module
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({ isGlobal: true }),
        DatabaseModule,
        AuthModule,
        GrantProgramModule,
        GrantCallModule,
      ],
      providers: [
        {
          provide: APP_GUARD,
          useExisting: JwtAuthGuard,
        },
        JwtAuthGuard,
        {
          provide: APP_GUARD,
          useExisting: RolesGuard,
        },
        RolesGuard,
      ],
    })
      .overrideProvider(S3Client)
      .useClass(MockS3Client)
      .overrideProvider(HederaService)
      .useValue({
        createTopic: jest.fn().mockResolvedValue({ toString: () => 'mock-topic-id' }),
        submitMessage: jest.fn(),
        getMessagesFromTopic: jest.fn(),
      })
      .overrideProvider(KeyManagementService)
      .useValue({
        signMessage: jest.fn().mockResolvedValue(Buffer.from('mocked-signature')),
      })
      .compile();
    app = moduleFixture.createNestApplication();
    // Enable validation pipe because the one in main.ts is not used in tests
    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    grantProgramRepo = moduleFixture.get<Repository<GrantProgram>>(getRepositoryToken(GrantProgram));
    grantCallRepo = moduleFixture.get<Repository<GrantCall>>(getRepositoryToken(GrantCall));
    jwtService = moduleFixture.get<JwtService>(JwtService);
    grantProgramService = moduleFixture.get<GrantProgramService>(GrantProgramService);
    workflowTemplateRepo = moduleFixture.get<Repository<WorkflowTemplate>>(getRepositoryToken(WorkflowTemplate));
    workflowStepDefRepo = moduleFixture.get<Repository<WorkflowStepDefinition>>(
      getRepositoryToken(WorkflowStepDefinition),
    );
    workflowStateRepo = moduleFixture.get<Repository<WorkflowState>>(getRepositoryToken(WorkflowState));
    await app.init();

    jest.spyOn(grantProgramService as any, 'uploadLogoToS3AndDeleteLocalFile').mockResolvedValue('mocked-file-path');

    testUsers = await createTestUsers([testUser1, testUser2, testUser3], userRepository);
  }, 60000);

  afterAll(async () => {
    await app.close();
  });

  describe('should upload a grant program logo', () => {
    it('should upload a grant program logo to the server', async () => {
      const authToken = jwtService.sign({
        payload: { id: testUsers[0].id, role: testUsers[0].role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
      });
      const res = await request(app.getHttpServer())
        .post('/grant-program/upload')
        .attach('file', Buffer.from('test'), 'test.jpg')
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toEqual(201);
      expect(res.body).toEqual({
        success: true,
        filePath: expect.any(String),
      });
    });

    it('should throw an error if the file is not an image', async () => {
      const authToken = jwtService.sign({
        payload: { id: testUsers[0].id, role: testUsers[0].role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
      });
      const res = await request(app.getHttpServer())
        .post('/grant-program/upload')
        .attach('file', Buffer.from('test'), 'test.txt')
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(res.body.message).toEqual('Only image files are allowed!');
    });

    it('should throw an error if the file size is greater than 2MB', async () => {
      const authToken = jwtService.sign({
        payload: { id: testUsers[0].id, role: testUsers[0].role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
      });
      const res = await request(app.getHttpServer())
        .post('/grant-program/upload')
        .attach('file', Buffer.from('test'.repeat(1024 * 1024)), 'test.jpg')
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toEqual(HttpStatus.PAYLOAD_TOO_LARGE);
      expect(res.body.message).toEqual('File too large');
    });
  });

  it('should create a grant program with associated workflow state', async () => {
    const createGrantProgram: CreateGrantProgram = {
      name: 'Test Grant Program',
      description: 'This is a test grant program',
      scope: 'Test Scope',
      budget: 10000,
      grantorPublicProfileName: 'Test Grantor',
      grantorLogoURL: 'temp/logo.png',
      grantorDescription: 'Test grantor description',
      grantorWebsite: 'test-grantor.com',
    };

    const authToken = jwtService.sign({
      payload: { id: testUsers[0].id, role: testUsers[0].role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
    });
    const res = await request(app.getHttpServer())
      .post('/grant-program')
      .send(createGrantProgram)
      .set('Authorization', `Bearer ${authToken}`);

    expect(res.status).toEqual(201);
    expect(res.body).toMatchObject({
      success: true,
      message: 'Grant Program created successfully.',
    });
    expect(res.body.grantProgramSlug).toContain('test-grant-program');

    // Fetch the grant program slug from the DB for subsequent tests
    const programs = await grantProgramRepo.find({
      relations: [
        'grantProgramCoordinator',
        'workflowState',
        'workflowState.currentStepDefinition',
        'workflowState.workflowTemplate',
      ],
    });
    expect(programs.length).toEqual(1);
    const program = programs[0];

    // Verify basic grant program attributes
    expect(program.grantProgramSlug).toContain('test-grant-program');
    expect(program.name).toEqual(createGrantProgram.name);
    expect(program.description).toEqual(createGrantProgram.description);
    expect(program.scope).toEqual(createGrantProgram.scope);
    expect(program.budget).toEqual(createGrantProgram.budget);
    expect(program.grantorPublicProfileName).toEqual(createGrantProgram.grantorPublicProfileName);
    expect(program.grantorLogoURL).toContain('mocked-file-path');
    expect(program.grantProgramCoordinator.email).toEqual(testUsers[0].email);
    expect(program.grantorDescription).toEqual(createGrantProgram.grantorDescription);
    expect(program.grantorWebsite).toEqual(`https://${createGrantProgram.grantorWebsite}`);

    // Verify workflow state was properly created
    expect(program.workflowState).toBeDefined();
    expect(program.workflowState.currentStepDefinition).toBeDefined();
    expect(program.workflowState.currentStepDefinition.code).toEqual(StageCode.GP_OPEN);
    expect(program.workflowState.status).toEqual(WorkflowStatus.IN_PROGRESS);
    expect(program.workflowState.workflowTemplate).toBeDefined();
    expect(program.workflowState.workflowTemplate.entityType).toEqual(WorkflowEntityType.PROGRAM);
    expect(program.workflowState.currentStepTransitionedAt).toBeDefined();
    expect(program.workflowState.currentStepEndsAt).toBeNull();
  });

  it('should get all grant programs', async () => {
    const grantProgram1 = await createTestGrantProgram('grant-program-aaaaaa', testUsers[0], grantProgramRepo);
    await createTestGrantCall('grant-call-aaaaaa', grantProgram1, testUsers[0], grantCallRepo);
    await createTestGrantCall('grant-call-bbbbbb', grantProgram1, testUsers[0], grantCallRepo);
    const grantProgram2 = await createTestGrantProgram('grant-program-bbbbbb', testUsers[1], grantProgramRepo);
    await createTestGrantCall('grant-call-cccccc', grantProgram2, testUsers[1], grantCallRepo);
    await createTestGrantCall('grant-call-dddddd', grantProgram2, testUsers[1], grantCallRepo);

    const res = await request(app.getHttpServer()).get(`/grant-program/`);
    expect(res.status).toEqual(200);
    const programs: GrantProgramWithGrantCallCountDto[] = res.body.data;
    expect(programs).toHaveLength(3); // 2 grant programs created in this test + 1 created in the previous test
    // Check if grantProgram1 is present
    expect(programs.find((p) => p.grantProgramSlug === grantProgram1.grantProgramSlug)).toBeDefined();
    expect(programs.find((p) => p.grantProgramSlug === grantProgram2.grantProgramSlug)).toBeDefined();
    const programDto2 = programs.find((p) => p.grantProgramSlug === grantProgram2.grantProgramSlug);
    expect(programDto2.name).toEqual(grantProgram2.name);
    expect(programDto2.grantCallsCount).toEqual(2);
  });

  it('should throw error if the user is not signed in for filtering by me', async () => {
    const res = await request(app.getHttpServer()).get(`/grant-program?user=me`);
    expect(res.status).toEqual(HttpStatus.UNAUTHORIZED);
    expect(res.body.message).toEqual('User not signed in.');
  });

  it('should get all grant programs based on authenticated user', async () => {
    const newUser = await createVerifiedUserRandomly(userRepository);
    // Create a program of which the user is the coordinator
    const program1 = await createTestGrantProgram('grant-program-78932', newUser, grantProgramRepo);
    // Create a program of which the user is not the coordinator but a coordinator of an associated grant call. Also add
    // a second grant call unrelated to the user.
    const grantProgram2 = await createTestGrantProgram('grant-program-78933', testUsers[0], grantProgramRepo);
    await createTestGrantCall('grant-call-78933', grantProgram2, newUser, grantCallRepo);
    await createTestGrantCall('grant-call-78934', grantProgram2, testUsers[0], grantCallRepo);
    // Create a program of which the user is not the coordinator but a member of an associated grant call
    await createTestGrantProgram('grant-program-78934', testUsers[0], grantProgramRepo);

    const authToken = jwtService.sign({
      payload: { id: newUser.id, role: newUser.role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
    });
    const res = await request(app.getHttpServer())
      .get(`/grant-program?user=me`)
      .set('Authorization', `Bearer ${authToken}`);
    expect(res.status).toEqual(200);
    const programs: GrantProgramWithGrantCallCountDto[] = res.body.data;
    expect(programs).toHaveLength(2);
    expect(programs.find((p) => p.grantProgramSlug === program1.grantProgramSlug)).toBeDefined();
    expect(programs.find((p) => p.grantProgramSlug === grantProgram2.grantProgramSlug)).toBeDefined();
  });

  it('should get a grant program by id', async () => {
    const grantProgram = await createTestGrantProgram('grant-program-ccccccc', testUsers[0], grantProgramRepo);
    await createTestGrantCall('grant-call-eeeeee', grantProgram, testUsers[0], grantCallRepo);
    await createTestGrantCall('grant-call-ffffff', grantProgram, testUsers[0], grantCallRepo);
    const res = await request(app.getHttpServer()).get(`/grant-program/${grantProgram.grantProgramSlug}`);

    expect(res.status).toEqual(200);
    const program: GrantProgramWithGrantCallCountDto = res.body.data;
    expect(program.grantProgramSlug).toEqual(grantProgram.grantProgramSlug);
    expect(program.name).toEqual(grantProgram.name);
    expect(program.description).toEqual(grantProgram.description);
    expect(program.scope).toEqual(grantProgram.scope);
    expect(program.budget).toEqual(grantProgram.budget);
    expect(program.grantorPublicProfileName).toEqual(grantProgram.grantorPublicProfileName);
    expect(program.grantorLogoURL).toBeDefined();
    expect(program.status).toBeDefined(); // Status comes from DTO, not entity
    expect(program.grantProgramCoordinator.displayName).toEqual(testUsers[0].displayName);
    expect(program.grantProgramCoordinator.id).toEqual(testUsers[0].id);
    expect(program.grantorWebsite).toEqual(grantProgram.grantorWebsite);
    expect(program.grantCallsCount).toEqual(2);
  });

  it('should update a grant program', async () => {
    let grantProgram = await createTestGrantProgram('test-grant-program-bbbbbbb', testUsers[0], grantProgramRepo);
    const authToken = jwtService.sign({
      payload: { id: testUsers[0].id, role: testUsers[0].role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
    });

    // Prepare file to update grantor logo
    let res = await request(app.getHttpServer())
      .post('/grant-program/upload')
      .attach('file', Buffer.from('test'), 'test.jpg')
      .set('Authorization', `Bearer ${authToken}`);
    expect(res.status).toEqual(201);
    const logoFilePath = res.body.filePath;

    const updateGrantProgram: UpdateGrantProgram = {
      name: 'Test Grant Program Updated',
      description: 'This is a test grant program updated',
      scope: 'Test Scope Updated',
      budget: 20000,
      grantorPublicProfileName: 'Test Grantor Updated',
      grantorLogoURL: logoFilePath,
      grantorDescription: 'Test grantor description updated',
      grantorWebsite: 'https://test-grantor-updated.com',
    };

    res = await request(app.getHttpServer())
      .patch(`/grant-program/${grantProgram.grantProgramSlug}`)
      .send(updateGrantProgram)
      .set('Authorization', `Bearer ${authToken}`);

    expect(res.status).toEqual(200);
    expect(res.body).toEqual({
      success: true,
      grantProgramSlug: expect.stringContaining('test-grant-program-updated'),
    });

    const newSlug = res.body.grantProgramSlug;
    grantProgram = await grantProgramRepo.findOneOrFail({
      where: { grantProgramSlug: newSlug },
      relations: { workflowState: { currentStepDefinition: true } },
    });
    expect(grantProgram.grantProgramSlug).toEqual(newSlug);
    expect(grantProgram.name).toEqual(updateGrantProgram.name);
    expect(grantProgram.description).toEqual(updateGrantProgram.description);
    expect(grantProgram.scope).toEqual(updateGrantProgram.scope);
    expect(grantProgram.budget).toEqual(updateGrantProgram.budget);
    expect(grantProgram.grantorPublicProfileName).toEqual(updateGrantProgram.grantorPublicProfileName);
    expect(grantProgram.grantorLogoURL).toContain('mocked-file-path');
    expect(grantProgram.workflowState.currentStepDefinition.code).toEqual(StageCode.GP_OPEN);
    expect(grantProgram.grantorDescription).toEqual(updateGrantProgram.grantorDescription);
    expect(grantProgram.grantorWebsite).toEqual(updateGrantProgram.grantorWebsite);
  });

  describe('should get grant calls for program', () => {
    it('should get all grant calls for program', async () => {
      const { grantProgram, call1, call2, programCoordinator } = await createTestData({
        userRepository,
        grantProgramRepo,
        grantCallRepo,
        workflowTemplateRepo,
        workflowStepDefRepo,
        workflowStateRepo,
      });

      const sessionToken = jwtService.sign({
        payload: {
          id: programCoordinator.id,
          role: programCoordinator.role,
          scope: JwtScope.SESSION_TOKEN,
          emailVerified: true,
        },
      });

      const res = await request(app.getHttpServer())
        .get(`/grant-program/${grantProgram.grantProgramSlug}/grant-calls`)
        .set('Authorization', `Bearer ${sessionToken}`);

      expect(res.status).toEqual(200);
      expect(res.body.grantCalls).toHaveLength(2);
      expect(res.body.grantCalls[0].grantCallSlug).toEqual(call1.grantCallSlug);
      expect(res.body.grantCalls[1].grantCallSlug).toEqual(call2.grantCallSlug);
    });
  });

  describe('Grant Program Workflow', () => {
    it('should transition grant program from GP_OPEN to GP_FINALIZED', async () => {
      const slug = 'workflow-test-program';
      const grantProgram = await createTestGrantProgram(slug, testUsers[0], grantProgramRepo);

      // Verify initial state is GP_OPEN
      const initialProgram = await grantProgramRepo.findOne({
        where: { id: grantProgram.id },
        relations: { workflowState: { currentStepDefinition: true } },
      });

      expect(initialProgram.workflowState).toBeDefined();
      expect(initialProgram.workflowState.currentStepDefinition.code).toEqual(StageCode.GP_OPEN);
      expect(initialProgram.workflowState.status).toEqual(WorkflowStatus.IN_PROGRESS);

      const authToken = jwtService.sign({
        payload: { id: testUsers[0].id, role: testUsers[0].role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
      });

      const res = await request(app.getHttpServer())
        .post(`/grant-program/${slug}/transition`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toEqual(201);

      const updatedProgram = await grantProgramRepo.findOne({
        where: { id: grantProgram.id },
        relations: { workflowState: { currentStepDefinition: true } },
      });

      expect(updatedProgram.workflowState.currentStepDefinition.code).toEqual(StageCode.GP_FINALIZED);
      expect(updatedProgram.workflowState.status).toEqual(WorkflowStatus.IN_PROGRESS);
      expect(updatedProgram.workflowState.currentStepTransitionedAt).toBeDefined();
      expect(updatedProgram.workflowState.currentStepEndsAt).toBeNull();
    });

    it('should not allow transition from terminal state', async () => {
      const slug = 'terminal-test-program';
      await createTestGrantProgram(slug, testUsers[0], grantProgramRepo);

      const authToken = jwtService.sign({
        payload: { id: testUsers[0].id, role: testUsers[0].role, scope: JwtScope.SESSION_TOKEN, emailVerified: true },
      });

      // GP_OPEN -> GP_FINALIZED
      await request(app.getHttpServer())
        .post(`/grant-program/${slug}/transition`)
        .set('Authorization', `Bearer ${authToken}`);

      // GP_FINALIZED -> !
      const res = await request(app.getHttpServer())
        .post(`/grant-program/${slug}/transition`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toEqual(HttpStatus.UNPROCESSABLE_ENTITY);
      expect(res.body.message).toContain('terminal step');
    });

    it('should fail transition if user is not grant program coordinator', async () => {
      const slug = 'unauthorized-test-program';
      await createTestGrantProgram(slug, testUsers[0], grantProgramRepo);
      const nonGrantCoordinator = await createVerifiedUserRandomly(userRepository, Role.COMMUNITY_MEMBER);

      const nonCoordinatorToken = jwtService.sign({
        payload: {
          id: nonGrantCoordinator.id,
          role: nonGrantCoordinator.role,
          scope: JwtScope.SESSION_TOKEN,
          emailVerified: true,
        },
      });

      const res = await request(app.getHttpServer())
        .post(`/grant-program/${slug}/transition`)
        .set('Authorization', `Bearer ${nonCoordinatorToken}`);

      expect(res.status).toEqual(HttpStatus.FORBIDDEN);
      expect(res.body.message).toContain('Forbidden resource');
    });
  });
});
