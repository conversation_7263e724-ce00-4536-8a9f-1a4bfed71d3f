import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { SnapshotModule } from '../src/snapshot/snapshot.module';
import { DatabaseModule } from '../src/database/database.module';
import { ConfigModule } from '@nestjs/config';
import { randomUUID } from 'node:crypto';

jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

describe('Snapshot (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule.forRoot({ isGlobal: true }), SnapshotModule, DatabaseModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('Get all snapshots', () => {
    it('should return all snapshots', () => {
      return request(app.getHttpServer())
        .get('/snapshot')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });
  });

  describe('Get snapshot by id', () => {
    it('should return 404 for non-existent snapshot', () => {
      const randomUuid = randomUUID();
      return request(app.getHttpServer()).get(`/snapshot/${randomUuid}`).expect(404);
    });
  });

  describe('Restore snapshot', () => {
    it('should return 400 for non-existent snapshot', () => {
      return request(app.getHttpServer())
        .post('/snapshot/restore')
        .send({
          snapshotId: randomUUID(),
        })
        .expect(404);
    });
  });

  describe('Delete snapshot', () => {
    it('should return 404 for non-existent snapshot', () => {
      return request(app.getHttpServer()).delete(`/snapshot/${randomUUID()}`).expect(404);
    });
  });
});
