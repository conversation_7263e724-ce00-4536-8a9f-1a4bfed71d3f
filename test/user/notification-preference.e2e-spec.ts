import { HttpStatus, INestApplication, ValidationPipe } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { AuthModule } from '../../src/auth/auth.module';
import { DatabaseModule } from '../../src/database/database.module';
import request from 'supertest';
import { JwtService } from '@nestjs/jwt';
import { JwtScope } from '../../src/auth/auth.service';
import { Repository } from 'typeorm';
import { User } from '../../src/auth/entities/user.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
  NotificationType,
  UserNotificationPreferences,
} from '../../src/user/notification-preference/entities/user-notification-preferences.entity';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from '../../src/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../src/auth/guards/roles.guard';
import { NotificationPreferenceModule } from '../../src/user/notification-preference/notification-preference.module';

jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

describe('NotificationPreference (e2e)', () => {
  let app: INestApplication;
  let sessionTokenOne: string;
  let sessionTokenTwo: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule.forRoot({ isGlobal: true }), DatabaseModule, AuthModule, NotificationPreferenceModule],
      providers: [
        {
          provide: APP_GUARD,
          useExisting: JwtAuthGuard,
        },
        JwtAuthGuard,
        {
          provide: APP_GUARD,
          useExisting: RolesGuard,
        },
        RolesGuard,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();

    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
    await app.init();

    const userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    const jwtService = moduleFixture.get<JwtService>(JwtService);

    // ——— User One: has one preference row ———
    const testUserOne = await userRepository.save(
      userRepository.create({
        addresses: ['0.0.1780959'],
        email: '<EMAIL>',
        displayName: 'Test User One',
        emailVerified: true,
      }),
    );
    const userNotificationPreferenceRepository = moduleFixture.get<Repository<UserNotificationPreferences>>(
      getRepositoryToken(UserNotificationPreferences),
    );
    await userNotificationPreferenceRepository.save(
      userNotificationPreferenceRepository.create({
        userId: testUserOne.id,
        notificationType: NotificationType.GRANT_CALL_UPDATE,
        isEnabled: false,
      }),
    );

    sessionTokenOne = jwtService.sign({
      payload: {
        id: testUserOne.id,
        role: testUserOne.role,
        scope: JwtScope.SESSION_TOKEN,
        emailVerified: testUserOne.emailVerified,
      },
    });

    // ——— User Two: no preference rows ———
    const testUserTwo = await userRepository.save(
      userRepository.create({
        addresses: ['0.0.1780960'],
        email: '<EMAIL>',
        displayName: 'Test User Two',
        emailVerified: true,
      }),
    );
    sessionTokenTwo = jwtService.sign({
      payload: {
        id: testUserTwo.id,
        role: testUserTwo.role,
        scope: JwtScope.SESSION_TOKEN,
        emailVerified: testUserTwo.emailVerified,
      },
    });
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/user/notification-preferences (GET)', () => {
    it('should return existing preferences for User One', async () => {
      const getResponse = await request(app.getHttpServer())
        .get('/user/notification-preferences')
        .set('Authorization', `Bearer ${sessionTokenOne}`);

      expect(getResponse.status).toEqual(HttpStatus.OK);
      expect(getResponse.body).toEqual({
        grant_call_update: false,
        grant_application_update: true,
      });
    });

    it('should return default for both preferences when none exist for User Two', async () => {
      const res = await request(app.getHttpServer())
        .get('/user/notification-preferences')
        .set('Authorization', `Bearer ${sessionTokenTwo}`);

      expect(res.status).toEqual(HttpStatus.OK);
      expect(res.body).toEqual({
        grant_call_update: true,
        grant_application_update: true,
      });
    });
  });

  describe('/user/notification-preferences (PATCH)', () => {
    it('should update a single preference', async () => {
      const updateResponse = await request(app.getHttpServer())
        .patch('/user/notification-preferences')
        .set('Authorization', `Bearer ${sessionTokenOne}`)
        .send({ notificationType: NotificationType.GRANT_CALL_UPDATE, isEnabled: false });

      expect(updateResponse.status).toEqual(HttpStatus.NO_CONTENT);

      const getResponse = await request(app.getHttpServer())
        .get('/user/notification-preferences')
        .set('Authorization', `Bearer ${sessionTokenOne}`);

      expect(getResponse.status).toEqual(HttpStatus.OK);
      expect(getResponse.body).toEqual({
        grant_call_update: false,
        grant_application_update: true,
      });
    });

    it('should return BAD_REQUEST when invalid notification preference type provided', async () => {
      const updateResponse = await request(app.getHttpServer())
        .patch('/user/notification-preferences')
        .set('Authorization', `Bearer ${sessionTokenOne}`)
        .send({ notificationType: 'invalid_type', isEnabled: true });

      expect(updateResponse.status).toEqual(HttpStatus.BAD_REQUEST);
      expect(updateResponse.body.message[0]).toContain('notificationType must be one of the following values:');
    });
  });
});
