import { HttpStatus, INestApplication, ValidationPipe } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AuthModule } from '../../src/auth/auth.module';
import { User } from '../../src/auth/entities/user.entity';
import { JwtAuthGuard } from '../../src/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../src/auth/guards/roles.guard';
import { DatabaseModule } from '../../src/database/database.module';
import { PhoneVerificationModule } from '../../src/user/phone-verification/phone-verification.module';
import { Repository } from 'typeorm';
import request from 'supertest';
import { JwtScope } from '../../src/auth/auth.service';

jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

describe('PhoneVerification (e2e)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let sessionTokenWithNoPhoneNumber: string;
  let userIdWithNoPhoneNumber: number;
  let sessionTokenWithPhoneNumber: string;
  let userIdWithPhoneNumber: number;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule.forRoot({ isGlobal: true }), DatabaseModule, AuthModule, PhoneVerificationModule],
      providers: [
        {
          provide: APP_GUARD,
          useExisting: JwtAuthGuard,
        },
        JwtAuthGuard,
        {
          provide: APP_GUARD,
          useExisting: RolesGuard,
        },
        RolesGuard,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();

    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
    await app.init();

    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    const jwtService = moduleFixture.get<JwtService>(JwtService);

    const testUserWithNoPhoneNumber = await userRepository.save(
      userRepository.create({
        addresses: ['0.0.1780959'],
        email: '<EMAIL>',
        displayName: 'Test User One',
        emailVerified: true,
      }),
    );
    userIdWithNoPhoneNumber = testUserWithNoPhoneNumber.id;
    sessionTokenWithNoPhoneNumber = jwtService.sign({
      payload: {
        id: testUserWithNoPhoneNumber.id,
        role: testUserWithNoPhoneNumber.role,
        scope: JwtScope.SESSION_TOKEN,
        emailVerified: testUserWithNoPhoneNumber.emailVerified,
      },
    });

    const testUserWithPhoneNumber = await userRepository.save(
      userRepository.create({
        addresses: ['0.0.1780959'],
        email: '<EMAIL>',
        displayName: 'Test User Two',
        emailVerified: true,
        isPhoneVerified: true,
        phoneNumber: '+91**********',
      }),
    );
    userIdWithPhoneNumber = testUserWithPhoneNumber.id;
    sessionTokenWithPhoneNumber = jwtService.sign({
      payload: {
        id: testUserWithPhoneNumber.id,
        role: testUserWithPhoneNumber.role,
        scope: JwtScope.SESSION_TOKEN,
        emailVerified: testUserWithPhoneNumber.emailVerified,
      },
    });
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Reset both users’ phone verification state before each test
    await userRepository.update(userIdWithNoPhoneNumber, {
      phoneNumber: null,
      otp: null,
      otpExpiresAt: null,
    });
    await userRepository.update(userIdWithPhoneNumber, {
      phoneNumber: '+91**********',
      isPhoneVerified: true,
    });
  });

  const dto = { phoneNumber: '9876787678', countryCode: 'IN' };

  describe('/user/phone-verification/send-code (POST)', () => {
    it('should send a new OTP for a fresh phone number', async () => {
      const response = await request(app.getHttpServer())
        .post('/user/phone-verification/send-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send(dto);
      expect(response.statusCode).toEqual(HttpStatus.NO_CONTENT);
    });

    it('should reject when the phone number is already taken', async () => {
      // This number was created in beforeAll as a verified phone on User Two
      const response = await request(app.getHttpServer())
        .post('/user/phone-verification/send-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send({ phoneNumber: '**********', countryCode: 'IN' });

      expect(response.statusCode).toEqual(HttpStatus.CONFLICT);
      expect(response.body.message).toContain('This phone number is already associated with another account');
    });

    it('should validate input and reject invalid payloads', async () => {
      const response = await request(app.getHttpServer())
        .post('/user/phone-verification/send-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send({ phoneNumber: 'invalid', countryCode: 'US' });
      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);
      expect(response.body.message[0]).toContain('phoneNumber must be a valid phone number');
    });

    it('allows two users to request OTP for the same unverified number', async () => {
      // Since the number is still unverified, both users can request an OTP without conflict
      await request(app.getHttpServer())
        .post('/user/phone-verification/send-code')
        .set('Authorization', `Bearer ${sessionTokenWithPhoneNumber}`)
        .send(dto);

      const res = await request(app.getHttpServer())
        .post('/user/phone-verification/send-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send(dto);

      expect(res.statusCode).toEqual(HttpStatus.NO_CONTENT);
    });
  });

  describe('GET /user/phone-verification/resend-code', () => {
    it('should reject when no phone number is on record', async () => {
      const response = await request(app.getHttpServer())
        .get('/user/phone-verification/resend-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`);
      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);
      expect(response.body.message).toContain('Phone number not found');
    });

    it('should reject if the phone number is already verified', async () => {
      const response = await request(app.getHttpServer())
        .get('/user/phone-verification/resend-code')
        .set('Authorization', `Bearer ${sessionTokenWithPhoneNumber}`);
      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);
      expect(response.body.message).toContain('Phone number already verified');
    });

    it('should prevent resend when an OTP is still active', async () => {
      // trigger initial send
      await request(app.getHttpServer())
        .post('/user/phone-verification/send-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send(dto);

      const response = await request(app.getHttpServer())
        .get('/user/phone-verification/resend-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`);

      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);
      expect(response.body.message).toContain('An OTP for this number is still valid');
    });

    it('should issue a new OTP once the previous one expires', async () => {
      // send and then expire otp manually
      await request(app.getHttpServer())
        .post('/user/phone-verification/send-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send(dto);

      await userRepository.update(userIdWithNoPhoneNumber, {
        otpExpiresAt: new Date(Date.now() - 1000),
      });

      const response = await request(app.getHttpServer())
        .get('/user/phone-verification/resend-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`);

      expect(response.statusCode).toEqual(HttpStatus.NO_CONTENT);
    });
  });

  describe('POST /user/phone-verification/verify-code', () => {
    it('should mark the phone as verified with the correct OTP', async () => {
      // send OTP and load it from DB
      await request(app.getHttpServer())
        .post('/user/phone-verification/send-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send(dto);

      const dbUser = await userRepository.findOne({
        where: {
          id: userIdWithNoPhoneNumber,
        },
      });

      // verify with the fetched OTP
      const response = await request(app.getHttpServer())
        .post('/user/phone-verification/verify-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send({ otp: dbUser.otp });

      expect(response.statusCode).toEqual(HttpStatus.NO_CONTENT);
    });

    it('should reject verification if no phone is set', async () => {
      const response = await request(app.getHttpServer())
        .post('/user/phone-verification/verify-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send({ otp: '123456' });
      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);
      expect(response.body.message).toContain('Phone number not found');
    });

    it('should reject if the phone is already verified', async () => {
      const response = await request(app.getHttpServer())
        .post('/user/phone-verification/verify-code')
        .set('Authorization', `Bearer ${sessionTokenWithPhoneNumber}`)
        .send({ otp: '092838' });

      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);
      expect(response.body.message).toContain('Phone number already verified');
    });

    it('should reject expired OTPs', async () => {
      // send OTP, expire it, then attempt verify
      await request(app.getHttpServer())
        .post('/user/phone-verification/send-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send(dto);

      const dbUser = await userRepository.findOne({
        where: {
          id: userIdWithNoPhoneNumber,
        },
      });

      await userRepository.update(userIdWithNoPhoneNumber, {
        otpExpiresAt: new Date(Date.now() - 1000),
      });

      const response = await request(app.getHttpServer())
        .post('/user/phone-verification/verify-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send({ otp: dbUser.otp });

      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);
      expect(response.body.message).toContain('Invalid OTP');
    });

    it('should reject incorrect OTPs', async () => {
      // send OTP then attempt wrong code
      await request(app.getHttpServer())
        .post('/user/phone-verification/send-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send(dto);

      const response = await request(app.getHttpServer())
        .post('/user/phone-verification/verify-code')
        .set('Authorization', `Bearer ${sessionTokenWithNoPhoneNumber}`)
        .send({ otp: '000000' });

      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);
      expect(response.body.message).toContain('Invalid OTP');
    });
  });

  describe('DELETE /user/phone-verification', () => {
    it('should clear phone number when requested', async () => {
      const response = await request(app.getHttpServer())
        .delete('/user/phone-verification')
        .set('Authorization', `Bearer ${sessionTokenWithPhoneNumber}`);

      expect(response.statusCode).toEqual(HttpStatus.NO_CONTENT);
    });
  });
});
