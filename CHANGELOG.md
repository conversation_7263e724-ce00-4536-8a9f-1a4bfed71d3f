# Changelog

## [0.2.2](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/compare/v0.2.1...v0.2.2) (2025-07-09)

## [0.2.1](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/compare/v0.2.0...v0.2.1) (2025-07-08)

## [0.2.0](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/compare/v0.1.3...v0.2.0) (2025-06-24)

### Features

* **UPDATE:** fix .github module path ([#342](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/342)) ([bf8cc5a](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/bf8cc5ad98eec22df884ae6c81c0cdb54b650b52))

### Bug fixes

* **HOTFIX:** fix create-release step ([#340](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/340)) ([38f81ca](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/38f81ca6656a2af0e8355349938987bc77eda5ba))
* **HOTFIX:** fix fetch-tag ([#343](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/343)) ([d06a2ed](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/d06a2edf7ed6328dfaabc8ead3f19284170e63f6))
* **UPDATE:** update path-ignore ([#341](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/341)) ([9587895](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/9587895f7fcb101e830ddc414c1ca6d541e7c03a))

## [0.1.3](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/compare/v0.1.2...v0.1.3) (2025-05-14)

## [0.1.2](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/compare/v0.1.1...v0.1.2) (2025-04-29)

## [0.1.1](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/compare/v0.1.0...v0.1.1) (2025-04-15)

## 0.1.0 (2025-04-15)

### Features

* add email for assignee ([f2bc2c6](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/f2bc2c6af499822d49fdd32b56dd4667930437d2))
* add voting and stage workflows ([#243](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/243)) ([284f00b](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/284f00b695d1956856d08833db2f0400ac2dbc70))
* **api:** Introduce provider parameter for social account recovery ([65b0665](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/65b066592e3f06f565550122b9a8b7ec6fe96e95))
* **DEVOPS-102:** add test branch DEVOPS-102/delete-from-old-dev ([8e536a6](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/8e536a6888d6faa5b8e59e3e36da06deb31854a2))
* **DEVOPS-102:** delete  test branch DEVOPS-102/delete-from-old-dev ([9634514](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/96345144f491b0b98b5823e1b2e0a35a6475aa28))
* **DEVOPS-102:** delete workflows for old dev ([6e3bae3](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/6e3bae3d432c50fa8e4c57773867c3d6976462e4))
* **DEVOPS-34:** ref ingress url ([#89](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/89)) ([275acf6](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/275acf6dee0d8409dd0d9a908aee5357b665ab9a))
* **DEVOPS-46:** add Rollout Status and Pod Restarts Check ([#93](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/93)) ([d9eb913](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/d9eb913480a64bb44509562b0d561b38aaee1a22))
* **DEVOPS-56:** refactor containers resources ([#115](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/115)) ([a620959](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/a62095929445a2f3c0341a0ec90ce2cf00a62e3b))
* Documentation added for user roles ([bd0ebff](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/bd0ebff880052ce20b8af70d5badccb6d59ebc14))
* **DTCP-44:** update actions version ([#163](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/163)) ([cb3ffd0](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/cb3ffd0990b6420daa2e883240312e9937035659))
* **FP-381:** Enable sending invitations for new Grant Call Coordinators ([fc78814](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/fc7881451285bba7ef17d9ff184088ab52248184))
* **FP-382:** Add default port fallback for BACKEND_PORT ([097d36d](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/097d36db630d0b96b4b7e29019d88379293e6991))
* **FP-382:** Make backend port configurable via env var ([52990a8](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/52990a88874469b7dbed6115fc41fd80dc2e1597))
* **FP-433:** Include  in Grant Call API response and update tests ([a9bb862](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/a9bb862aa576ea5b46c8c96f0af8effcdb40911f))
* **Fp-464:** Implement user notification preferences management ([d8a5518](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/d8a55183c90f18929688dc386223945f642528dd))
* **FP-465:** Implement user name update endpoint ([0bbd1b0](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/0bbd1b00cd59dc98d0718bad45693ac1cd733363))
* **FP-466:** Implement account deletion and recovery ([f2908d1](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/f2908d19aed21cc77c665906413387726273928c))
* **FP-466:** Implement Delete Account endpoint with migration and role validation ([10c0fd5](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/10c0fd51e9b42ee6478591675e39f029edb27d60))
* **FP-466:** Reset emailVerified status on account recovery ([174bd4a](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/174bd4a2bd3e14d97cefa54bed7203f96d3c497a))
* **FP-469:** Integrate Google OAuth for User Authentication ([56bb34a](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/56bb34a8d6fe379b2329f3a56f36396f1df32a31))
* **FP-472:** Introduce DatabaseErrorCode enum for database error clarity ([634a2e2](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/634a2e2470afb69e2419e8e157fea8d135cb68f4))
* **GMp-587:** Enhance notification preferences endpoint response and add account deletion check ([9694d9d](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/9694d9d27971d8e31975b67214d3a8551f7f8d76))
* **GMP-587:** Enhance phone verification API responses ([ce702f9](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/ce702f9dd3eb317738c48e50dc789719e484efbc))
* **GMP-587:** Implement core Phone Number KYC functionality ([8eb0b55](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/8eb0b55b55e7e9d3696894478c2b04f33783b18a))
* **GMP-587:** Include phoneNumber in singin API response ([71f7020](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/71f7020fbb8378eb2b78027c2e36607150575026))
* **GMP-587:** Retrurn phone and isPhoneVerified properties with user response ([33859f0](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/33859f029927c590d18a7cb0523a7c95ff897a4b))
* **GMP-587:** update test preferences test cases to consider deleted account ([3b10c5e](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/3b10c5e710fff790fc1950dacd873e17487a872e))
* **GMP-635:** add new Pipelines ([bc988f5](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/bc988f55e0220be0d8b150ecb3e1be55087f520c))

### Bug fixes

* add required relations when getting previous status during application withdrawal ([#229](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/229)) ([1f28aeb](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/1f28aeb13f06d4e5f4e1a7ba3defbfae0a769983))
* Adjust Prettier annotations to match formatting rules ([74ecb44](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/74ecb442d0e37b5b8b031fdb5dd98f9a7df222ed))
* Change parameter decorator in recoverWallet endpoint to @Query ([a926845](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/a926845ecca7f283cbee222116fba16f71e23775))
* Changes: ([9f00a74](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/9f00a744fe272a185443c3722457febe7f80eda1))
* **docs:** Correct typo in social login response Swagger definition ([a07b0d7](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/a07b0d734cb38748ad2d9d428873f6254dfe044e))
* Ensure emailVerified is true when restoring soft-deleted user via social login ([3394602](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/339460292d0fb641409e0ee4dda75e77dc651474))
* **FP-464:** notification preferences typo, swagger doc ([9e331d1](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/9e331d1d6c065055ca4614512c8cfcb686cfbe98))
* **FP-464:** Resolve database enum typo causing validation issues ([16e0ccd](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/16e0ccda9b55ad0366d9da6083dedb890968bb32))
* **GMP-587:** Add missing PhoneVerification Modules ([7ccf68e](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/7ccf68ecae6b59fd726d9ffabc26238890a44668))
* **GMP-587:** Add missing SNSModule ([f953087](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/f95308731ca5622e9cff57e855de3c94ecca3317))
* **GMP-587:** dependencies issue ([9a28c21](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/9a28c2192f4c26be541723af84f822f9be045700))
* **GMP-772:** Enforce 10-min OTP resend cooldown  ([#226](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/226)) ([096bc24](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/096bc24bea35e126d2a700b06ff58bb062f3f8d8))
* **GMP-814:** update user display name retrieval in grant application service ([#227](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/227)) ([239018d](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/239018d8193baf3f562d1629f407ae2c44207431))
* **nest-cli:** Update mail assets paths after folder relocation ([847bdde](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/847bdde45ed2fccf5d9f5906d34f097122b7e6c5))
* override ports property in ci so it doesn't clash with app container ([#231](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/231)) ([a543d14](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/a543d1477df02f22bc425af7ae243e0c07ca3a9b))
* Patched e2e spec ([ba10c64](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/ba10c642cf32760f2a7afac3c9f3af6f7749183c))
* same as last ([f1456df](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/f1456dfc67263e3d2fa164115a73893d1f0912b6))
* **sns:** Correct AWS SNS region configuration ([2e17715](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/2e17715f3c12683d8c6cfb682a2cbd24df12ec99))
* Updated CORS methods to include PUT ([b75791d](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/b75791d2f1df574e2bdfd7bf8df555521694949b))

### Refactoring

* **GMP-587:** Improve error message for existing phone numbers ([1649002](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/1649002f4fa25c70c8c5e5a9f9c6d09b2fc78a8a))
* minor simplification ([06e6971](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/06e6971d22d0b9beee1acd7ef3716e7dc768ed18))
* Move the `prefixMessageToSign` method into a utils file. ([9af0657](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/9af0657c73dc395ac01d35275232e02cfdba051d))
* Remove deprecated CreateUser DTO ([1fd8a11](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/1fd8a118bef7a3fe4e68989da6af01f491706faf))
* **sns:** Remove explicit credentials config for dev testing ([ac2dd8c](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/ac2dd8c6483c5f768b2e3a44bd3805b435dc66d3))
* **social-login:** Remove request body data from social login endpoint, aligning with REST principles ([3b6dad8](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/3b6dad8c4aa64c58e7d6a6b632408311d0c48221))
* Update GrantProgram entity to use PrimaryGeneratedColumn for id ([7ba3bea](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/7ba3beaf753ad5771a4eaf3a7f37e8789081fa3e))

### Tests

* **FP-412:** Configure Jest coverage threshold to 70% ([4ff7e6d](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/4ff7e6db1362450d08f7309bac715eac776d5f84))
* **FP-412:** Fully tested core auth features, achieving coverage goal ([f201c7f](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/f201c7fb6991ca87f041c3d8e6e0cd1ecc6e5b01))
* **FP-412:** WIP - Implemented majority of auth unit tests ([9368c7d](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/9368c7dbf9aacc1a441a36ee95b5d422009bd734))
* **FP-413:** Fully tested core grant application features, achieving coverage goal ([cc4953f](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/cc4953f2205d58fd3d3d592d41a68fe3b8b56321))
* **FP-413:** WIP - Add extensive test coverage for grant application management ([efc1521](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/efc15215ee09e93e422f99b4d2dd07519a46ff52))
* **FP-414:** Fully tested core grant call features, achieving coverage goal ([fa8e61a](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/fa8e61acbf5454e54c9678632576495017eb435e))
* **FP-415:** Fully tested core grant application features, achieving coverage goal ([a952c60](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/a952c604c2c1711498947b06817a48af9c29b715))
* **FP-416:** Add tests for Mail module notification functions ([f95f504](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/f95f50442b34c9c75d0e3aab6ea5499cb3e9af96))
* **Fp-464:** add missing UserNotificationPreferences Entity to E2E test files ([24ae95c](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/24ae95cb5a16fd33239f34a0de79535655617bed))
* **Fp-464:** Add test cases to notification settings ([9de472c](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/9de472cd6c487ac3076e7e7c42c1305dc25920a9))
* **FP-464:** Enhance test coverage for Social Login and Notification Preferences modules ([897cee6](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/897cee62206c6c98b07ec96436bdcf9fa459b410))
* **Fp-464:** update test cases to validate NotificationPreferencesDto keys and values ([0afa244](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/0afa244d7bdbda4c08cf303be6e25a826963c7b2))
* **Fp-472:** Add more tests for social providers ([1122a99](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/1122a99f9143fb113891225c1b1ff9f2d3d34209))
* **FP-472:** Add unit tests for social login with LinkedIn, GitHub, Google, and Twitter ([39de8f8](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/39de8f8fe4c42fb19daa6296bb6cce1ec06592f2))
* **GMP-578:** Add tests for core Phone Number KYC functionality ([ffcd64f](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/ffcd64f12ac01f61ed1e06ad73dfe61c7fc9cbe6))

### Documentation

* **GMP-587:** Add comment explaining USE_LOCALSTACK variable ([5758587](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/5758587ea63d4413dfc9639f81abf63e3deb4c21))

### Chores

* Add GrantProgramModule to app.module.ts ([387d3ef](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/387d3ef2402d5b31e8313915d537facde749b1c4))
* Add recover wallet and update wallet endpoints ([3847406](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/38474063f9edc33b476dd4c9094b79276d6a9cc1))
* change to trigger deployment ([#206](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/206)) ([#208](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/issues/208)) ([5944682](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/5944682cd7009b8abb864f3e66ac343f4ce4eec0))
* **FP-381:** remove logs ([d29fca4](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/d29fca436c5b98c8888a450e5d0ce3c83c002824))
* **FP-412:** Remove unused commented code ([94dc5b2](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/94dc5b264f7f13d9ed58d47b970e1e2147cc17e9))
* **Fp-464:** comment failed test cases ([8c72a33](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/8c72a331bb8039f689014e7545c9dfe827d35eca))
* **FP-465:** add missing files ([3bd3dd4](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/3bd3dd4247b2ea584cdd8eecc2f2b29674ab765e))
* **FP-469:** Implement social login with Google, GitHub, LinkedIn, and Twitter ([ffa26d4](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/ffa26d47f36ada9eb2a0a39c82e4ab08871b7166))
* **FP-469:** linkedAccounts user table migration ([9864273](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/98642730e13ee6aebb64cb16b4cf1c3b5af6a9b3))
* **FP-472:** Remove commented code ([bb94dc1](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/bb94dc1ffffc511a14512af3459e78dfdf9f9464))
* **GMP-587:** remove unused authService from Phone module ([211ea6f](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/211ea6f94e804486553ae8827c5ca6646618158e))
* Refactor AuthService tests for better readability ([2b1d7f3](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/2b1d7f370836e82401cd2376d581ca1af5cac94d))
* Refactor AuthService to ensure unique wallet addresses ([806474f](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/806474f957bdcc019efcb313c9f1cbe95f867c71))
* Refactor grant program service to handle file uploads to S3 ([2f2dd0d](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/2f2dd0da880eb7619be68353ba454a415987efb8))
* Remove commented code ([d29bea6](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/d29bea63a3bad4396e04f5b290d4817b6581c508))
* Update auth module to use User entity instead of Auth entity ([50d96a4](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/50d96a486ecfbc584d70191b344516736a1cddb9))
* Update environment variables in .env.example ([1f84cc5](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/1f84cc5f187787fdc79334b34fa90ab85bf43aea))
* Update grant program controller and service ([93a90ff](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/93a90ff2c2055c5919c1aaf2ccd799c964315fb6))
* Update grant-program.service.spec.ts to fix syntax error ([c51cc45](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/c51cc45901eb954de19afffa1384f1b56c37477e))
* Update Swagger annotations in app.controller.ts ([e7064b5](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/e7064b50898df2035f3a9d5a0b71ff5888fa0f63))
* Update Swagger annotations in app.controller.ts ([adfeab5](https://github.com/Swiss-Digital-Assets-Institute/funding-platform-api/commit/adfeab5980598152fca95005729caffcaeff96d2))
