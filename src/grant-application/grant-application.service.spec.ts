import * as sanitizeDtoUtil from '../utils/sanitize.util';

import { CreateGrantApplicationDto, GetGrantApplicationsQueryDto } from './dto';
import { GrantApplicationStageCode, StageCode } from '../workflow/enums/stage-code.enum';
import { Test, TestingModule } from '@nestjs/testing';

import { BalanceAlertService } from '../balance-alerting/balance-alert.service';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { GrantApplication } from './entities/grant-application.entity';
import { GrantApplicationService } from './grant-application.service';
import { GrantCall } from '../grant-call/entities/grant-call.entity';
import { GrantCallMapper } from '../grant-call/grant-call.mapper';
import { GrantCategory } from '../grant-call/enums/grant-category.enum';
import { GrantProgram } from '../grant-program/entities/grant-program.entity';
import { HederaService } from '../hedera/hedera.service';
import { HttpService } from '@nestjs/axios';
import { StageDefinitionResponseDto } from '../workflow/dto/stage-definition.response.dto';
import { StageTransitionType } from '../workflow/enums/stage-transition-type.enum';
import { TopicId } from '@hashgraph/sdk';
import { User } from '../auth/entities/user.entity';
import { VotesService } from '../votes/votes.service';
import { VotingOutcomeService } from '../voting-outcome/voting-outcome.service';
import { WorkflowEntityType } from '../workflow/enums/workflow-entity-type.enum';
import { WorkflowService } from '../workflow/workflow.service';
import { WorkflowState } from '../workflow/entities/workflow-state.entity';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';
import { formatStageCode } from '../utils/formatting.util';
import { getRepositoryToken } from '@nestjs/typeorm';
import { NotFoundException } from '@nestjs/common';
import { GrantApplicationRepository } from './grant-application.repository';
import { NotificationsService } from '../notifications/notifications.service';

jest.mock('../utils/formatting.util', () => ({
  formatStageCode: jest.fn((code) => {
    if (code === StageCode.GA_SCREENING) return 'Screening Stage';
    if (code === StageCode.GA_QUALIFICATION) return 'Qualification Stage';
    if (code === StageCode.GC_FINAL_COMMUNITY_VOTING) return 'Final Voting Stage';
    if (code) return `Formatted ${code}`;
    return null;
  }),
}));

const mockApplicationRepoTx = {
  create: jest.fn(),
  save: jest.fn(),
  findOne: jest.fn(),
  update: jest.fn(),
  count: jest.fn(),
};
const mockGrantCallRepoTx = { findOne: jest.fn() };
const mockStepDefRepoTx = { findOneBy: jest.fn() };
const mockWfTemplateRepoTx = { findOneBy: jest.fn() };
const mockWfStateRepoTx = { create: jest.fn(), save: jest.fn() };
const mockUserRepoTx = { findOne: jest.fn(), findOneBy: jest.fn() };

const transactionalRepositoryMap = new Map<any, any>([
  [GrantApplication, mockApplicationRepoTx],
  [GrantCall, mockGrantCallRepoTx],
  [WorkflowStepDefinition, mockStepDefRepoTx],
  [WorkflowTemplate, mockWfTemplateRepoTx],
  [WorkflowState, mockWfStateRepoTx],
  [User, mockUserRepoTx],
]);

const mockTransactionalEntityManager = {
  getRepository: jest.fn((entity: any): any => {
    const repo = transactionalRepositoryMap.get(entity);
    if (repo) return repo;
    throw new Error(`Mock getRepository not configured for ${typeof entity === 'function' ? entity.name : entity}`);
  }),
  create: jest.fn((entityType, plainObject) => Object.assign(new entityType(), plainObject)),
  save: jest.fn((entity) => Promise.resolve(entity)),
};

const mockAuthRepositoryValue = { findOne: jest.fn(), findOneBy: jest.fn() };
const mockGrantApplicationRepository = {
  findOneById: jest.fn(),
  updateById: jest.fn(),
  transaction: jest.fn(),
  findDetailedById: jest.fn(),
  findOneForStatusUpdate: jest.fn(),
  findOneForTransition: jest.fn(),
  findOneForVotingResult: jest.fn(),
  findForVotingByGrantCallId: jest.fn(),
  findWithOnlyVotingTopics: jest.fn(),
  getFilteredApplications: jest.fn(),
};
const mockGrantCallMapperValue = { mapGrantCallToDetailDto: jest.fn(), mapGrantCallToBaseDto: jest.fn() };
const mockNotificationsServiceValue = {
  applicationCreated: jest.fn(),
  applicationTransitioned: jest.fn(),
  applicationStatusChange: jest.fn(),
};
const mockHederaServiceValue = {
  createTopicWithRetry: jest.fn(),
  submitMessageWithRetry: jest.fn(),
  getMessagesFromTopic: jest.fn(),
};
const mockWorkflowServiceValue = {
  initializeEntityState: jest.fn(),
  getStageDefinitionsForEntityType: jest.fn(),
  getStageSummariesWithCounts: jest.fn(),
  countChildEntities: jest.fn(),
  stageDefinitionToDto: jest.fn(),
};
const mockWorkflowTransitionServiceValue = {
  transitionSingleStateToNextStep: jest.fn(),
  updateStateStatus: jest.fn(),
};

const mockDataSourceValue = {
  createQueryRunner: jest.fn(() => ({})),
  manager: {
    transaction: jest.fn().mockImplementation(async (param1: any, param2?: any) => {
      const actualCallback = typeof param1 === 'function' ? param1 : param2;
      return actualCallback(mockTransactionalEntityManager);
    }),
  },
};

const mockVotesServiceValue = {
  canUserVoteForApplication: jest.fn(() => true),
};

jest.mock('../utils/sanitize.util.ts', () => ({
  sanitizeDto: jest.fn((dto) => dto),
}));

describe('GrantApplicationService', () => {
  let service: GrantApplicationService;
  let hederaService: typeof mockHederaServiceValue;
  let workflowService: typeof mockWorkflowServiceValue;

  const mockGrantCallOpen = {
    id: 1,
    name: 'Open Call',
    grantCallSlug: 'open-call',
    workflowState: { currentStepDefinition: { code: StageCode.GC_OPEN_FOR_APPLICATIONS } },
  } as GrantCall;

  const mockWorkflowTemplateApp = { id: 3, entityType: WorkflowEntityType.APPLICATION } as WorkflowTemplate;
  const mockStepDefScreening = { id: 101, code: StageCode.GA_SCREENING, name: 'Screening' } as WorkflowStepDefinition;
  const mockUserCreator = { id: 1, displayName: 'Applicant User', email: '<EMAIL>' } as User;
  const mockVotingOutcomeService = {
    getTotalKycVerifiedUsers: jest.fn(),
    prepareApplicationStageData: jest.fn(),
    buildScoresMapFromVoteRecords: jest.fn(),
    calculateGrantCallQuorum: jest.fn(),
    categorizeFinalVotingApps: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GrantApplicationService,
        { provide: GrantApplicationRepository, useValue: mockGrantApplicationRepository },
        ConfigService,
        {
          provide: HttpService,
          useValue: {
            get: jest.fn(),
          },
        },
        { provide: VotingOutcomeService, useValue: mockVotingOutcomeService },
        {
          provide: BalanceAlertService,
          useValue: {
            ensureSufficientSystemOperatorBalance: jest.fn().mockResolvedValue(undefined),
          },
        },

        { provide: getRepositoryToken(User), useValue: mockAuthRepositoryValue },
        {
          provide: getRepositoryToken(GrantCall),
          useValue: { findOne: jest.fn() },
        },
        { provide: GrantCallMapper, useValue: mockGrantCallMapperValue },

        { provide: NotificationsService, useValue: mockNotificationsServiceValue },
        { provide: HederaService, useValue: mockHederaServiceValue },
        { provide: WorkflowService, useValue: mockWorkflowServiceValue },
        { provide: WorkflowTransitionService, useValue: mockWorkflowTransitionServiceValue },
        { provide: DataSource, useValue: mockDataSourceValue },
        { provide: VotesService, useValue: mockVotesServiceValue },
      ],
    }).compile();

    service = module.get<GrantApplicationService>(GrantApplicationService);
    hederaService = module.get(HederaService);
    workflowService = module.get(WorkflowService);

    (sanitizeDtoUtil.sanitizeDto as jest.Mock).mockImplementation((dto) => dto);
  });

  describe('create', () => {
    let createDto: CreateGrantApplicationDto;
    const requestingUserId = 1;
    const topicIdFromString = TopicId.fromString('0.0.123');

    beforeEach(() => {
      createDto = {
        grantCallSlug: 'open-call',
        title: 'Test App',
        description: 'Test Desc',
        companyName: 'Test Co',
        companyCountry: 'Testland',
        contactFullName: 'Test Contact',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '123',
        categories: [GrantCategory.SUSTAINABILITY],
      };

      mockGrantCallRepoTx.findOne.mockResolvedValue(mockGrantCallOpen);
      mockWfTemplateRepoTx.findOneBy.mockResolvedValue(mockWorkflowTemplateApp);
      mockStepDefRepoTx.findOneBy.mockResolvedValue(mockStepDefScreening);
      mockWfStateRepoTx.create.mockImplementation((data) => ({ ...data, id: 201 }));
      mockWfStateRepoTx.save.mockImplementation((state) => Promise.resolve({ ...state, id: state.id ?? 201 }));
      mockHederaServiceValue.createTopicWithRetry.mockResolvedValue(topicIdFromString);
      mockApplicationRepoTx.create.mockImplementation(
        (data: Partial<GrantApplication>) =>
          ({
            id: 0,
            ...data,
            grantCall: data.grantCall,
            workflowState: {
              ...data.workflowState,
              currentStepDefinition: data.workflowState?.currentStepDefinition || mockStepDefScreening,
            } as WorkflowState,
            actionTopicId: topicIdFromString.toString(),
          }) as GrantApplication,
      );
      mockApplicationRepoTx.save.mockImplementation(async (app) => ({
        ...app,
        id: app.id === 0 ? 1001 : app.id,
      }));
      mockAuthRepositoryValue.findOne.mockResolvedValue(mockUserCreator);

      mockGrantApplicationRepository.transaction.mockClear();
      mockGrantApplicationRepository.transaction.mockImplementation((callback) =>
        callback(mockTransactionalEntityManager),
      );
    });

    it('should successfully create a grant application and anchor event', async () => {
      const result = await service.create(createDto, requestingUserId);

      expect(result).toEqual({ grantApplicationId: 1001 });
      expect(mockGrantApplicationRepository.transaction).toHaveBeenCalledTimes(1);

      expect(mockGrantCallRepoTx.findOne).toHaveBeenCalledWith({
        where: { grantCallSlug: createDto.grantCallSlug },
        relations: { grantProgram: true, workflowState: { currentStepDefinition: true } },
      });
      expect(mockWfTemplateRepoTx.findOneBy).toHaveBeenCalledWith({ entityType: WorkflowEntityType.APPLICATION });
      expect(mockStepDefRepoTx.findOneBy).toHaveBeenCalledWith({ code: StageCode.GA_SCREENING });
      expect(mockWfStateRepoTx.save).toHaveBeenCalled();
      expect(mockHederaServiceValue.createTopicWithRetry).toHaveBeenCalledTimes(1);
      expect(mockApplicationRepoTx.save).toHaveBeenCalledTimes(1);

      const savedAppArg = mockApplicationRepoTx.save.mock.calls[0][0];
      expect(savedAppArg.title).toEqual(createDto.title);

      expect(savedAppArg.grantCall.id).toEqual(mockGrantCallOpen.id);
      expect(savedAppArg.workflowState.currentStepDefinition.id).toEqual(mockStepDefScreening.id);
      expect(savedAppArg.actionTopicId).toEqual(topicIdFromString.toString());
      expect(mockAuthRepositoryValue.findOne).toHaveBeenCalledWith({ where: { id: requestingUserId } });
      expect(mockHederaServiceValue.submitMessageWithRetry).toHaveBeenCalledTimes(1);
      expect(mockHederaServiceValue.submitMessageWithRetry).toHaveBeenCalledWith('0.0.123', {
        previous_status: null,
        current_status: formatStageCode(StageCode.GA_SCREENING),
        who: mockUserCreator.displayName,
        reason: 'Application Created and Submitted',
      });
    });
  });

  describe('findGrantApplication', () => {
    const testApplicationId = 1;
    let mockFullApplicationEntity: GrantApplication;

    beforeEach(() => {
      const mockUserEntity = { id: 101, displayName: 'App Creator', email: '<EMAIL>' } as User;
      const mockGrantCallEntity = {
        id: 201,
        grantCallSlug: 'test-gc-for-app',
        name: 'Test GC',
        createdBy: { id: 501, displayName: 'GC Creator', email: '<EMAIL>' } as User,
        createdById: 501,
        stageSettings: [],
        distributionRules: [],
        workflowState: {
          currentStepDefinition: { code: StageCode.GC_OPEN_FOR_APPLICATIONS, id: 701 } as WorkflowStepDefinition,
        } as WorkflowState,
        grantProgram: {
          id: 301,
          grantProgramSlug: 'test-gp-slug',
          name: 'Associated Program',
          grantProgramCoordinator: { id: 401, displayName: 'Prog Coord', email: '<EMAIL>' } as User,
        } as GrantProgram,
      } as GrantCall;

      mockFullApplicationEntity = {
        id: testApplicationId,
        title: 'Full Detailed Application',
        description: 'Full Description',
        categories: [GrantCategory.CONSUMER_LOYALTY],
        companyName: 'Tech Solutions Inc.',
        companyCountry: 'Innovation Land',
        companyWebpage: 'http://techsolutions.inc',
        contactFullName: 'Techy McTester',
        contactEmail: '<EMAIL>',
        contactPhoneNumber: '555-TECH',
        actionTopicId: 'action-topic-789',
        votingTopicId: 'vote-topic-101',
        createdAt: new Date('2024-02-15T10:00:00Z'),
        updatedAt: new Date('2024-02-16T14:30:00Z'),
        createdById: mockUserEntity.id,
        createdBy: mockUserEntity,
        grantCall: mockGrantCallEntity,
        workflowState: {
          id: 901,
          status: WorkflowStatus.IN_PROGRESS,
          currentStepDefinitionId: 955,
          currentStepDefinition: {
            id: 955,
            code: StageCode.GA_SCREENING,
            name: 'Under Review',
          } as WorkflowStepDefinition,
        } as WorkflowState,
      } as unknown as GrantApplication;

      mockGrantApplicationRepository.findDetailedById.mockResolvedValue(mockFullApplicationEntity);
    });

    it('should successfully find and map the grant application', async () => {
      const result = await service.findGrantApplication(testApplicationId);

      expect(mockGrantApplicationRepository.findDetailedById).toHaveBeenCalledTimes(1);
      expect(mockGrantApplicationRepository.findDetailedById).toHaveBeenCalledWith(testApplicationId);

      expect(result).toBeDefined();
      expect(result.id).toEqual(testApplicationId);
      expect(result.title).toEqual('Full Detailed Application');
      expect(result.creator.id).toEqual(mockFullApplicationEntity.createdById);
      expect(result.creator.displayName).toEqual(mockFullApplicationEntity.createdBy.displayName);
      expect(result.status).toEqual(WorkflowStatus.IN_PROGRESS);
      expect(result.currentStage).toEqual(
        workflowService.stageDefinitionToDto(mockFullApplicationEntity.workflowState.currentStepDefinition),
      );
      expect(result.grantCallSlug).toEqual(mockFullApplicationEntity.grantCall.grantCallSlug);
      expect(result.createdAt).toEqual('2024-02-15T10:00:00.000Z');
      expect(result.description).toEqual('Full Description');
      expect(result.categories).toEqual([GrantCategory.CONSUMER_LOYALTY]);
      expect(result.companyName).toEqual('Tech Solutions Inc.');
      expect(result.contactEmail).toEqual('<EMAIL>');
    });
  });

  describe('getApplicationActivityLog', () => {
    const applicationId = 123;
    const mockActionTopicId = '0.0.777';

    const mockStageDefs: StageDefinitionResponseDto[] = [
      {
        id: 1,
        code: StageCode.GA_SCREENING,
        name: 'Screening Stage',
        position: 10,
        workflowTemplateId: 0,
        transitionType: StageTransitionType.MANUAL,
        isTerminal: false,
      },
      {
        id: 2,
        code: StageCode.GA_SCREENING,
        name: 'Review Stage',
        position: 20,
        workflowTemplateId: 0,
        transitionType: StageTransitionType.MANUAL,
        isTerminal: false,
      },
      {
        id: 3,
        code: StageCode.GA_FINAL_QUALIFICATION,
        name: 'Final Voting Stage',
        position: 30,
        workflowTemplateId: 0,
        transitionType: StageTransitionType.MANUAL,
        isTerminal: false,
      },
    ];

    const mockApplicationWithTopic = { id: applicationId, actionTopicId: mockActionTopicId };

    beforeEach(() => {
      workflowService.getStageDefinitionsForEntityType.mockResolvedValue(mockStageDefs);
      mockGrantApplicationRepository.findOneById.mockResolvedValue(mockApplicationWithTopic);
      hederaService.getMessagesFromTopic.mockResolvedValue([]);
    });

    it('should return structured activity log for an application with messages', async () => {
      const hederaMessages = [
        {
          consensus_timestamp: 1748250979.783347,
          message: JSON.stringify({
            previous_status: '',
            current_status: 'Screening Stage',
            who: 'User A',
            reason: 'Application Submitted',
          }),
        },
        {
          consensus_timestamp: 1748250980.123456,
          message: JSON.stringify({
            previous_status: 'Screening Stage',
            current_status: 'Review Stage',
            who: 'User B',
            reason: 'Moved to Review',
          }),
        },
      ];
      hederaService.getMessagesFromTopic.mockResolvedValue(hederaMessages);

      const result = await service.getApplicationActivityLog(applicationId);

      expect(result.applicationId).toEqual(applicationId);
      expect(workflowService.getStageDefinitionsForEntityType).toHaveBeenCalledWith(WorkflowEntityType.APPLICATION);
      expect(mockGrantApplicationRepository.findOneById).toHaveBeenCalledWith(applicationId);
      expect(hederaService.getMessagesFromTopic).toHaveBeenCalledWith(mockActionTopicId);

      expect(result.activityLog).toHaveLength(1);

      const screeningStageGroup = result.activityLog[0];
      expect(screeningStageGroup).toBeDefined();
      expect(screeningStageGroup.stageName).toEqual('Screening Stage');
      expect(screeningStageGroup.position).toEqual(10);
      expect(screeningStageGroup.activities).toHaveLength(2);
      expect(screeningStageGroup.activities[0]).toEqual(
        expect.objectContaining({
          fromStage: '',
          toStage: 'Screening Stage',
          who: 'User A',
          reason: 'Application Submitted',
          timestamp: '2025-05-26T09:16:19.783Z',
        }),
      );
      expect(screeningStageGroup.activities[1]).toEqual(
        expect.objectContaining({
          fromStage: 'Screening Stage',
          toStage: 'Review Stage',
          who: 'User B',
          reason: 'Moved to Review',
          timestamp: '2025-05-26T09:16:20.123Z',
        }),
      );

      const reviewStageGroup = result.activityLog.find((g) => g.stageName === 'Review Stage');
      expect(reviewStageGroup).toBeUndefined();
    });

    it('should handle messages correctly including sorting by timestamp (if present, otherwise order of messages)', async () => {
      const hederaMessages = [
        {
          consensus_timestamp: 1748250979.783347,
          message: JSON.stringify({
            previous_status: 'Screening Stage',
            current_status: 'Review Stage',
            who: 'User B',
            reason: 'Later Action on Screening',
          }),
        },
        {
          consensus_timestamp: 1748250980.123456,
          message: JSON.stringify({
            previous_status: 'Screening Stage',
            current_status: 'Review Stage',
            who: 'User A',
            reason: 'Earlier Action on Screening',
          }),
        },
      ];
      hederaService.getMessagesFromTopic.mockResolvedValue(hederaMessages);
      workflowService.getStageDefinitionsForEntityType.mockResolvedValue([mockStageDefs[0]]);

      const result = await service.getApplicationActivityLog(applicationId);
      const screeningStageLog = result.activityLog.find((log) => log.stageCode === GrantApplicationStageCode.SCREENING);
      expect(screeningStageLog?.activities[0].reason).toBe('Later Action on Screening');
      expect(screeningStageLog?.activities[1].reason).toBe('Earlier Action on Screening');
    });

    it('should return an empty activityLog if Hedera topic messages are empty', async () => {
      mockGrantApplicationRepository.findOneById.mockResolvedValue(mockApplicationWithTopic);
      hederaService.getMessagesFromTopic.mockResolvedValue([]);

      const result = await service.getApplicationActivityLog(applicationId);

      expect(result.applicationId).toEqual(applicationId);
      expect(result.activityLog).toHaveLength(0);
    });
  });

  describe('findGrantApplications', () => {
    let mockApplications: GrantApplication[];

    const mockUserApplicant = { id: 1, displayName: 'Applicant', email: '<EMAIL>' } as User;
    const mockUserOther = { id: 2, displayName: 'Other User', email: '<EMAIL>' } as User;

    const mockGrantCall1 = {
      id: 10,
      grantCallSlug: 'gc-slug-1',
      name: 'Grant Call 1',
      grantProgram: { grantProgramSlug: 'gp-slug-1' },
      workflowState: { currentStepDefinition: { code: StageCode.GC_COMMUNITY_VOTING } },
    } as GrantCall;
    const mockGrantCall2 = {
      id: 11,
      grantCallSlug: 'gc-slug-2',
      name: 'Grant Call 2',
      grantProgram: { grantProgramSlug: 'gp-slug-2' },
      workflowState: { currentStepDefinition: { code: StageCode.GC_ONBOARDING } },
    } as GrantCall;

    const mockStepDefScreening = { id: 101, code: StageCode.GA_SCREENING, name: 'Screening' } as WorkflowStepDefinition;
    const mockStepDefQualification = {
      id: 102,
      code: StageCode.GA_QUALIFICATION,
      name: 'Qualification',
    } as WorkflowStepDefinition;

    const baseMockApplication: Partial<GrantApplication> = {
      id: 0,
      title: 'Test App',
      description: 'Desc',
      categories: [GrantCategory.SUSTAINABILITY],
      companyName: 'TestCo',
      companyCountry: 'Testland',
      actionTopicId: 'topic1',
      votingTopicId: 'vote1',
      createdAt: new Date('2024-01-01T00:00:00.000Z'),
      updatedAt: new Date('2024-01-01T00:00:00.000Z'),
      contactFullName: 'Test Contact',
      contactEmail: '<EMAIL>',
      contactPhoneNumber: '123',
      workflowState: {
        status: WorkflowStatus.IN_PROGRESS,
        currentStepDefinitionId: mockStepDefScreening.id,
        currentStepDefinition: mockStepDefScreening,
      } as WorkflowState,
      createdById: 0,
    };

    beforeEach(() => {
      mockApplications = [
        {
          ...baseMockApplication,
          id: 1,
          createdById: mockUserApplicant.id,
          createdBy: mockUserApplicant,
          grantCall: mockGrantCall1,
          workflowState: {
            ...baseMockApplication.workflowState,
            currentStepDefinition: mockStepDefScreening,
            currentStepDefinitionId: mockStepDefScreening.id,
          },
          title: 'My App 1 (Screening)',
          createdAt: new Date('2024-01-02T00:00:00.000Z'),
          updatedAt: new Date('2024-01-03T00:00:00.000Z'),
        } as GrantApplication,
        {
          ...baseMockApplication,
          id: 2,
          createdById: mockUserApplicant.id,
          createdBy: mockUserApplicant,
          grantCall: mockGrantCall2,
          workflowState: {
            ...baseMockApplication.workflowState,
            currentStepDefinition: mockStepDefQualification,
            currentStepDefinitionId: mockStepDefQualification.id,
          },
          title: 'My App 2 (Qualification)',
          createdAt: new Date('2024-01-03T00:00:00.000Z'),
          updatedAt: new Date('2024-01-03T00:00:00.000Z'),
        } as GrantApplication,
        {
          ...baseMockApplication,
          id: 3,
          createdById: mockUserOther.id,
          createdBy: mockUserOther,
          grantCall: mockGrantCall1,
          workflowState: {
            ...baseMockApplication.workflowState,
            currentStepDefinition: mockStepDefScreening,
            currentStepDefinitionId: mockStepDefScreening.id,
          },
          title: 'Other User App (Screening)',
          createdAt: new Date('2024-01-01T00:00:00.000Z'),
          updatedAt: new Date('2024-01-03T00:00:00.000Z'),
        } as GrantApplication,
      ];

      mockGrantApplicationRepository.getFilteredApplications.mockResolvedValue(mockApplications);

      jest.spyOn(service as any, 'mapApplicationToFindBaseDto');
    });

    it('should return all applications', async () => {
      const query: GetGrantApplicationsQueryDto = {};
      const result = await service.findGrantApplications(query, null);

      expect(mockGrantApplicationRepository.getFilteredApplications).toHaveBeenCalledTimes(1);
      expect(result).toHaveLength(mockApplications.length);
      expect((service as any).mapApplicationToFindBaseDto).toHaveBeenCalledTimes(mockApplications.length);
      expect(result[0].title).toBe('My App 1 (Screening)');
    });
  });

  describe('updatePitchFileUrl', () => {
    const mockGrantApplication = {
      id: 1,
    };

    beforeAll(() => {
      mockGrantApplicationRepository.findOneById.mockResolvedValue(mockGrantApplication);
      mockGrantApplicationRepository.updateById.mockResolvedValue({ affected: 1 });
    });

    it('should update pitch file url', async () => {
      const updateResult = await service.updatePitchFileUrl(1, { pitchFileUrl: 'https://example.com' });
      expect(updateResult.affected).toEqual(1);
    });

    it('should throw NotFoundException when application is not found', async () => {
      mockGrantApplicationRepository.findOneById.mockResolvedValueOnce(null);

      await expect(service.updatePitchFileUrl(1, { pitchFileUrl: 'https://example.com' })).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
