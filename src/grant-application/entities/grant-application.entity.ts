import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { GrantCall } from '../../grant-call/entities/grant-call.entity';
import { GrantCategory } from '../../grant-call/enums/grant-category.enum';
import { User } from '../../auth/entities/user.entity';
import { Vote } from '../../votes/entities/vote.entity';
import { WorkflowState } from '../../workflow/entities/workflow-state.entity';

@Entity()
export class GrantApplication {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column()
  title: string;

  @Column()
  description: string;

  @Column()
  companyName: string;

  @Column()
  companyCountry: string;

  @Column({ type: 'enum', enum: GrantCategory, enumName: 'GrantCategory' })
  categories: GrantCategory[];

  @Column()
  contactFullName: string;

  @Column()
  contactEmail: string;

  @Column()
  contactPhoneNumber: string;

  @Column({ type: 'varchar', length: 255 })
  actionTopicId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  votingTopicId: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true })
  finalVotingTopicId: string | null;

  @Column({ type: 'int' })
  workflowStateId: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  pitchFileUrl: string | null;

  @Column({ type: 'int', nullable: false })
  createdById: number;

  @ManyToOne(() => User, {
    nullable: false,
    onDelete: 'RESTRICT',
    eager: false,
  })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @ManyToOne(() => GrantCall)
  grantCall: GrantCall;

  @OneToMany(() => Vote, (vote) => vote.grantApplication)
  votes: Vote[];

  @OneToOne(() => WorkflowState, {
    onDelete: 'RESTRICT',
    eager: false,
    cascade: true,
  })
  @JoinColumn({ name: 'workflowStateId' })
  workflowState: WorkflowState;

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date;
}
