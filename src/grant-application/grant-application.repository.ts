import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { GrantApplication } from './entities/grant-application.entity';
import { DeepPartial, EntityManager, In, Repository, UpdateResult } from 'typeorm';
import { dbSortableFields, GetGrantApplicationsQueryDto } from './dto';

@Injectable()
export class GrantApplicationRepository {
  private readonly logger = new Logger(GrantApplicationRepository.name);

  constructor(
    @InjectRepository(GrantApplication)
    private readonly repository: Repository<GrantApplication>,
  ) {}

  transaction<T>(runInTransaction: (entityManager: EntityManager) => Promise<T>): Promise<T> {
    return this.repository.manager.transaction(runInTransaction);
  }

  findOneById(id: number): Promise<GrantApplication> {
    return this.repository.findOne({
      where: {
        id,
      },
    });
  }

  findDetailedById(id: number): Promise<GrantApplication> {
    return this.repository.findOne({
      where: { id },
      relations: {
        createdBy: true,
        workflowState: {
          currentStepDefinition: true,
        },
        grantCall: {
          createdBy: true,
          stageSettings: { workflowStepDefinition: true },
          distributionRules: true,
          grantProgram: { grantProgramCoordinator: true },
          workflowState: { currentStepDefinition: true, workflowTemplate: { steps: true } },
        },
        votes: true,
      },
    });
  }

  findOneForLifecycle(id: number): Promise<GrantApplication> {
    return this.repository.findOne({
      where: { id },
      relations: {
        createdBy: true,
        grantCall: {
          grantProgram: true,
        },
        workflowState: { currentStepDefinition: true },
      },
    });
  }

  findForStatusUpdate(applicationIds: number[]) {
    return this.repository.find({
      relations: {
        createdBy: true,
        grantCall: {
          grantProgram: true,
        },
        workflowState: { currentStepDefinition: true },
      },
      where: { id: In(applicationIds) },
    });
  }

  findOneForVotingResult(id: number) {
    return this.repository.findOne({
      where: { id },
      relations: { grantCall: true, workflowState: true },
      select: {
        id: true,
        grantCall: { id: true },
        workflowState: { status: true, id: true },
      },
    });
  }

  findForVotingByGrantCallId(id: number): Promise<GrantApplication[]> {
    return this.repository.find({
      where: { grantCall: { id } },
      relations: {
        createdBy: true,
        votes: true,
      },
    });
  }

  getFilteredApplications(
    query: GetGrantApplicationsQueryDto,
    requestingUserId: number | null,
  ): Promise<GrantApplication[]> {
    const { grantCallSlug, stageCodes, statusCodes, myApplications, sort = 'createdAt', direction = 'DESC' } = query;

    const queryBuilder = this.repository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.createdBy', 'creatorUser')
      .leftJoinAndSelect('application.grantCall', 'grantCall')
      .leftJoinAndSelect('grantCall.grantProgram', 'grantProgram')
      .leftJoinAndSelect('application.workflowState', 'appWorkflowState')
      .leftJoinAndSelect('appWorkflowState.currentStepDefinition', 'appCurrentStepDef')
      .leftJoinAndSelect('appWorkflowState.workflowTemplate', 'appWorkflowTemplate')
      .leftJoinAndSelect('application.votes', 'votes');

    if (grantCallSlug) {
      queryBuilder.andWhere('grantCall.grantCallSlug = :grantCallSlug', { grantCallSlug });
    }

    if (myApplications) {
      if (!requestingUserId) {
        return Promise.resolve([]);
      }
      queryBuilder.andWhere('application.createdById = :requestingUserId', { requestingUserId });
    }

    if (stageCodes?.length) {
      queryBuilder.andWhere('appCurrentStepDef.code IN (:...stageCodes)', { stageCodes });
    }

    if (statusCodes?.length) {
      queryBuilder.andWhere('appWorkflowState.status IN (:...statusCodes)', { statusCodes });
    }

    const sortBy = dbSortableFields.includes(sort) ? sort : 'createdAt';
    queryBuilder.orderBy(`application.${sortBy}`, direction as 'ASC' | 'DESC');

    return queryBuilder.getMany();
  }

  updateById(id: number, updateData: DeepPartial<GrantApplication>): Promise<UpdateResult> {
    return this.repository.update({ id }, updateData);
  }
}
