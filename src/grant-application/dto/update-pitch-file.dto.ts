import { ApiPropertyOptional } from '@nestjs/swagger';
import { Allow, IsUrl, ValidateIf } from 'class-validator';

export class UpdatePitchFileDto {
  @ApiPropertyOptional({
    type: String,
    nullable: true,
    description:
      'Publicly accessible HTTP(s) URL for the applicant’s pitch file. Send `null` to clear an existing link.',
    example: 'https://example.com/pitch.pdf',
    format: 'uri',
  })
  @Allow(null)
  @ValidateIf((o) => o.pitchFileUrl !== null)
  @IsUrl({ protocols: ['https'] }, { message: 'Must be a valid http(s) URL' })
  pitchFileUrl: string | null;
}
