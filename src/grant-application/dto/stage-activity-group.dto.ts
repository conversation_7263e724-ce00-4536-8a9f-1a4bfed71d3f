import { ActivityLogEntryDto } from './activity-log-entry.dto';
import { ApiProperty } from '@nestjs/swagger';
import { GrantApplicationStageCode } from '../../workflow/enums/stage-code.enum';

export class StageActivityGroupDto {
  @ApiProperty({
    example: 'Screening',
    description: "The name of the stage these activities relate to (often as 'fromStage').",
  })
  stageName: string;

  @ApiProperty({
    enum: GrantApplicationStageCode,
    enumName: 'GrantApplicationStageCode',
    example: GrantApplicationStageCode.SCREENING,
    description: 'The code of the stage.',
  })
  stageCode: GrantApplicationStageCode;

  @ApiProperty({
    example: 10,
    description: 'Sequence number of the stage for ordering.',
  })
  position: number;

  @ApiProperty({
    type: [ActivityLogEntryDto],
    description: 'List of activities that occurred when the application was in or transitioned from this stage.',
  })
  activities: ActivityLogEntryDto[];
}
