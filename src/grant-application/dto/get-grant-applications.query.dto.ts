import { <PERSON><PERSON>y<PERSON>otEmpt<PERSON>, <PERSON><PERSON><PERSON>y, IsBoolean, <PERSON>In, <PERSON><PERSON>ptional, IsString } from 'class-validator';

import { ApiPropertyOptional } from '@nestjs/swagger';
import { FindOptionsOrderValue } from 'typeorm';
import { IsGrantApplicationStageCode } from '../../utils/validators/is-grant-application-stage-code.validator';
import { StageCode } from '../../workflow/enums/stage-code.enum';
import { TransformToApplicationStages } from '../../utils/transforms/to-application-stages.transform';
import { Type } from 'class-transformer';
import { WorkflowStatus } from '../../workflow/enums/workflow-status.enum';

export const dbSortableFields = ['createdAt', 'updatedAt'];
const memorySortableFields = ['ranking'];
const grantApplicationSortableFields: string[] = [...dbSortableFields, ...memorySortableFields];

export class GetGrantApplicationsQueryDto {
  @ApiPropertyOptional({
    description: 'Set to true to filter by applications created by the authenticated user. Requires authentication.',
    type: <PERSON><PERSON><PERSON>,
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  myApplications?: boolean;

  @ApiPropertyOptional({
    description:
      'Filter by Grant Application workflow stage codes (e.g., GA_SCREENING, GA_INTERVIEW). Must start with "GA_". Can be comma-separated.',
    example: `${StageCode.GA_SCREENING},${StageCode.GA_INTERVIEW}`,
    type: [String],
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @TransformToApplicationStages()
  @IsGrantApplicationStageCode({ each: true })
  stageCodes?: StageCode[];

  @ApiPropertyOptional({
    description: 'Filter by Grant Application workflow status (e.g., IN_PROGRESS, APPROVED).',
    example: `${WorkflowStatus.IN_PROGRESS},${WorkflowStatus.APPROVED}`,
    type: [String],
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @TransformToApplicationStages()
  statusCodes?: WorkflowStatus[];

  @ApiPropertyOptional({
    description: 'Filter by Grant Call slug.',
    example: 'research-grant-stellar-probes-xyz',
    type: String,
  })
  @IsOptional()
  @IsString()
  grantCallSlug?: string;

  @ApiPropertyOptional({
    name: 'sort',
    description: `Sort field. Allowed values: ${grantApplicationSortableFields.join(', ')}.`,
    enum: grantApplicationSortableFields,
    example: 'createdAt',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(grantApplicationSortableFields, {
    message: `Sort field must be one of: ${grantApplicationSortableFields.join(', ')}`,
  })
  sort?: string = 'createdAt';

  @ApiPropertyOptional({
    name: 'direction',
    description: 'The direction for sorting the results. Defaults to DESC (descending).',
    enum: ['ASC', 'DESC'],
    enumName: 'SortDirection',
    example: 'DESC',
    default: 'DESC',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  direction?: FindOptionsOrderValue = 'DESC';
}
