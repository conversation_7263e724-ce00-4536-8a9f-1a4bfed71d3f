import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import { GrantApplicationBaseDto } from './grant-application-base.dto';
import { StageDefinitionResponseDto } from '../../workflow/dto/stage-definition.response.dto';
import { UserDto } from '../../auth/dto';
import { VoteType } from '../../votes/dto/prepare-vote.dto';
import { WorkflowStatus } from '../../workflow/enums/workflow-status.enum';
import { ExposeForRoles } from '../../auth/decorators/response-by-role.decorator';
import { Role } from '../../auth/role.enum';

export class FindGrantApplicationResponseDto extends GrantApplicationBaseDto {
  @ApiProperty({
    example: 101,
    description: 'Unique ID of the Grant Application.',
  })
  id: number;

  @ApiProperty({
    description: 'Unique slug identifying the Grant Call.',
    example: 'grant-call-to-the-moon-hdoija',
  })
  grantCallSlug: string;

  @ApiProperty({
    description: 'Unique slug identifying the Grant Program.',
    example: 'grant-program-to-the-moon-hdoija',
  })
  grantProgramSlug: string;

  @ApiProperty({
    description: 'The current workflow status of the application (e.g., IN_PROGRESS, APPROVED).',
    enumName: 'WorkflowStatus',
    enum: WorkflowStatus,
    example: WorkflowStatus.IN_PROGRESS,
  })
  status: WorkflowStatus;

  @ApiProperty({
    type: UserDto,
    description: 'User who initially created the application',
  })
  creator: UserDto;

  @ApiProperty({
    type: StageDefinitionResponseDto,
    description: 'Current stage of the application',
  })
  currentStage: StageDefinitionResponseDto;

  @ApiProperty({
    example: '0.0.102736',
    description: 'Created action topic id.',
  })
  actionTopicId: string;

  @ApiPropertyOptional({
    example: '0.0.102736',
    nullable: true,
    description: 'Created vote topic id',
  })
  votingTopicId?: string | null;

  @ApiPropertyOptional({
    example: '0.0.102736',
    nullable: true,
    description: 'Final voting topic id (appears only after grant call moves to final voting stage)',
  })
  finalVotingTopicId?: string | null;

  @ExposeForRoles(Role.COORDINATOR)
  @ApiPropertyOptional({
    example: 10,
    nullable: true,
    description: 'Total number of votes cast **in favor** of this application during the current voting stage',
  })
  inFavorVoteCount?: number | null;

  @ExposeForRoles(Role.COORDINATOR)
  @ApiPropertyOptional({
    example: 10,
    nullable: true,
    description: 'Total number of votes cast **against** this application during the current voting stage',
  })
  againstVoteCount?: number | null;

  @ApiPropertyOptional({
    example: VoteType.IN_FAVOR,
    description: 'User vote for application',
  })
  currentUserVote?: VoteType;

  @ApiProperty({
    description: 'Timestamp when the application was created.',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Timestamp when the application was last updated.',
  })
  updatedAt: string;

  @ApiPropertyOptional({
    type: Boolean,
    description:
      'Indicates if the application meets the individual rules to pass its current voting stage (e.g., net score is above threshold). ' +
      'This is independent of the overall grant call quorum. ',
    example: true,
  })
  isVotingQualified?: boolean;

  @ApiPropertyOptional({
    type: String,
    description: 'A URL of the pitch file.',
    example: 'https://example.com/pitch.pdf',
  })
  pitchFileUrl?: string;
}
