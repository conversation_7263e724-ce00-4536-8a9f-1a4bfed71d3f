import { Body, Controller, Get, HttpCode, HttpStatus, Param, ParseIntPipe, Patch, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  CreateGrantApplicationDto,
  GrantApplicationDetailResponseDto,
  GetGrantApplicationsQueryDto,
  FindGrantApplicationResponseDto,
  FindGrantApplicationDetailsResponseDto,
} from './dto/index';
import { GrantApplicationService } from './grant-application.service';
import { StageDefinitionResponseDto } from '../workflow/dto/stage-definition.response.dto';
import { StageWithCountResponseDto } from '../workflow/dto/stage-with-count.response.dto';
import { Roles } from '../auth/decorators/roles.decorator';
import { UpdateStateStatusDto } from '../workflow/dto/update-state-status.dto';
import { UpdateStateStatusResponseDto } from '../workflow/dto/update-state-status-response.dto';
import { TransitionApplicationStageDto } from './dto/transition-application-stage.dto';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { ApplicationActivityLogResponseDto } from './dto/application-activity-log-response.dlt';
import { UpdatePitchFileDto } from './dto/update-pitch-file.dto';
import { ResponseByRole } from '../auth/decorators/response-by-role.decorator';
import { Role } from '../auth/role.enum';
import { Public } from '../auth/decorators/public.decorator';
import { LoggedInUser } from '../auth/decorators/logged-in-user.decorator';

@ApiTags('Grant Application')
@Controller('grant-application')
export class GrantApplicationController {
  constructor(private readonly service: GrantApplicationService) {}

  @ApiOperation({ summary: 'Get the defined workflow stage definitions for Grant Applications' })
  @ApiResponse({ status: 200, description: 'List of stage definitions.', type: [StageDefinitionResponseDto] })
  @Get('/stages')
  @Public()
  async getApplicationStageDefinitions(): Promise<StageDefinitionResponseDto[]> {
    return this.service.getApplicationStageDefinitions();
  }

  @ApiOperation({ summary: 'Get Grant Application workflow stages with counts of applications in each stage.' })
  @ApiResponse({ status: 200, type: [StageWithCountResponseDto] })
  @ApiBearerAuth()
  @Get('/stage-counts')
  getGlobalGrantCallStageCounts(): Promise<StageWithCountResponseDto[]> {
    return this.service.getApplicationStagesWithCounts();
  }

  @ApiOperation({ summary: 'Create a new grant application' })
  @ApiResponse({
    status: 201,
    type: GrantApplicationDetailResponseDto,
    description: 'The application has been successfully created.',
  })
  @ApiResponse({ status: 404, description: 'When the given grant call is not found.' })
  @ApiResponse({ status: 400, description: 'When the grant call is not open for applications.' })
  @ApiBearerAuth()
  @Post()
  @Roles(Role.COMMUNITY_MEMBER)
  create(
    @Body() createGrantApplication: CreateGrantApplicationDto,
    @LoggedInUser() loggedInUser: LoggedInUser,
  ): Promise<GrantApplicationDetailResponseDto> {
    return this.service.create(createGrantApplication, loggedInUser.id);
  }

  @ApiOperation({ summary: 'Fetch grant applications.' })
  @ApiResponse({
    status: 200,
    description: 'Returns a list of grant applications.',
    type: [FindGrantApplicationResponseDto],
  })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'If no valid session token is present.' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'In case of invalid query params.' })
  @ApiBearerAuth()
  @Get()
  @Public()
  getGrantApplications(
    @Query()
    filterOptions: GetGrantApplicationsQueryDto,
    @LoggedInUser() loggedInUser: LoggedInUser | null,
  ): Promise<FindGrantApplicationResponseDto[]> {
    return this.service.findGrantApplications(filterOptions, loggedInUser?.id);
  }

  @ApiOperation({ summary: 'Retrieve the grant application using the ID.' })
  @ApiResponse({
    status: 200,
    description: 'The grant application of the grant call has been retrieved successfully.',
    type: FindGrantApplicationDetailsResponseDto,
  })
  @ApiResponse({ status: 404, description: 'The grant application does not exist.' })
  @ApiResponse({ status: 403, description: 'If the authenticated user does not have the right permissions.' })
  @ApiParam({ name: 'id', description: 'ID of the grant application.', example: '1' })
  @ApiBearerAuth()
  @Get(':id')
  @ResponseByRole(FindGrantApplicationDetailsResponseDto)
  @Public()
  getGrantApplication(
    @Param('id') id: number,
    @LoggedInUser() loggedInUser: LoggedInUser | null,
  ): Promise<FindGrantApplicationDetailsResponseDto> {
    return this.service.findGrantApplication(id, loggedInUser?.id);
  }

  @ApiOperation({ summary: 'Get topic messages for a grant application' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved topic messages.',
    type: ApplicationActivityLogResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Grant application not found.' })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden. User is not a member of the grant call, grant application, or grant program coordinator to access topic messages.',
  })
  @Get(':id/feedback')
  @Public()
  async getApplicationActivityLog(@Param('id') applicationId: number): Promise<ApplicationActivityLogResponseDto> {
    return this.service.getApplicationActivityLog(applicationId);
  }

  @ApiOperation({ summary: "Update Grant Application's main status" })
  @ApiBody({ type: UpdateStateStatusDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Application status updated successfully.',
    type: UpdateStateStatusResponseDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input data.' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'User lacks permission for this status update.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Application not found.' })
  @Patch(':appId/status')
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  async updateApplicationStatus(
    @Param('appId') appId: number,
    @Body() body: UpdateStateStatusDto,
    @LoggedInUser() loggedInUser: LoggedInUser,
  ): Promise<UpdateStateStatusResponseDto> {
    return this.service.updateApplicationStatusForCoordinator(appId, body.status, body.reason, loggedInUser.id);
  }

  @Patch(':appId/withdraw')
  @ApiBearerAuth()
  @ApiBody({ type: TransitionApplicationStageDto })
  @Roles(Role.COMMUNITY_MEMBER)
  async withdrawApplication(
    @Param('appId') appId: number,
    @Body() body: TransitionApplicationStageDto,
    @LoggedInUser() loggedInUser: LoggedInUser,
  ): Promise<UpdateStateStatusResponseDto> {
    return this.service.withdrawApplication(appId, body.reason, loggedInUser.id);
  }

  @ApiOperation({ summary: 'Advance Grant Application to the next workflow stage' })
  @ApiBody({ type: TransitionApplicationStageDto, description: 'Requires reason for transition' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Application successfully transitioned to the next stage.',
    type: SingleTransitionResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid reason or Application not in a transitionable state.',
  })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'User lacks permission.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Application not found.' })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Transition failed due to workflow rules.',
  })
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @Post(':appId/transition')
  async transitionGrantApplication(
    @Param('appId') appId: number,
    @Body() { reason }: TransitionApplicationStageDto,
    @LoggedInUser() loggedInUser: LoggedInUser,
  ): Promise<SingleTransitionResponseDto> {
    return this.service.transitionSingleApplicationStage(appId, reason, loggedInUser.id);
  }

  @ApiOperation({ summary: 'Update public pitch file URL of grant application' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Pitch file URL updated successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Grant application not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid URL format',
  })
  @ApiParam({
    name: 'applicationId',
    type: Number,
    description: 'ID of the grant application',
    example: 123,
  })
  @ApiBody({
    type: UpdatePitchFileDto,
    description: 'Payload containing the Pitch file URL (or `null` to clear)',
  })
  @Patch(':applicationId/pitch-file')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  async updatePitchFile(@Param('applicationId', ParseIntPipe) applicationId: number, @Body() dto: UpdatePitchFileDto) {
    await this.service.updatePitchFileUrl(applicationId, dto);
  }
}
