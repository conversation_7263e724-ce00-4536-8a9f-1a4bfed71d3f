import {
  BadRequestException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager, In, Repository, UpdateResult } from 'typeorm';
import { User } from '../auth/entities/user.entity';
import { Role } from '../auth/role.enum';
import { GrantCall } from '../grant-call/entities/grant-call.entity';
import { sanitizeDto } from '../utils/sanitize.util';
import {
  ActivityLogEntryDto,
  ApplicationActivityLogResponseDto,
  CreateGrantApplicationDto,
  FindGrantApplicationDetailsResponseDto,
  FindGrantApplicationResponseDto,
  GetGrantApplicationsQueryDto,
  StageActivityGroupDto,
} from './dto';
import { GrantApplication } from './entities/grant-application.entity';
import { ConfigService } from '@nestjs/config';
import { HederaService } from '../hedera/hedera.service';
import { WorkflowService } from '../workflow/workflow.service';
import { StageDefinitionResponseDto } from '../workflow/dto/stage-definition.response.dto';
import { WorkflowEntityType } from '../workflow/enums/workflow-entity-type.enum';
import { StageWithCountResponseDto } from '../workflow/dto/stage-with-count.response.dto';
import { WorkflowState } from '../workflow/entities/workflow-state.entity';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { GrantApplicationStageCode, StageCode } from '../workflow/enums/stage-code.enum';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';
import { formatStageCode } from '../utils/formatting.util';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { UpdateStateStatusResponseDto } from '../workflow/dto/update-state-status-response.dto';
import { VotingType } from '../votes/entities/vote.entity';
import { VotesService } from '../votes/votes.service';
import { VotingOutcomeService } from '../voting-outcome/voting-outcome.service';
import { ApplicationScoresMap, CategorizedVotingOutcome } from '../voting-outcome/types/voting-service.types';
import { GrantApplicationWithVotingQualificationStatus } from './grant-application.service.types';
import { VoteType } from '../votes/dto/prepare-vote.dto';
import { UpdatePitchFileDto } from './dto/update-pitch-file.dto';
import { GrantApplicationRepository } from './grant-application.repository';
import { NotificationsService } from '../notifications/notifications.service';

@Injectable()
export class GrantApplicationService {
  private readonly logger = new Logger(GrantApplicationService.name);
  private readonly maxHederaMessagePayloadLength: number;

  constructor(
    private readonly grantApplicationRepository: GrantApplicationRepository,
    @InjectRepository(User)
    private readonly authRepository: Repository<User>,
    @InjectRepository(GrantCall)
    private readonly grantCallRepository: Repository<GrantCall>,
    private readonly notificationsService: NotificationsService,
    private readonly configService: ConfigService,
    private readonly hederaService: HederaService,
    private readonly workflowService: WorkflowService,
    private readonly workflowTransitionService: WorkflowTransitionService,
    private readonly dataSource: DataSource,
    private readonly votesService: VotesService,
    @Inject(forwardRef(() => VotingOutcomeService))
    private readonly votingOutcomeService: VotingOutcomeService,
  ) {
    this.maxHederaMessagePayloadLength = this.configService.get<number>('HEDERA_MESSAGE_PAYLOAD_LIMIT', 1000);
  }

  async create(
    createApplicationDto: CreateGrantApplicationDto,
    requestingUserId: number,
  ): Promise<{ grantApplicationId: number }> {
    const sanitizedDto = sanitizeDto(createApplicationDto);

    return this.grantApplicationRepository.transaction(async (transactionalEntityManager) => {
      const grantCallRepo = transactionalEntityManager.getRepository(GrantCall);
      const grantApplicationRepo = transactionalEntityManager.getRepository(GrantApplication);
      const stepDefRepo = transactionalEntityManager.getRepository(WorkflowStepDefinition);
      const wfTemplateRepo = transactionalEntityManager.getRepository(WorkflowTemplate);
      const wfStateRepo = transactionalEntityManager.getRepository(WorkflowState);

      const grantCall = await grantCallRepo.findOne({
        where: { grantCallSlug: sanitizedDto.grantCallSlug },
        relations: { grantProgram: true, workflowState: { currentStepDefinition: true } },
      });

      if (!grantCall) {
        throw new NotFoundException(`Grant Call with slug "${sanitizedDto.grantCallSlug}" not found.`);
      }

      if (grantCall.workflowState?.currentStepDefinition?.code !== StageCode.GC_OPEN_FOR_APPLICATIONS) {
        throw new BadRequestException(
          `Cannot create application: Grant Call "${grantCall.name}" is not open for applications.`,
        );
      }

      const workflowTemplate = await wfTemplateRepo.findOneBy({ entityType: WorkflowEntityType.APPLICATION });
      const defaultStepDef = await stepDefRepo.findOneBy({ code: StageCode.GA_SCREENING });

      const initialWorkflowState = wfStateRepo.create({
        workflowTemplate: workflowTemplate,
        currentStepDefinition: defaultStepDef,
        currentStepDefinitionId: defaultStepDef.id,
        currentStepTransitionedAt: new Date(),
        currentStepEndsAt: null,
      });

      await wfStateRepo.save(initialWorkflowState);

      let actionTopicId: string;

      try {
        const topic = await this.hederaService.createTopicWithRetry();

        actionTopicId = topic.toString();
      } catch (hederaError) {
        this.logger.error(`Failed to create Hedera topic for new application: ${hederaError.message}`, hederaError);

        throw new InternalServerErrorException('Failed to create necessary DLT topic.');
      }

      const {
        title,
        description,
        companyName,
        companyCountry,
        contactFullName,
        contactEmail,
        contactPhoneNumber,
        categories,
      } = sanitizedDto;

      let application = grantApplicationRepo.create({
        title,
        description,
        companyName,
        companyCountry,
        categories,
        contactFullName,
        contactEmail,
        contactPhoneNumber,
        grantCall,
        workflowState: initialWorkflowState,
        actionTopicId,
        createdById: requestingUserId,
      });

      application = await grantApplicationRepo.save(application);

      await this.anchorTransitionEvent(
        application,
        'Application Created and Submitted',
        defaultStepDef.code,
        requestingUserId,
        null,
      );

      const user = await this.authRepository.findOne({
        where: {
          id: requestingUserId,
        },
      });

      await this.notificationsService.applicationCreated(user, application);

      return {
        grantApplicationId: application.id,
      };
    });
  }

  async transitionSingleApplicationStage(
    applicationId: number,
    reason: string,
    userId: number,
  ): Promise<SingleTransitionResponseDto> {
    const application = await this.fetchApplicationForLifecycle(applicationId);

    return this.dataSource.manager.transaction(async (transactionalEntityManager) => {
      const previousStepDef = application.workflowState.currentStepDefinition;

      const transitionResultDto = await this.workflowTransitionService.transitionSingleStateToNextStep(
        WorkflowEntityType.APPLICATION,
        application.id,
        transactionalEntityManager,
      );

      await this.anchorTransitionEvent(
        application,
        reason,
        transitionResultDto.stepDefinitionCode,
        userId,
        previousStepDef.code,
      );

      await this.notificationsService.applicationTransitioned(application, transitionResultDto.stepDefinitionCode);

      return transitionResultDto;
    });
  }

  async updateApplicationStatusForCoordinator(
    applicationId: number,
    newStatus: WorkflowStatus,
    reason: string | undefined,
    coordinatorUserId: number,
  ): Promise<UpdateStateStatusResponseDto> {
    const application = await this.fetchApplicationForLifecycle(applicationId);

    if (newStatus === WorkflowStatus.WITHDRAWN) {
      throw new BadRequestException(
        `Coordinator cannot change status to '${WorkflowStatus.WITHDRAWN}'. Application owners should use the dedicated withdraw endpoint.`,
      );
    }

    return this.performStatusUpdateAndNotifications(application, newStatus, reason, coordinatorUserId);
  }

  async withdrawApplication(
    applicationId: number,
    reason: string | undefined,
    requestingUserId: number,
  ): Promise<UpdateStateStatusResponseDto> {
    const application = await this.fetchApplicationForLifecycle(applicationId);

    if (application.createdById !== requestingUserId) {
      throw new ForbiddenException('You do not have permission to withdraw this application.');
    }

    return this.performStatusUpdateAndNotifications(application, WorkflowStatus.WITHDRAWN, reason, requestingUserId);
  }

  async findGrantApplications(
    query: GetGrantApplicationsQueryDto,
    requestingUserId: number | null,
  ): Promise<FindGrantApplicationResponseDto[]> {
    const { grantCallSlug, sort = 'createdAt' } = query;

    const [initialApplications, grantCall] = await Promise.all([
      this.grantApplicationRepository.getFilteredApplications(query, requestingUserId),
      grantCallSlug && sort === 'ranking'
        ? this.grantCallRepository.findOne({
            where: { grantCallSlug },
            select: { id: true, workflowState: { id: true, currentStepDefinition: { code: true } } },
            relations: { workflowState: { currentStepDefinition: true } },
          })
        : Promise.resolve(null),
    ]);

    if (!initialApplications.length) {
      return [];
    }

    let applications: GrantApplicationWithVotingQualificationStatus[] = initialApplications;

    const user = await (requestingUserId ? this.authRepository.findOneBy({ id: requestingUserId }) : null);
    const userWalletSet: Set<string> = new Set<string>(user?.addresses ?? []);
    const totalKycVerifiedUsers = await this.votingOutcomeService.getTotalKycVerifiedUsers();

    const allVoteRecords = applications.flatMap((app) =>
      (app.votes ?? []).map((vote) => ({
        ...vote,
        grantApplication: { id: app.id } as GrantApplication,
      })),
    );

    const applicationScoresMap = VotingOutcomeService.buildScoresMapFromVoteRecords(allVoteRecords);
    const grantCallStage = grantCall?.workflowState?.currentStepDefinition?.code;

    if (
      user?.role === Role.COORDINATOR &&
      query.sort === 'ranking' &&
      [StageCode.GC_COMMUNITY_VOTING, StageCode.GC_FINAL_COMMUNITY_VOTING, StageCode.GC_FINALIZED].includes(
        grantCallStage,
      )
    ) {
      applications = await this.getRankedFinalApplicationsList(
        applications,
        applicationScoresMap,
        totalKycVerifiedUsers,
        grantCallStage === StageCode.GC_COMMUNITY_VOTING
          ? VotingType.COMMUNITY_VOTING
          : VotingType.FINAL_COMMUNITY_VOTING,
      );
    }

    return applications.map((app) => this.mapApplicationToFindBaseDto(app, userWalletSet));
  }

  async getGrantApplicationById(applicationId: number): Promise<GrantApplication> {
    const application = await this.grantApplicationRepository.findDetailedById(applicationId);

    if (!application) {
      this.logger.warn(`Grant Application with ID ${applicationId} not found.`);

      throw new NotFoundException(`Grant Application with ID ${applicationId} not found.`);
    }

    return application;
  }

  async findGrantApplication(applicationId: number, userId?: number) {
    const application = await this.getGrantApplicationById(applicationId);
    const user = await this.authRepository.findOneBy({ id: userId });

    return this.mapApplicationToDetailsDto(application, user);
  }

  async getApplicationActivityLog(applicationId: number): Promise<ApplicationActivityLogResponseDto> {
    const [applicationStageDefinitions, topicMessages] = await Promise.all([
      this.getApplicationStageDefinitions(),
      this.getApplicationTopicMessages(applicationId),
    ]);

    if (!applicationStageDefinitions || applicationStageDefinitions.length === 0) {
      this.logger.warn(`No application stage definitions found for activity log generation.`);

      return { applicationId, activityLog: [] };
    }

    const activitiesGroupedByFromStage = new Map<string, ActivityLogEntryDto[]>();

    topicMessages.forEach((msg) => {
      const fromStage = !msg.fromStage || msg.fromStage === '' ? 'InitialCreation' : msg.fromStage;

      if (!activitiesGroupedByFromStage.has(fromStage)) {
        activitiesGroupedByFromStage.set(fromStage, []);
      }

      activitiesGroupedByFromStage.get(fromStage).push(msg);
    });

    const structuredActivityLog: StageActivityGroupDto[] = applicationStageDefinitions
      .map((stageDef) => {
        const activities = activitiesGroupedByFromStage.get(stageDef.name) || [];

        if (stageDef.position === applicationStageDefinitions[0].position) {
          const initialActivities = activitiesGroupedByFromStage.get('InitialCreation') || [];
          activities.unshift(...initialActivities);
        }

        return {
          stageName: stageDef.name,
          stageCode: stageDef.code as unknown as GrantApplicationStageCode,
          position: stageDef.position,
          activities,
        };
      })
      .filter((group) => group.activities.length > 0)
      .sort((a, b) => a.position - b.position);

    return {
      applicationId,
      activityLog: structuredActivityLog,
    };
  }

  async getApplicationStageDefinitions(): Promise<StageDefinitionResponseDto[]> {
    return await this.workflowService.getStageDefinitionsForEntityType(WorkflowEntityType.APPLICATION);
  }

  async getApplicationStagesWithCounts(): Promise<StageWithCountResponseDto[]> {
    return await this.workflowService.getStageSummariesWithCounts(WorkflowEntityType.APPLICATION);
  }

  async bulkCreateApplicationVotingTopicId(
    applications: GrantApplication[],
    votingType: VotingType,
    transactionalEntityManager: EntityManager,
  ) {
    const topicIdPropertyMap: Record<VotingType, keyof Pick<GrantApplication, 'votingTopicId' | 'finalVotingTopicId'>> =
      {
        [VotingType.COMMUNITY_VOTING]: 'votingTopicId',
        [VotingType.FINAL_COMMUNITY_VOTING]: 'finalVotingTopicId',
      };
    const topicIdProperty = topicIdPropertyMap[votingType];
    const applicationsWithTopicId: { id: number; topicId: string }[] = [];

    for (const application of applications) {
      if (application[topicIdProperty]) {
        applicationsWithTopicId.push({
          id: application.id,
          topicId: application[topicIdProperty] as string,
        });

        continue;
      }

      this.logger.warn(`Application (ID: ${application.id}) has no ${topicIdProperty}.`);

      try {
        const newVoteTopicId = await this.hederaService.createTopicWithRetry();
        this.logger.log(`Created new voteTopicId: ${newVoteTopicId} for Application (ID: ${application.id})`);

        await transactionalEntityManager.getRepository(GrantApplication).update(
          {
            id: application.id,
          },
          {
            [topicIdProperty]: newVoteTopicId.toString(),
          },
        );

        applicationsWithTopicId.push({
          id: application.id,
          topicId: newVoteTopicId.toString(),
        });
      } catch (error) {
        this.logger.error(`Failed to create or save voteTopicId for Application (ID: ${application.id})`, error.stack);

        throw new InternalServerErrorException(`Failed to establish voting topic for application ${application.id}`);
      }
    }

    return applicationsWithTopicId;
  }

  findForVotingByGrantCallId(grantCallId: number): Promise<GrantApplication[]> {
    return this.grantApplicationRepository.findForVotingByGrantCallId(grantCallId);
  }

  findOneForVotingResult(applicationId: number): Promise<GrantApplication> {
    return this.grantApplicationRepository.findOneForVotingResult(applicationId);
  }

  private async getRankedFinalApplicationsList(
    applications: GrantApplication[],
    allAppScoresMap: ApplicationScoresMap,
    totalKycVerifiedUsers: number,
    votingType: VotingType,
  ): Promise<GrantApplicationWithVotingQualificationStatus[]> {
    const grantCallId = applications[0].grantCall.id;

    const quorumMet = await this.votingOutcomeService.calculateGrantCallQuorum(
      grantCallId,
      votingType,
      totalKycVerifiedUsers,
    );

    const allProcessedAppData = applications.map((app) =>
      this.votingOutcomeService.prepareApplicationStageData(app, totalKycVerifiedUsers, votingType, allAppScoresMap),
    );

    const { qualifiedApplications: qualifiedApplicationsWithVotingInfo } =
      votingType === VotingType.COMMUNITY_VOTING
        ? this.votingOutcomeService.categorizeCommunityVotingApps(allProcessedAppData, quorumMet)
        : this.votingOutcomeService.categorizeFinalVotingApps(allProcessedAppData, quorumMet);

    const qualifiedIdsSet = new Set(qualifiedApplicationsWithVotingInfo.map((application) => application.id));
    const applicationMap = new Map(applications.map((app) => [app.id, app]));

    const qualifiedApplications = qualifiedApplicationsWithVotingInfo.map((application) => {
      return {
        ...applicationMap.get(application.id),
        isVotingQualified: true,
      };
    });
    const otherApplications = applications
      .filter((app) => !qualifiedIdsSet.has(app.id))
      .map((application) => {
        return {
          ...applicationMap.get(application.id),
          isVotingQualified: false,
        };
      });

    return [...qualifiedApplications, ...otherApplications];
  }

  private async fetchApplicationForLifecycle(applicationId: number): Promise<GrantApplication> {
    const application = await this.grantApplicationRepository.findOneForLifecycle(applicationId);

    if (!application) {
      throw new NotFoundException('Grant application not found');
    }

    return application;
  }

  private async performStatusUpdateAndNotifications(
    application: GrantApplication,
    newStatus: WorkflowStatus,
    reason: string | undefined,
    userId: number,
  ): Promise<UpdateStateStatusResponseDto> {
    return this.dataSource.manager.transaction(async (transactionalEntityManager) => {
      await this.workflowTransitionService.updateStateStatus(
        WorkflowEntityType.APPLICATION,
        application.id,
        newStatus,
        transactionalEntityManager,
      );

      const maxReasonLength = await this.anchorTransitionEvent(
        application,
        reason,
        newStatus,
        userId,
        application.workflowState.currentStepDefinition.code,
      );

      await this.notificationsService.applicationStatusChange(
        application,
        newStatus,
        application.workflowState.currentStepDefinition.code,
      );

      this.logger.log(`Updated MAIN status for App ${application.id} to ${newStatus}`);

      return {
        maxReasonLength,
      };
    });
  }

  private async getApplicationTopicMessages(applicationId: number): Promise<ActivityLogEntryDto[]> {
    const application = await this.grantApplicationRepository.findOneById(applicationId);

    if (!application) {
      throw new NotFoundException(`Grant Application with ID ${applicationId} not found.`);
    }

    const rawHederaMessages = await this.hederaService.getMessagesFromTopic(application.actionTopicId);

    return rawHederaMessages
      .filter((msg) => typeof msg.message === 'string')
      .map((decodedMessage) => {
        const hederaTimestamp = new Date(decodedMessage.consensus_timestamp * 1000).toISOString();

        try {
          const parsedData = JSON.parse(decodedMessage.message);

          return {
            fromStage: parsedData.previous_status ?? null,
            toStage: parsedData.current_status,
            who: parsedData.who,
            reason: parsedData.reason,
            timestamp: hederaTimestamp,
          };
        } catch (jsonParseError) {
          this.logger.error(
            `Error parsing JSON for app ${applicationId}: ${jsonParseError.message}`,
            `Raw: ${decodedMessage}`,
          );

          return {
            toStage: 'Unparseable Log Entry',
            reason: `Malformed data: ${String(decodedMessage).substring(0, 100)}...`,
            who: '',
            timestamp: hederaTimestamp,
          };
        }
      })
      .sort(
        (a, b) =>
          (a.timestamp ? new Date(a.timestamp).getTime() : 0) - (b.timestamp ? new Date(b.timestamp).getTime() : 0),
      );
  }

  private mapApplicationToFindBaseDto(
    application: GrantApplicationWithVotingQualificationStatus,
    userWalletSet: Set<string>,
  ): FindGrantApplicationResponseDto {
    const {
      id,
      grantCall,
      title,
      companyName,
      description,
      companyCountry,
      categories,
      workflowState,
      actionTopicId,
      votingTopicId,
      finalVotingTopicId,
      votes,
      createdAt,
      updatedAt,
      contactFullName,
      contactEmail,
      contactPhoneNumber,
      createdBy,
      isVotingQualified,
      pitchFileUrl,
    } = application;

    const { id: createdById, displayName, email } = createdBy;

    let currentUserVote: VoteType | undefined = undefined;
    let inFavorVoteCount: number = 0;
    let againstVoteCount: number = 0;

    const currentStageCode = workflowState.currentStepDefinition.code;
    const isCommunityVotingStage = currentStageCode === StageCode.GA_QUALIFICATION;
    const isFinalVotingStage = currentStageCode === StageCode.GA_FINAL_QUALIFICATION;

    if ((isCommunityVotingStage || isFinalVotingStage) && votes?.length) {
      const votingType = isCommunityVotingStage ? VotingType.COMMUNITY_VOTING : VotingType.FINAL_COMMUNITY_VOTING;
      const vote = votes.find((v) => v.voteType === votingType);

      if (vote) {
        inFavorVoteCount = vote.inFavorVotes;
        againstVoteCount = vote.againstVotes;
        if (userWalletSet.size > 0) {
          if (vote.walletsInFavor.some((w) => userWalletSet.has(w))) {
            currentUserVote = VoteType.IN_FAVOR;
          } else if (vote.walletsAgainst.some((w) => userWalletSet.has(w))) {
            currentUserVote = VoteType.AGAINST;
          }
        }
      }
    }

    return {
      id,
      title,
      description,
      categories,
      companyName,
      companyCountry,
      actionTopicId,
      votingTopicId,
      finalVotingTopicId,
      inFavorVoteCount,
      againstVoteCount,
      currentUserVote,
      status: workflowState.status,
      createdAt: createdAt.toISOString(),
      updatedAt: updatedAt.toISOString(),
      contactFullName,
      contactEmail,
      contactPhoneNumber,
      grantCallSlug: grantCall.grantCallSlug,
      grantProgramSlug: grantCall.grantProgram.grantProgramSlug,
      creator: { id: createdById, displayName, email },
      currentStage: this.workflowService.stageDefinitionToDto(workflowState.currentStepDefinition),
      isVotingQualified,
      pitchFileUrl,
    };
  }

  private async mapApplicationToDetailsDto(
    application: GrantApplication,
    user?: User,
  ): Promise<FindGrantApplicationDetailsResponseDto> {
    return {
      ...this.mapApplicationToFindBaseDto(application, new Set(user?.addresses ?? [])),
      isAbleToVote: this.votesService.canUserVoteForApplication(application, user),
    };
  }

  private async anchorTransitionEvent(
    application: Pick<GrantApplication, 'id' | 'actionTopicId'>,
    reason: string,
    newStepCode: StageCode | WorkflowStatus,
    userId: number | null,
    previousStepCode?: StageCode | null,
  ) {
    if (!application.actionTopicId) {
      this.logger.warn(`DLT Anchoring skipped for App ${application.id} - no actionTopicId defined.`);
      return;
    }

    try {
      let displayName = 'Grant Management Platform';

      if (userId) {
        const creator = await this.authRepository.findOne({ where: { id: userId } });

        displayName = creator.displayName;
      }

      const baseMessagePayload = {
        previous_status: previousStepCode ? formatStageCode(previousStepCode) : null,
        current_status: newStepCode ? formatStageCode(newStepCode) : null,
        who: displayName,
        reason: '',
      };

      const staticPayloadLength = JSON.stringify(baseMessagePayload).length;
      const maxReasonLength = this.maxHederaMessagePayloadLength - staticPayloadLength;

      this.validateReasonLength(reason, maxReasonLength);

      const messagePayload = { ...baseMessagePayload, reason };

      await this.hederaService.submitMessageWithRetry(application.actionTopicId, messagePayload);

      this.logger.log(
        `Anchored App ${application.id} stage transition to topic ${application.actionTopicId}: ${previousStepCode} -> ${newStepCode}`,
      );

      return maxReasonLength;
    } catch (error) {
      this.logger.error(
        `Error anchoring App ${application.id} stage change (Topic ${application.actionTopicId}):`,
        error,
      );

      throw new UnprocessableEntityException(`Failed to anchor stage transition to DLT: ${error.message}`);
    }
  }

  private validateReasonLength(reason: string, maxReasonLength: number): void {
    if (reason && reason.length > maxReasonLength) {
      throw new BadRequestException(`reason is too long. Maximum allowed characters: ${maxReasonLength}.`);
    }
  }

  async updatePitchFileUrl(applicationId: number, dto: UpdatePitchFileDto): Promise<UpdateResult> {
    const grantApplication = await this.grantApplicationRepository.findOneById(applicationId);

    if (!grantApplication) {
      throw new NotFoundException('Grant application not found');
    }

    const updateResult = await this.grantApplicationRepository.updateById(applicationId, dto);

    if (updateResult.affected !== 1) {
      throw new InternalServerErrorException('Failed to update pitch file URL');
    }

    return updateResult;
  }

  async anchorVotingStartedEvent(
    applicationIds: number[],
    votingType: VotingType,
    transactionalEntityManager: EntityManager,
  ): Promise<void> {
    const grantApplications = await transactionalEntityManager.getRepository(GrantApplication).find({
      relations: {
        createdBy: true,
        grantCall: {
          grantProgram: true,
        },
        workflowState: { currentStepDefinition: true },
      },
      where: { id: In(applicationIds) },
    });

    const applicationsWithVotingTopic = await this.bulkCreateApplicationVotingTopicId(
      grantApplications,
      votingType,
      transactionalEntityManager,
    );

    const message =
      votingType === VotingType.COMMUNITY_VOTING ? 'Community voting started' : 'Final community voting started';

    await Promise.all(
      applicationsWithVotingTopic.map((app) => this.hederaService.submitMessageWithRetry(app.topicId, message)),
    );

    const currentStage =
      votingType === VotingType.COMMUNITY_VOTING ? StageCode.GA_QUALIFICATION : StageCode.GA_FINAL_QUALIFICATION;
    const stageDefinitions = await this.getApplicationStageDefinitions();

    await Promise.all(
      grantApplications.map((ga) =>
        this.notificationsService.applicationTransitioned(ga, currentStage, stageDefinitions),
      ),
    );
  }

  async anchorVotingEndedEvent(
    grantCall: GrantCall,
    categorizationResult: CategorizedVotingOutcome,
    allAppScoresMap: ApplicationScoresMap,
    voteTypeForProcessing: VotingType,
  ) {
    const allApplications = [
      ...categorizationResult.rejectedApplications,
      ...categorizationResult.qualifiedApplications,
    ];

    await Promise.all(
      allApplications.map(({ votingTopicId, finalVotingTopicId }) =>
        this.hederaService.submitMessageWithRetry(
          voteTypeForProcessing === VotingType.COMMUNITY_VOTING ? votingTopicId : finalVotingTopicId,
          voteTypeForProcessing === VotingType.COMMUNITY_VOTING
            ? 'Community voting ended'
            : 'Final community voting ended',
        ),
      ),
    );

    await Promise.all(
      allApplications.map(({ id, votingTopicId, finalVotingTopicId }) =>
        this.hederaService.submitMessageWithRetry(
          voteTypeForProcessing === VotingType.COMMUNITY_VOTING ? votingTopicId : finalVotingTopicId,
          `Total number of votes in favor: ${allAppScoresMap.get(id)?.[voteTypeForProcessing]?.inFavor ?? 0}. Total number of votes against: ${allAppScoresMap.get(id)?.[voteTypeForProcessing]?.against ?? 0}`,
        ),
      ),
    );

    const stageDefinitions = await this.getApplicationStageDefinitions();

    // @TODO Use a message queue system
    if (voteTypeForProcessing === VotingType.COMMUNITY_VOTING) {
      await Promise.all([
        ...categorizationResult.qualifiedApplications.flatMap((application) => [
          this.anchorTransitionEvent(
            application,
            'The application reached the required threshold of votes and moves to the next stage of the process.',
            StageCode.GA_INTERVIEW,
            null,
            StageCode.GA_QUALIFICATION,
          ),
          this.notificationsService.applicationTransitionedForVoting(
            application,
            grantCall,
            StageCode.GA_INTERVIEW,
            stageDefinitions,
          ),
        ]),
        ...categorizationResult.rejectedApplications.flatMap((application) => [
          this.anchorTransitionEvent(
            application,
            'The application did not reach the required threshold of votes and does not move to the next stage of the process.',
            WorkflowStatus.REJECTED,
            null,
            StageCode.GA_QUALIFICATION,
          ),
          this.notificationsService.applicationStatusChangeForVoting(
            application,
            grantCall,
            StageCode.GA_QUALIFICATION,
            WorkflowStatus.REJECTED,
            stageDefinitions,
          ),
        ]),
      ]);
    } else if (voteTypeForProcessing === VotingType.FINAL_COMMUNITY_VOTING) {
      await Promise.all([
        ...categorizationResult.qualifiedApplications.flatMap((application, index) => [
          this.anchorTransitionEvent(
            application,
            `The application reached place number ${index + 1} in the vote process.`,
            WorkflowStatus.APPROVED,
            null,
            StageCode.GA_FINAL_QUALIFICATION,
          ),
          this.notificationsService.applicationStatusChangeForVoting(
            application,
            grantCall,
            StageCode.GA_FINAL_QUALIFICATION,
            WorkflowStatus.APPROVED,
            stageDefinitions,
          ),
        ]),
        ...categorizationResult.rejectedApplications.flatMap((application) => [
          this.anchorTransitionEvent(
            application,
            'The application was not among the three most voted applications and does not move to the next stage of the process.',
            WorkflowStatus.REJECTED,
            null,
            StageCode.GA_FINAL_QUALIFICATION,
          ),
          this.notificationsService.applicationStatusChangeForVoting(
            application,
            grantCall,
            StageCode.GA_FINAL_QUALIFICATION,
            WorkflowStatus.REJECTED,
            stageDefinitions,
          ),
        ]),
      ]);
    }
  }

  async notifyDueDiligenceCompleted(applicationIds: number[]) {
    const stepDefinitions = await this.getApplicationStageDefinitions();
    const grantApplications = await this.grantApplicationRepository.findForStatusUpdate(applicationIds);

    return Promise.all(
      grantApplications.map((ga) =>
        this.notificationsService.applicationTransitioned(ga, StageCode.GA_TOWN_HALL, stepDefinitions),
      ),
    );
  }
}
