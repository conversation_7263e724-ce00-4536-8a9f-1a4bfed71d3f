import { forwardRef, Module } from '@nestjs/common';

import { AuthModule } from '../auth/auth.module';
import { GrantApplication } from './entities/grant-application.entity';
import { GrantApplicationController } from './grant-application.controller';
import { GrantApplicationService } from './grant-application.service';
import { GrantCall } from '../grant-call/entities/grant-call.entity';
import { GrantCallMapper } from '../grant-call/grant-call.mapper';
import { GrantProgram } from '../grant-program/entities/grant-program.entity';
import { HederaModule } from '../hedera/hedera.module';
import { HttpModule } from '@nestjs/axios';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../auth/entities/user.entity';
import { VotesModule } from '../votes/votes.module';
import { VotingOutcomeModule } from '../voting-outcome/voting-outcome.module';
import { WorkflowService } from '../workflow/workflow.service';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';
import { GrantApplicationRepository } from './grant-application.repository';
import { NotificationsModule } from '../notifications/notifications.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      GrantApplication,
      WorkflowTemplate,
      WorkflowStepDefinition,
      User,
      GrantCall,
      GrantProgram,
      WorkflowStepDefinition,
    ]),
    AuthModule,
    NotificationsModule,
    HederaModule,
    HttpModule,
    forwardRef(() => VotesModule),
    VotingOutcomeModule,
  ],
  controllers: [GrantApplicationController],
  providers: [
    GrantApplicationService,
    GrantApplicationRepository,
    WorkflowService,
    WorkflowTransitionService,
    GrantCallMapper,
  ],
  exports: [GrantApplicationService],
})
export class GrantApplicationModule {}
