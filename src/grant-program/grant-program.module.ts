import * as multer from 'multer';
import * as path from 'path';

import { BadRequestException, Module } from '@nestjs/common';

import { AuthModule } from '../auth/auth.module';
import { ConfigService } from '@nestjs/config';
import { <PERSON><PERSON>all } from '../grant-call/entities/grant-call.entity';
import { GrantCallModule } from '../grant-call/grant-call.module';
import { GrantProgram } from './entities/grant-program.entity';
import { GrantProgramController } from './grant-program.controller';
import { GrantProgramService } from './grant-program.service';
import { MulterModule } from '@nestjs/platform-express';
import { RateLimiterModule } from 'nestjs-rate-limiter';
import { S3Client } from '@aws-sdk/client-s3';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../auth/entities/user.entity';
import { WorkflowModule } from '../workflow/workflow.module';
import { GrantProgramRepository } from './grant-program.repository';

const s3ClientProvider = {
  provide: S3Client,
  useFactory: (configService: ConfigService) => {
    return new S3Client({
      region: configService.get('AWS_REGION'),
      forcePathStyle: configService.get('USE_LOCALSTACK'),
      endpoint: configService.get('AWS_ENDPOINT'),
      credentials: {
        accessKeyId: configService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: configService.get('AWS_SECRET_ACCESS_KEY'),
      },
    });
  },
  inject: [ConfigService],
};

@Module({
  imports: [
    AuthModule,
    TypeOrmModule.forFeature([GrantProgram, User, GrantCall]),
    GrantCallModule,
    RateLimiterModule,
    MulterModule.register({
      storage: multer.diskStorage({
        destination: './temp',
        filename: (req, file, cb) => {
          const ext = path.extname(file.originalname);
          const filename = `${path.basename(file.originalname, ext)}-${Date.now()}${ext}`;
          cb(null, filename);
        },
      }),
      fileFilter: (req, file, cb) => {
        const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        if (allowedMimeTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new BadRequestException(`Only image files are allowed!`), false);
        }
      },
      limits: {
        fileSize: 2 * 1024 * 1024, // 2MB
      },
    }),
    WorkflowModule,
  ],
  controllers: [GrantProgramController],
  providers: [GrantProgramService, s3ClientProvider, GrantProgramRepository],
})
export class GrantProgramModule {}
