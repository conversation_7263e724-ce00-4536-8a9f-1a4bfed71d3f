import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { GrantProgram } from './entities/grant-program.entity';
import { GrantProgramWithApplicationsCount } from './dto/grant-program-with-applications-count.dto';
import { Role } from '../auth/role.enum';
import { LoggedInUser } from 'src/auth/decorators/logged-in-user.decorator';

@Injectable()
export class GrantProgramRepository {
  constructor(
    @InjectRepository(GrantProgram)
    private readonly repository: Repository<GrantProgram>,
  ) {}

  /**
   * Fetches a single GrantProgram by its slug, including related data.
   */
  async findOneBySlug(slug: string, loggedInUser: LoggedInUser): Promise<GrantProgramWithApplicationsCount> {
    const query = this.repository.createQueryBuilder('grantProgram');

    if (loggedInUser && loggedInUser.role === Role.COORDINATOR) {
      query.where('grantProgram.grantProgramCoordinatorId = :userId', { userId: loggedInUser.id });
    }

    query
      // Filter by slug
      .where('grantProgram.grantProgramSlug = :slug', { slug })
      // Core GrantProgram fields
      .select([
        'grantProgram.id',
        'grantProgram.name',
        'grantProgram.grantProgramSlug',
        'grantProgram.description',
        'grantProgram.scope',
        'grantProgram.budget',
        'grantProgram.grantorPublicProfileName',
        'grantProgram.grantorLogoURL',
        'grantProgram.grantorDescription',
        'grantProgram.grantorWebsite',
        'grantProgram.updatedAt',
      ])

      // Join the coordinator user
      .leftJoin('grantProgram.grantProgramCoordinator', 'grantProgramCoordinator')
      .addSelect(['grantProgramCoordinator.id', 'grantProgramCoordinator.displayName'])

      // Join this grant program's workflow state
      .leftJoin('grantProgram.workflowState', 'workflowState')
      .addSelect([
        'workflowState.id',
        'workflowState.currentStepTransitionedAt',
        'workflowState.currentStepEndsAt',
        'workflowState.currentStepDefinition',
      ])
      // And the current workflow step
      .leftJoin('workflowState.currentStepDefinition', 'currentStepDefinition')
      .addSelect([
        'currentStepDefinition.code',
        'currentStepDefinition.transitionType',
        'currentStepDefinition.isTerminal',
        'currentStepDefinition.sequenceNumber',
      ])
      // Load the template and its steps
      .leftJoinAndSelect('workflowState.workflowTemplate', 'workflowTemplate')
      .leftJoin('workflowTemplate.steps', 'steps')
      .addSelect(['steps.id', 'steps.sequenceNumber'])

      // Join all grant calls under this program
      .leftJoin('grantProgram.grantCalls', 'grantCalls')
      .addSelect([
        'grantCalls.id',
        'grantCalls.name',
        'grantCalls.description',
        'grantCalls.grantCallSlug',
        'grantCalls.totalGrantAmount',
        'grantCalls.categories',
        'grantCalls.updatedAt',
        'grantCalls.workflowState',
      ])

      // Fetch each grant call's workflow state
      .leftJoin('grantCalls.workflowState', 'gcWorkflowState')
      .addSelect([
        'gcWorkflowState.id',
        'gcWorkflowState.currentStepTransitionedAt',
        'gcWorkflowState.currentStepEndsAt',
        'gcWorkflowState.currentStepDefinition',
      ])
      // And that state's definition details
      .leftJoin('gcWorkflowState.currentStepDefinition', 'gcCurrentStepDefinition')
      .addSelect([
        'gcCurrentStepDefinition.code',
        'gcCurrentStepDefinition.transitionType',
        'gcCurrentStepDefinition.isTerminal',
        'gcCurrentStepDefinition.sequenceNumber',
      ])
      // Load the grant-call template and its steps
      .leftJoinAndSelect('gcWorkflowState.workflowTemplate', 'gcWorkflowTemplate')
      .leftJoin('gcWorkflowTemplate.steps', 'gcSteps')
      .addSelect(['gcSteps.id', 'gcSteps.sequenceNumber'])

      // Include stage settings for each grant call
      .leftJoinAndSelect('grantCalls.stageSettings', 'stageSettings')
      .leftJoin('stageSettings.workflowStepDefinition', 'workflowStepDefinition')
      .addSelect('workflowStepDefinition.code')

      // Count applications per grant call
      .loadRelationCountAndMap('grantCalls.grantApplicationsCount', 'grantCalls.grantApplications');

    return (await query.getOne()) as unknown as GrantProgramWithApplicationsCount;
  }
}
