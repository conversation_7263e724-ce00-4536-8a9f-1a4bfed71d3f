import { PutObjectCommand, PutObjectCommandInput, S3Client } from '@aws-sdk/client-s3';
import { BadRequestException, HttpException, HttpStatus, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { existsSync, readFileSync, unlink } from 'node:fs';
import { dirname, extname } from 'node:path';
import { GrantCall } from '../grant-call/entities/grant-call.entity';
import { FindOptionsWhere, Repository } from 'typeorm';
import { User } from '../auth/entities/user.entity';
import { sanitizeDto } from '../utils/sanitize.util';
import { slugifyString } from '../utils/slugify.util';
import {
  CreateGrantProgram,
  GrantProgramWithGrantCallCountDto,
  UpdateGrantProgram,
  SimpleGrantProgramDto,
} from './dto';
import { GrantProgram } from './entities/grant-program.entity';
import { DatabaseErrorCode } from '../database/database-error-code.enum';
import { GrantCallMapper } from '../grant-call/grant-call.mapper';
import { GrantProgramWithCallsResponseDto } from './dto/grant-program-with-calls.response.dto';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';
import { WorkflowEntityType } from '../workflow/enums/workflow-entity-type.enum';
import { WorkflowState } from '../workflow/entities/workflow-state.entity';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { StageCode } from '../workflow/enums/stage-code.enum';
import { StageTransitionType } from '../workflow/enums/stage-transition-type.enum';
import { GrantProgramRepository } from './grant-program.repository';
import { GrantCategory } from '../grant-call/enums/grant-category.enum';
import { LoggedInUser } from 'src/auth/decorators/logged-in-user.decorator';

@Injectable()
export class GrantProgramService {
  constructor(
    @InjectRepository(GrantProgram)
    private readonly grantProgramRepository: Repository<GrantProgram>,
    private readonly grantProgramRepositoryCustom: GrantProgramRepository,
    private readonly grantCallMapper: GrantCallMapper,
    @InjectRepository(User)
    private readonly authRepository: Repository<User>,
    @InjectRepository(GrantCall)
    private readonly grantCallRepo: Repository<GrantCall>,
    private readonly configService: ConfigService,
    private readonly s3: S3Client,
    private readonly workflowTransitionService: WorkflowTransitionService,
  ) {}

  private readonly logger = new Logger(GrantProgramService.name);

  private async uploadToS3(file: Express.Multer.File) {
    try {
      const bucket = this.configService.get('AWS_BUCKET_NAME');
      const filePath = `grant-logos/${file.filename}`;
      const uploadParams: PutObjectCommandInput = {
        Bucket: bucket,
        Key: filePath,
        Body: file.buffer,
        ACL: 'public-read',
      };
      const response = await this.s3.send(new PutObjectCommand(uploadParams));
      if (response.$metadata.httpStatusCode !== 200) {
        throw new HttpException('File upload failed.', HttpStatus.INTERNAL_SERVER_ERROR);
      }
      return filePath;
    } catch (error) {
      throw new HttpException(error.message, error.status);
    }
  }

  async create(createGrantProgram: CreateGrantProgram, loggedInUser: LoggedInUser) {
    try {
      const user = await this.authRepository.findOne({
        where: { id: loggedInUser.id },
      });
      if (!user) {
        throw new HttpException('User not found.', HttpStatus.NOT_FOUND);
      }
      if (dirname(createGrantProgram.grantorLogoURL) !== 'temp' || !existsSync(createGrantProgram.grantorLogoURL)) {
        throw new HttpException('Invalid grantor logo file path.', HttpStatus.BAD_REQUEST);
      }

      return this.grantProgramRepository.manager.transaction(async (transactionalEntityManager) => {
        const wfStateRepo = transactionalEntityManager.getRepository(WorkflowState);
        const stepDefRepo = transactionalEntityManager.getRepository(WorkflowStepDefinition);
        const wfTemplateRepo = transactionalEntityManager.getRepository(WorkflowTemplate);

        const defaultOpenStepDef = await stepDefRepo.findOne({ where: { code: StageCode.GP_OPEN } });

        const workflowTemplate = await wfTemplateRepo.findOneBy({
          entityType: WorkflowEntityType.PROGRAM,
        });

        const initialWorkflowState = wfStateRepo.create({
          workflowTemplate,
          currentStepDefinitionId: defaultOpenStepDef.id,
          currentStepDefinition: defaultOpenStepDef,
          currentStepTransitionedAt: new Date(),
          currentStepEndsAt: null,
        });

        await wfStateRepo.save(initialWorkflowState);

        createGrantProgram = sanitizeDto(createGrantProgram);
        const grantProgramSlug = slugifyString(createGrantProgram.name);
        const filePath = await this.uploadLogoToS3AndDeleteLocalFile(
          createGrantProgram.grantorLogoURL,
          grantProgramSlug,
        );

        await this.grantProgramRepository.save({
          name: createGrantProgram.name,
          grantProgramSlug,
          description: createGrantProgram.description,
          scope: createGrantProgram.scope,
          budget: createGrantProgram.budget,
          grantorPublicProfileName: createGrantProgram.grantorPublicProfileName,
          grantorLogoURL: filePath,
          grantorDescription: createGrantProgram.grantorDescription,
          grantorWebsite: this.formatGrantorWebsiteUrl(createGrantProgram.grantorWebsite),
          grantProgramCoordinator: user,
          workflowState: initialWorkflowState,
        });

        return {
          success: true,
          grantProgramSlug,
          message: 'Grant Program created successfully.',
        };
      });
    } catch (error) {
      // Code 23505 is for unique constraint violation in PostgreSQL
      if (error.code === DatabaseErrorCode.UNIQUE_CONSTRAINT_VIOLATION) {
        throw new HttpException('Slug name already exists.', HttpStatus.CONFLICT);
      } else {
        throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  }

  async findAll(user: string, requestUserId: number | null) {
    let where: FindOptionsWhere<GrantProgram>[];
    if (user == 'me') {
      if (!requestUserId) {
        throw new HttpException('User not signed in.', HttpStatus.UNAUTHORIZED);
      }

      where = [
        { grantCalls: { createdBy: { id: requestUserId } } },
        { grantProgramCoordinator: { id: requestUserId } },
      ];
    }

    const grantPrograms = await this.grantProgramRepository.find({
      where,
      relations: {
        workflowState: {
          currentStepDefinition: true,
          workflowTemplate: { steps: true },
        },
        grantProgramCoordinator: true,
        grantCalls: { createdBy: true, workflowState: { currentStepDefinition: true } },
      },
    });

    // Augment the grant programs with the count of grant calls
    const augmentedGrantPrograms = await Promise.all(
      grantPrograms.map(async (program) => {
        const dto = GrantProgramService.createDtoFromGrantProgram(program) as GrantProgramWithGrantCallCountDto;
        dto.grantCallsCount = program.grantCalls.length;

        if (user == 'me') {
          // Because of the "where" clause program.grantCalls might not be populated with all grant calls
          dto.grantCallsCount = await this.grantCallRepo.count({ where: { grantProgram: { id: program.id } } });
        }

        return dto;
      }),
    );

    return {
      success: true,
      data: augmentedGrantPrograms,
    };
  }

  async findOne(grantProgramSlug: string) {
    const program = await this.grantProgramRepository.findOne({
      where: { grantProgramSlug },
      relations: {
        grantProgramCoordinator: true,
        grantCalls: {
          workflowState: {
            currentStepDefinition: true,
          },
        },
        workflowState: {
          currentStepDefinition: true,
          workflowTemplate: { steps: true },
        },
      },
    });
    if (!program) {
      throw new HttpException('Grant Program not found.', HttpStatus.NOT_FOUND);
    }
    const dto = GrantProgramService.createDtoFromGrantProgram(program) as GrantProgramWithGrantCallCountDto;
    dto.grantCallsCount = program.grantCalls.length;
    return {
      success: true,
      data: dto,
    };
  }

  async update(grantProgramSlug: string, updateGrantProgram: UpdateGrantProgram) {
    let grantProgram = await this.grantProgramRepository.findOne({
      where: { grantProgramSlug },
      relations: ['grantProgramCoordinator'],
    });
    if (!grantProgram) {
      throw new HttpException('Grant Program not found.', HttpStatus.NOT_FOUND);
    }
    if (
      updateGrantProgram.grantorLogoURL &&
      (dirname(updateGrantProgram.grantorLogoURL) !== 'temp' || !existsSync(updateGrantProgram.grantorLogoURL))
    ) {
      throw new HttpException('Invalid grantor logo file path.', HttpStatus.BAD_REQUEST);
    }

    updateGrantProgram = sanitizeDto(updateGrantProgram);
    if (updateGrantProgram.name && updateGrantProgram.name !== grantProgram.name) {
      grantProgram.grantProgramSlug = slugifyString(updateGrantProgram.name);
    }
    if (updateGrantProgram.grantorWebsite) {
      updateGrantProgram.grantorWebsite = this.formatGrantorWebsiteUrl(updateGrantProgram.grantorWebsite);
    }
    grantProgram = {
      ...grantProgram,
      ...updateGrantProgram,
    };
    if (updateGrantProgram.grantorLogoURL) {
      grantProgram.grantorLogoURL = await this.uploadLogoToS3AndDeleteLocalFile(
        updateGrantProgram.grantorLogoURL,
        grantProgramSlug,
      );
    }
    await this.grantProgramRepository.save(grantProgram);
    return {
      success: true,
      grantProgramSlug: grantProgram.grantProgramSlug,
    };
  }

  async grantCallsForGrantProgram(
    grantProgramSlug: string,
    loggedInUser?: LoggedInUser,
    filterByCurrentUser: boolean = false,
  ): Promise<GrantProgramWithCallsResponseDto> {
    if (filterByCurrentUser && !loggedInUser.id) {
      throw new BadRequestException('Authentication is required when filtering by "myCalls".');
    }

    const grantProgram = await this.grantProgramRepositoryCustom.findOneBySlug(grantProgramSlug, loggedInUser);

    if (!grantProgram) {
      throw new NotFoundException(`Grant Program with slug "${grantProgramSlug}" not found.`);
    }

    const grantCalls = grantProgram.grantCalls.map((grantCall) => {
      const {
        name,
        totalGrantAmount,
        description,
        grantCallSlug,
        updatedAt,
        grantApplicationsCount,
        categories,
        stageSettings,
        workflowState,
        createdBy,
      } = grantCall;

      const timingSettings = this.grantCallMapper.extractTimingsFromSettings(stageSettings);

      return {
        name,
        totalGrantAmount,
        description,
        grantCallSlug,
        updatedAt,
        grantApplicationsCount,
        categories: categories as GrantCategory[],
        openForApplicationStart: timingSettings.openForApplicationStart,
        openForApplicationEnd: timingSettings.openForApplicationEnd,
        status: workflowState.currentStepDefinition.code,
        createdBy,
      };
    });

    return {
      grantProgram: {
        ...GrantProgramService.createDtoFromGrantProgram(grantProgram),
        isAbleToChangeStageManually: this.calculateIsAbleToChangeStageManually(grantProgram),
      },
      grantCalls,
    };
  }

  async transitionGrantProgramToNextStep(grantProgramSlug: string): Promise<SingleTransitionResponseDto> {
    const grantProgram = await this.grantProgramRepository.findOne({
      where: { grantProgramSlug },
      relations: {
        grantCalls: {
          workflowState: {
            currentStepDefinition: true,
          },
        },
      },
    });

    if (!grantProgram) {
      throw new NotFoundException('Grant Program not found');
    }

    if (
      grantProgram.grantCalls &&
      grantProgram.grantCalls.some((call) => call.workflowState.currentStepDefinition.code !== StageCode.GC_FINALIZED)
    ) {
      throw new BadRequestException(
        'Cannot transition grant program to next step because some grant calls are not finalized.',
      );
    }

    return this.workflowTransitionService.transitionSingleStateToNextStep(WorkflowEntityType.PROGRAM, grantProgram.id);
  }

  private async uploadLogoToS3AndDeleteLocalFile(localLogoFilePath: string, grantProgramSlug: string): Promise<string> {
    const file = readFileSync(localLogoFilePath);
    const filePath = await this.uploadToS3({
      buffer: file,
      filename: grantProgramSlug + extname(localLogoFilePath),
    } as Express.Multer.File);

    // Asynchronously delete the local file
    unlink(localLogoFilePath, (err) => {
      if (err) {
        this.logger.error(`Error deleting local grant program logo '${filePath}':`, err);
      }
      this.logger.debug(`Local grant program logo '${localLogoFilePath}' deleted successfully after upload to S3.`);
    });

    return filePath;
  }

  private calculateIsAbleToChangeStageManually(grantProgram: GrantProgram): boolean {
    return (
      grantProgram.workflowState.currentStepDefinition.transitionType === StageTransitionType.MANUAL &&
      !grantProgram.workflowState.currentStepDefinition.isTerminal &&
      (!grantProgram.workflowState.currentStepEndsAt || grantProgram.workflowState.currentStepEndsAt <= new Date()) &&
      grantProgram.workflowState.workflowTemplate.steps.some(
        (step) => step.sequenceNumber > grantProgram.workflowState.currentStepDefinition.sequenceNumber,
      ) &&
      grantProgram.grantCalls.every((call) => call.workflowState.currentStepDefinition.code === StageCode.GC_FINALIZED)
    );
  }

  static createDtoFromGrantProgram(grantProgram: GrantProgram): SimpleGrantProgramDto {
    return {
      grantProgramSlug: grantProgram.grantProgramSlug,
      name: grantProgram.name,
      description: grantProgram.description,
      scope: grantProgram.scope,
      budget: grantProgram.budget,
      grantorPublicProfileName: grantProgram.grantorPublicProfileName,
      grantorLogoURL: grantProgram.grantorLogoURL,
      status: grantProgram.workflowState.currentStepDefinition.code,
      grantProgramCoordinator: {
        displayName: grantProgram.grantProgramCoordinator.displayName,
        id: grantProgram.grantProgramCoordinator.id,
      },
      grantorDescription: grantProgram.grantorDescription,
      grantorWebsite: grantProgram.grantorWebsite,
    };
  }

  private formatGrantorWebsiteUrl(url?: string): string {
    if (!url) {
      return '';
    }
    return url.startsWith('http://') || url.startsWith('https://') ? url : `https://${url}`;
  }
}
