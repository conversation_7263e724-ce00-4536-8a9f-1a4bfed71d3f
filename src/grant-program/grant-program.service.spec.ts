import { CreateGrantProgram, UpdateGrantProgram } from './dto';
import { DataSource, Repository } from 'typeorm';
import { HttpException, HttpStatus } from '@nestjs/common';
import { PutObjectCommandOutput, S3Client } from '@aws-sdk/client-s3';
import { Test, TestingModule } from '@nestjs/testing';
import { existsSync, writeFileSync } from 'node:fs';

import { AuthService } from '../auth/auth.service';
import { ConfigService } from '@nestjs/config';
import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { GrantCall } from '../grant-call/entities/grant-call.entity';
import { GrantCallMapper } from '../grant-call/grant-call.mapper';
import { GrantProgram } from './entities/grant-program.entity';
import { GrantProgramService } from './grant-program.service';
import { JwtModule } from '@nestjs/jwt';
import { MailService } from '../notifications/mail/mail.service';
import { MailerService } from '@nestjs-modules/mailer';
import { StageCode } from '../workflow/enums/stage-code.enum';
import { User } from '../auth/entities/user.entity';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { GrantProgramRepository } from './grant-program.repository';
import { Role } from '../auth/role.enum';

jest.mock('node:fs');

const mockMapper = { mapGrantCallToDetailDto: jest.fn() };

const mockGrantProgramRepository = {
  findOneBySlug: jest.fn(),
};

describe('GrantProgramService', () => {
  let service: GrantProgramService;
  let grantProgramRepository: Repository<GrantProgram>;
  let userRepository: Repository<User>;
  let grantCallRepo: Repository<GrantCall>;
  let s3Client: S3Client;
  let authService: AuthService;
  const mockLoggedInUser = {
    id: 1,
    email: '<EMAIL>',
    emailVerified: true,
    phoneNumber: null,
    isPhoneVerified: null,
    otp: null,
    otpExpiresAt: null,
    role: Role.COORDINATOR,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [JwtModule.register({ secret: 'test_secret', signOptions: { expiresIn: '1d' }, global: true })],
      providers: [
        GrantProgramService,
        ConfigService,
        {
          provide: WorkflowTransitionService,
          useValue: {
            transitionSingleStateToNextStep: jest.fn(),
          },
        },
        { provide: GrantCallMapper, useValue: mockMapper },
        {
          provide: getRepositoryToken(GrantProgram),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(User),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(GrantCall),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(GrantApplication),
          useClass: Repository,
        },
        {
          provide: S3Client,
          useValue: {
            send: jest.fn(),
          },
        },
        AuthService,
        {
          provide: MailService,
          useValue: {
            send: jest.fn(),
          },
        },
        {
          provide: MailerService,
          useValue: {
            send: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: {
            name: 'test',
            createQueryRunner: jest.fn(),
          },
        },
        {
          provide: GrantProgramRepository,
          useValue: mockGrantProgramRepository,
        },
      ],
    }).compile();

    service = module.get<GrantProgramService>(GrantProgramService);
    grantProgramRepository = module.get<Repository<GrantProgram>>(getRepositoryToken(GrantProgram));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    grantCallRepo = module.get<Repository<GrantCall>>(getRepositoryToken(GrantCall));
    s3Client = module.get<S3Client>(S3Client);
    authService = module.get<AuthService>(AuthService);
  });

  it('should upload a file to S3', async () => {
    const file = {
      filename: 'test.jpg',
      buffer: 'sample',
    } as any;

    const mockResponse = {
      $metadata: {
        httpStatusCode: 200,
      },
    } as PutObjectCommandOutput;

    jest.spyOn(s3Client, 'send').mockReturnValue(mockResponse as any);
    const result = await service['uploadToS3'](file);

    expect(result).toEqual(`grant-logos/${file.filename}`);
  });

  describe('create a grant program', () => {
    it('should thrown an error if the user does not exist', async () => {
      const createGrantProgram: CreateGrantProgram = {
        name: 'Test Grant Program',
        description: 'This is a test grant program',
        scope: 'Test Scope',
        budget: 10000,
        grantorPublicProfileName: 'Test Grantor',
        grantorLogoURL: 'test.jpg',
        grantorDescription: 'Test Grantor Description',
        grantorWebsite: 'https://test.com',
      };

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(undefined);
      try {
        await service.create(createGrantProgram as any, mockLoggedInUser);
      } catch (error) {
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toEqual('User not found.');
      }
    });

    it('should throw BAD_REQUEST if grantor logo file path is invalid', async () => {
      const mockCreateGrantProgram: CreateGrantProgram = {
        name: 'Test Grant Program',
        grantorLogoURL: 'temp/logo.png',
        grantorPublicProfileName: 'Test Grantor',
        grantorDescription: 'Test Grantor Description',
        grantorWebsite: 'www.example.com',
        scope: 'Test Scope',
        budget: 1000,
        description: 'Test Description',
      };
      jest.spyOn(userRepository, 'findOne').mockResolvedValue({ id: 1 } as any);
      (existsSync as jest.Mock).mockReturnValue(false);
      await expect(service.create(mockCreateGrantProgram, mockLoggedInUser)).rejects.toThrowError(
        new HttpException('Invalid grantor logo file path.', HttpStatus.BAD_REQUEST),
      );
    });
  });

  describe('get all grant programs', () => {
    it('should return all grant programs with grant call counts for a non-specific user', async () => {
      // Mock data
      const mockGrantPrograms: GrantProgram[] = [
        {
          id: 1,
          name: 'Program 1',
          grantCalls: [
            { id: 1, grantProgramId: 1 } as unknown as GrantCall,
            { id: 2, grantProgramId: 1 } as unknown as GrantCall,
          ],
          grantProgramCoordinator: { id: 99 } as User,
          workflowState: {
            currentStepDefinition: {
              code: StageCode.GP_OPEN,
            },
          },
        } as GrantProgram,
        {
          id: 2,
          name: 'Program 2',
          grantCalls: [{ id: 3, grantProgramId: 2 } as unknown as GrantCall],
          grantProgramCoordinator: { id: 99 } as User,
          workflowState: {
            currentStepDefinition: {
              code: StageCode.GP_FINALIZED,
            },
          },
        } as GrantProgram,
      ];
      jest.spyOn(grantProgramRepository, 'find').mockResolvedValue(mockGrantPrograms);

      // Call the function
      const result = await service.findAll('anyUser', 1);

      // Assertions
      expect(grantProgramRepository.find).toHaveBeenCalledWith({
        where: undefined, // Important: No specific 'where' clause for non-'me' user
        relations: {
          grantProgramCoordinator: true,
          grantCalls: { createdBy: true, workflowState: { currentStepDefinition: true } },
          workflowState: {
            currentStepDefinition: true,
            workflowTemplate: { steps: true },
          },
        },
      });
      expect(result.success).toBe(true);
      expect(result.data.length).toBe(2);
      expect(result.data[0].grantCallsCount).toBe(2); // Count from grantCalls array
      expect(result.data[1].grantCallsCount).toBe(1); // Count from grantCalls array
    });

    it('should return an array of grant programs', async () => {
      const user = new User();
      user.displayName = 'Joe Grant Coordinator';
      const grantPrograms: GrantProgram[] = [
        {
          id: 1,
          grantProgramSlug: 'test-grant-program',
          name: 'Test Grant Program',
          description: 'This is a test grant program',
          scope: 'Test Scope',
          budget: 10000,
          grantorPublicProfileName: 'Test Grantor',
          grantorLogoURL: 'test.jpg',
          grantorDescription: 'Test Grantor Description',
          grantProgramCoordinator: user,
          grantorWebsite: 'https://test.com',
          grantCalls: [],
          createdAt: new Date(),
          updatedAt: new Date(Date.now() + 1000),
          workflowStateId: 1,
          workflowState: {
            id: 1,
            currentStepDefinitionId: 1,
            currentStepTransitionedAt: new Date(),
            currentStepEndsAt: new Date(),
            workflowTemplateId: 0,
            workflowTemplate: new WorkflowTemplate(),
            currentStepDefinition: new WorkflowStepDefinition(),
            status: WorkflowStatus.IN_PROGRESS,
          },
        },
      ];

      jest.spyOn<any, any>(authService, 'verifyAndExtractSignedInUser').mockReturnValue({ id: 1 } as any);
      jest.spyOn(grantProgramRepository, 'find').mockResolvedValue(grantPrograms);
      jest.spyOn(grantCallRepo, 'count').mockResolvedValue(1);

      const result = await service.findAll('me', 1);

      expect(result.success).toBeTruthy();
      expect(result.data).toHaveLength(1);
      expect(result.data[0].grantProgramSlug).toEqual(grantPrograms[0].grantProgramSlug);
      expect(result.data[0].grantProgramCoordinator.displayName).toEqual(
        grantPrograms[0].grantProgramCoordinator.displayName,
      );
      expect(result.data[0].description).toEqual(grantPrograms[0].description);
    });
  });

  describe('get grant program by slug', () => {
    it('should return a grant program if the grant program is found', async () => {
      const user = new User();
      user.displayName = 'Joe Grant Coordinator';
      const grantProgram: GrantProgram = {
        id: 1,
        grantProgramSlug: 'test-grant-program',
        name: 'Test Grant Program',
        description: 'This is a test grant program',
        scope: 'Test Scope',
        budget: 10000,
        grantorPublicProfileName: 'Test Grantor',
        grantorLogoURL: 'test.jpg',
        grantorDescription: 'Test Grantor Description',
        grantorWebsite: 'https://test.com',
        grantProgramCoordinator: user,
        grantCalls: [],
        createdAt: new Date(),
        updatedAt: new Date(Date.now() + 1000),
        workflowStateId: 1,
        workflowState: {
          id: 1,
          currentStepDefinitionId: 1,
          currentStepTransitionedAt: new Date(),
          currentStepEndsAt: new Date(),
          workflowTemplateId: 0,
          workflowTemplate: new WorkflowTemplate(),
          currentStepDefinition: new WorkflowStepDefinition(),
          status: WorkflowStatus.IN_PROGRESS,
        },
      };

      jest.spyOn(grantProgramRepository, 'findOne').mockResolvedValue(grantProgram);
      const result = await service.findOne('test-grant-program');

      expect(result.success).toBeTruthy();
      expect(result.data).toBeDefined();
      expect(result.data.grantProgramSlug).toEqual(grantProgram.grantProgramSlug);
      expect(result.data.grantProgramCoordinator.displayName).toEqual(grantProgram.grantProgramCoordinator.displayName);
      expect(result.data.description).toEqual(grantProgram.description);
    });

    it('should throw an error if the grant program is not found', async () => {
      jest.spyOn(grantProgramRepository, 'findOne').mockResolvedValue(undefined);
      try {
        await service.findOne('test-grant-program');
      } catch (error) {
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toEqual('Grant Program not found.');
        expect(error.status).toEqual(HttpStatus.NOT_FOUND);
      }
    });
  });

  describe('update grant program', () => {
    it('should update a grant program', async () => {
      const coordinator = new User();
      coordinator.id = 1;

      const grantProgram = new GrantProgram();
      grantProgram.id = 1;
      grantProgram.name = 'Test Grant Program 2';
      grantProgram.grantProgramSlug = 'test-grant-program';
      grantProgram.grantProgramCoordinator = coordinator;

      grantProgramRepository.findOne = jest.fn().mockResolvedValue(grantProgram);
      grantProgram.name = 'Test Grant Program 3';
      grantProgramRepository.save = jest.fn();
      // Create a test file meant to be uploaded to S3, but mock the S3 client
      writeFileSync('./temp/test.png', 'sample');
      const mockResponse = { $metadata: { httpStatusCode: 200 } } as PutObjectCommandOutput;
      jest.spyOn(s3Client, 'send').mockReturnValue(mockResponse as any);

      const updateGrantProgram: UpdateGrantProgram = {
        name: 'Test Grant Program 2 Updated',
        description: 'This is an updated test grant program',
        scope: 'Test Scope Updated',
        budget: 10000,
        grantorPublicProfileName: 'Test Grantor',
        grantorLogoURL: '',
        grantorDescription: 'Test Grantor Description',
        grantorWebsite: 'https://test.com',
      };

      const result = await service.update('test-grant-program', updateGrantProgram);

      expect(result.grantProgramSlug).toContain('test-grant-program-2-updated');
      expect(result.success).toBeTruthy();
    });

    it('should throw an error if the grant program is not found', async () => {
      jest.spyOn(grantProgramRepository, 'findOne').mockResolvedValue(undefined);
      try {
        await service.update('test-grant-program', {} as any);
      } catch (error) {
        expect(error).toBeInstanceOf(HttpException);
        expect(error.message).toEqual('Grant Program not found.');
        expect(error.status).toEqual(HttpStatus.NOT_FOUND);
      }
    });
  });

  describe('grantCallsForGrantProgram', () => {
    const testProgramSlug = 'test-prog-slug';

    it('should throw if no grant program is found', async () => {
      mockGrantProgramRepository.findOneBySlug.mockResolvedValue(null);

      await expect(service.grantCallsForGrantProgram(testProgramSlug, null)).rejects.toThrow(
        'Grant Program with slug "test-prog-slug" not found.',
      );
    });
  });
});
