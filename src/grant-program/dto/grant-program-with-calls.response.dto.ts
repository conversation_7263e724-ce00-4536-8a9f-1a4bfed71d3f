import { ApiProperty, PickType } from '@nestjs/swagger';
import { FindOneGrantCallResponseDto } from '../../grant-call/dto';
import { GrantProgramDto } from './grant-program.dto';

class GrantCallDto extends PickType(FindOneGrantCallResponseDto, [
  'name',
  'totalGrantAmount',
  'description',
  'grantCallSlug',
  'updatedAt',
  'grantApplicationsCount',
  'categories',
  'openForApplicationStart',
  'openForApplicationEnd',
  'status',
] as const) {}

export class GrantProgramWithCallsResponseDto {
  @ApiProperty({ type: GrantProgramDto, description: 'Details of the Grant Program.' })
  grantProgram: GrantProgramDto;

  @ApiProperty({ type: [FindOneGrantCallResponseDto], description: 'List of Grant Calls associated with the program.' })
  grantCalls: GrantCallDto[];
}
