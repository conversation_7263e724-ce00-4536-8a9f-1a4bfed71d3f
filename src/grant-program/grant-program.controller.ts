import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Logger,
  Param,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RateLimit, RateLimiterGuard } from 'nestjs-rate-limiter';
import { Role } from '../auth/role.enum';
import { Roles } from '../auth/decorators/roles.decorator';
import {
  CreateGrantProgram,
  CreateGrantProgramResponse,
  FetchGrantProgramResponse,
  FetchGrantProgramsResponse,
  FileUploadResponse,
  UpdateGrantProgram,
  UpdateGrantProgramResponse,
} from './dto';
import { GrantProgramService } from './grant-program.service';
import { GrantProgramWithCallsResponseDto } from './dto/grant-program-with-calls.response.dto';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { Public } from '../auth/decorators/public.decorator';
import { LoggedInUser } from '../auth/decorators/logged-in-user.decorator';

@ApiTags('Grant Program')
@Controller('grant-program')
export class GrantProgramController {
  private readonly logger = new Logger(GrantProgramController.name);

  constructor(private readonly grantProgramService: GrantProgramService) {}

  @Post()
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @ApiOperation({
    summary: 'Create a grant program.',
    description: `Only users with the grant program coordinators role can create grant programs. They automatically 
      become the coordinator of the grant program that they create.`,
  })
  @ApiResponse({
    status: 201,
    description: 'The grant program has been successfully created.',
    type: CreateGrantProgramResponse,
  })
  @ApiResponse({
    status: 400,
    description: 'An invalid grantor logo file path was provided.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden.',
  })
  @ApiResponse({
    status: 404,
    description: 'The user set as the grant program coordinator does not exist.',
  })
  @ApiResponse({
    status: 409,
    description: 'The grant program slug already exists.',
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to upload the grant logo.',
  })
  create(@Body() createGrantProgram: CreateGrantProgram, @LoggedInUser() loggedInUser: LoggedInUser) {
    return this.grantProgramService.create(createGrantProgram, loggedInUser);
  }

  @ApiBearerAuth()
  @Get()
  @ApiOperation({ summary: 'Retrieve all grant programs.' })
  @ApiResponse({
    status: 200,
    description: 'The grant programs have been successfully retrieved.',
    type: FetchGrantProgramsResponse,
  })
  @ApiQuery({
    name: 'user',
    description:
      'Filters for programs for which the user is the coordinator or a member of an associated grant call. Value must be "me" to filter by those assigned to user.',
    example: 'me',
    required: false,
  })
  @Public()
  findAll(
    @Query('user') userFilter: string,
    @LoggedInUser() loggedInUser: LoggedInUser | null,
  ): Promise<FetchGrantProgramsResponse> {
    return this.grantProgramService.findAll(userFilter, loggedInUser?.id);
  }

  @Get(':slug')
  @ApiOperation({ summary: 'Retrieve a grant program by slug.' })
  @ApiResponse({
    status: 200,
    description: 'The grant program has been successfully retrieved.',
    type: FetchGrantProgramResponse,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden.',
  })
  @ApiResponse({
    status: 404,
    description: 'The grant program does not exist.',
  })
  @ApiParam({
    name: 'slug',
    description: 'The slug of the grant program to retrieve.',
    example: 'grant-program-1',
  })
  @Public()
  findOne(@Param('slug') slug: string): Promise<FetchGrantProgramResponse> {
    return this.grantProgramService.findOne(slug);
  }

  @Patch(':slug')
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @ApiOperation({ summary: 'Update a grant program by slug.' })
  @ApiResponse({
    status: 200,
    description: 'The grant program has been successfully updated.',
    type: UpdateGrantProgramResponse,
  })
  @ApiResponse({
    status: 400,
    description: 'An invalid grantor logo file path was provided.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden.',
  })
  @ApiResponse({
    status: 404,
    description: 'The grant program does not exist.',
  })
  @ApiParam({
    name: 'slug',
    description: 'The slug of the grant program to update.',
    example: 'grant-program-1',
  })
  update(
    @Param('slug') slug: string,
    @Body() updateGrantProgram: UpdateGrantProgram,
  ): Promise<UpdateGrantProgramResponse> {
    return this.grantProgramService.update(slug, updateGrantProgram);
  }

  @Get(':slug/grant-calls')
  @ApiOperation({ summary: 'Retrieve grant calls for specific grant program.' })
  @ApiResponse({
    status: 200,
    description: 'The grant calls for the grant program has been successfully retrieved.',
    type: GrantProgramWithCallsResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden.',
  })
  @ApiResponse({
    status: 404,
    description: 'The grant calls does not exist.',
  })
  @ApiQuery({
    name: 'user',
    description: 'Can be user ID or "me"',
    required: false,
  })
  @ApiParam({
    name: 'slug',
    description: 'The slug of the grant program.',
    example: 'grant-program-1',
  })
  @ApiQuery({
    name: 'myCalls',
    description: 'Set to true to only return calls created by the authenticated user.',
    required: false,
  })
  @Public()
  grantCalls(
    @Param('slug') slug: string,
    @LoggedInUser() loggedInUser: LoggedInUser | null,
    @Query('myCalls') filterByMe?: boolean,
  ): Promise<GrantProgramWithCallsResponseDto> {
    return this.grantProgramService.grantCallsForGrantProgram(slug, loggedInUser, filterByMe);
  }

  @ApiOperation({ summary: 'Advance Grant Program to the next workflow stage' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Grant Program successfully transitioned.',
    type: SingleTransitionResponseDto,
  })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'User lacks permission.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Program or its workflow state not found.' })
  @ApiResponse({ status: HttpStatus.UNPROCESSABLE_ENTITY, description: 'terminal state' })
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @Post(':slug/transition')
  async transitionGrantProgram(@Param('slug') slug: string) {
    return this.grantProgramService.transitionGrantProgramToNextStep(slug);
  }

  @Post('upload')
  @RateLimit({
    keyPrefix: 'grantor-logo-upload',
    points: 4,
    duration: 60,
    blockDuration: 120, // if the user uploads 4 or more images in 60 sec, block them for 120 sec
    errorMessage: 'Logos cannot be uploaded more than 4 times in 60 seconds.',
  })
  @ApiBearerAuth()
  @UseGuards(RateLimiterGuard)
  @Roles(Role.COORDINATOR)
  @ApiOperation({ summary: 'Upload the grant logo.' })
  @ApiResponse({
    status: 200,
    description: 'The file has been successfully uploaded.',
    type: FileUploadResponse,
  })
  @ApiResponse({
    status: 400,
    description:
      "If the user tried to upload a file that doesn't have one of the following extensions: 'jpeg', 'png', 'jpg'.",
  })
  @ApiResponse({
    status: 413,
    description: 'The file size exceeds the limit of 2MB. Please upload a file with a size less than 2MB.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden.',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @LoggedInUser() loggedInUser: LoggedInUser,
  ): FileUploadResponse {
    this.logger.log(`User with ID ${loggedInUser.id} uploaded a grantor logo ${file.filename}.`);
    return {
      success: true,
      filePath: 'temp/' + file.filename,
    };
  }
}
