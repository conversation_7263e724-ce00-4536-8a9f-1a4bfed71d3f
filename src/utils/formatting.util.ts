import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { StageCode } from '../workflow/enums/stage-code.enum';

export function formatStageCode(code: StageCode | WorkflowStatus | undefined): string {
  if (!code) {
    return '';
  }

  return code
    .toString()
    .replace(/^(GA_|GC_|GP_)/, '')
    .replace(/_/g, ' ')
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}
