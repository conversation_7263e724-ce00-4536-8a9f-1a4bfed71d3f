import { Controller, Post, Get, Delete, Body, Param, HttpCode, HttpStatus } from '@nestjs/common';
import { SnapshotService } from './snapshot.service';
import { RestoreSnapshotDto } from './dto/restore-snapshot.dto';
import { SnapshotResponseDto } from './dto/snapshot-response.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Snapshot')
@Controller('snapshot')
export class SnapshotController {
  constructor(private readonly snapshotService: SnapshotService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new snapshot.' })
  @ApiResponse({
    status: 201,
    description: 'Snapshot created successfully.',
    type: SnapshotResponseDto,
  })
  async create(): Promise<SnapshotResponseDto> {
    return this.snapshotService.create();
  }

  @Post('restore')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Restore a snapshot.' })
  @ApiResponse({
    status: 204,
    description: 'Snapshot restored successfully.',
  })
  async restore(@Body() restoreSnapshotDto: RestoreSnapshotDto) {
    return this.snapshotService.restore(restoreSnapshotDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all snapshots.' })
  @ApiResponse({
    status: 200,
    description: 'All snapshots retrieved successfully.',
    type: [SnapshotResponseDto],
  })
  async findAll(): Promise<SnapshotResponseDto[]> {
    return this.snapshotService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a snapshot by ID.' })
  @ApiResponse({
    status: 200,
    description: 'Snapshot retrieved successfully.',
    type: SnapshotResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Snapshot not found.',
  })
  async findOne(@Param('id') id: string): Promise<SnapshotResponseDto> {
    return this.snapshotService.findOne(id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a snapshot by ID.' })
  @ApiResponse({
    status: 204,
    description: 'Snapshot deleted successfully.',
  })
  @ApiResponse({
    status: 404,
    description: 'Snapshot not found.',
  })
  async delete(@Param('id') id: string): Promise<void> {
    return this.snapshotService.delete(id);
  }
}
