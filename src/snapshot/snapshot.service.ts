import { Injectable, Logger, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { RestoreSnapshotDto } from './dto/restore-snapshot.dto';
import { SnapshotResponseDto } from './dto/snapshot-response.dto';
import { Snapshot } from './entities/snapshot.entity';
import { ConfigService } from '@nestjs/config';
import { spawn } from 'child_process';
import { unlink } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'node:fs';
import { randomUUID } from 'node:crypto';

@Injectable()
export class SnapshotService {
  private readonly logger = new Logger(SnapshotService.name);

  constructor(
    @InjectRepository(Snapshot)
    private readonly snapshotRepository: Repository<Snapshot>,
    private readonly configService: ConfigService,
  ) {}

  async create(): Promise<SnapshotResponseDto> {
    const dbConfig = this.getDatabaseConfig();
    const snapshotName = `snapshot-${Date.now()}`;
    const snapshotFilePath = this.getSnapshotFilePath(snapshotName);

    // Creating snapshot first so after restoring it will be visible in db
    // Otherwise it would be removed, effectively allowing to restore only once
    const snapshot = this.snapshotRepository.create({
      id: randomUUID(),
      name: snapshotName,
    });

    const savedSnapshot = await this.snapshotRepository.save(snapshot);

    const pgDumpArgs = [
      '--host',
      dbConfig.host,
      '--port',
      dbConfig.port.toString(),
      '--username',
      dbConfig.username,
      '--dbname',
      dbConfig.database,
      '--format',
      'custom',
      '--file',
      snapshotFilePath,
    ];

    if (dbConfig.password) {
      process.env.PGPASSWORD = dbConfig.password;
    }

    return new Promise<Snapshot>((resolve, reject) => {
      const pgDump = spawn('pg_dump', pgDumpArgs, {
        stdio: ['pipe', 'pipe', 'pipe'],
      });

      let errorOutput = '';
      pgDump.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      pgDump.on('close', async (code) => {
        if (code === 0) {
          this.logger.log(`Snapshot ${snapshotName} created successfully`);
          resolve(savedSnapshot);
        } else {
          reject(new InternalServerErrorException(`pg_dump failed with code ${code}: ${errorOutput}`));
        }
      });

      pgDump.on('error', (error) => {
        reject(new InternalServerErrorException(`Failed to execute pg_dump: ${error.message}`));
      });
    });
  }

  async restore(restoreSnapshotDto: RestoreSnapshotDto) {
    const snapshot = await this.snapshotRepository.findOne({
      where: { id: restoreSnapshotDto.snapshotId },
    });

    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${restoreSnapshotDto.snapshotId} not found`);
    }

    return this.restoreSnapshotFile(snapshot.name);
  }

  async findAll(): Promise<SnapshotResponseDto[]> {
    const snapshots = await this.snapshotRepository.find({
      order: { createdAt: 'DESC' },
    });

    return snapshots;
  }

  async findOne(id: string): Promise<SnapshotResponseDto> {
    const snapshot = await this.snapshotRepository.findOne({
      where: { id },
    });

    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${id} not found`);
    }

    return snapshot;
  }

  async delete(id: string): Promise<void> {
    const snapshot = await this.snapshotRepository.findOne({
      where: { id },
    });

    if (!snapshot) {
      throw new NotFoundException(`Snapshot with ID ${id} not found`);
    }

    try {
      const snapshotFilePath = this.getSnapshotFilePath(snapshot.name);
      await unlink(snapshotFilePath);
    } catch (error) {
      this.logger.warn(`Could not delete snapshot file for ${id}:`, error);
    }

    await this.snapshotRepository.remove(snapshot);
  }

  private async restoreSnapshotFile(snapshotName: string): Promise<void> {
    const dbConfig = this.getDatabaseConfig();
    const snapshotFilePath = this.getSnapshotFilePath(snapshotName);

    if (!existsSync(snapshotFilePath)) {
      throw new InternalServerErrorException(`Snapshot file not found: ${snapshotFilePath}`);
    }

    const pgRestoreArgs = [
      '--host',
      dbConfig.host,
      '--port',
      dbConfig.port.toString(),
      '--username',
      dbConfig.username,
      '--dbname',
      dbConfig.database,
      '--clean',
      '--if-exists',
      '--single-transaction',
      snapshotFilePath,
    ];

    if (dbConfig.password) {
      process.env.PGPASSWORD = dbConfig.password;
    }

    return new Promise<void>((resolve, reject) => {
      const pgRestore = spawn('pg_restore', pgRestoreArgs, {
        stdio: ['pipe', 'pipe', 'pipe'],
      });

      let errorOutput = '';
      pgRestore.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      pgRestore.on('close', async (code) => {
        if (code === 0) {
          try {
            this.logger.log(`Snapshot ${snapshotName} restored successfully`);
            resolve();
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new InternalServerErrorException(`pg_restore failed with code ${code}: ${errorOutput}`));
        }
      });

      pgRestore.on('error', (error) => {
        reject(new InternalServerErrorException(`Failed to execute pg_restore: ${error.message}`));
      });
    });
  }

  private getSnapshotFilePath(snapshotName: string): string {
    return join('temp', `${snapshotName}.dump`);
  }

  private getDatabaseConfig() {
    return {
      host: this.configService.get('POSTGRES_HOST'),
      port: parseInt(this.configService.get('POSTGRES_PORT')),
      username: this.configService.get('POSTGRES_USER'),
      password: this.configService.get('POSTGRES_PASSWORD'),
      database: this.configService.get('POSTGRES_DB'),
    };
  }
}
