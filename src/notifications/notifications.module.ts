import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MailModule } from './mail/mail.module';
import { Module } from '@nestjs/common';
import { NotificationPreferenceService } from '../user/notification-preference/notification-preference.service';
import { SnsModule } from './channels/sns/sns.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserNotificationPreferences } from '../user/notification-preference/entities/user-notification-preferences.entity';
import { NotificationPreferenceRepository } from '../user/notification-preference/notification-preference.repository';
import { AuthModule } from '../auth/auth.module';
import { NotificationsService } from './notifications.service';

@Module({
  imports: [
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: configService.get<string>('JWT_EXPIRATION_TIME') },
      }),
    }),
    TypeOrmModule.forFeature([UserNotificationPreferences]),
    MailModule,
    SnsModule,
    AuthModule,
  ],
  providers: [NotificationPreferenceService, NotificationPreferenceRepository, NotificationsService],
  exports: [NotificationsService],
})
export class NotificationsModule {}
