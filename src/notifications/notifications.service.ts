import { Injectable, Logger } from '@nestjs/common';
import { NotificationPreferenceService } from '../user/notification-preference/notification-preference.service';
import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { NotificationType } from '../user/notification-preference/entities/user-notification-preferences.entity';
import { StageCode } from '../workflow/enums/stage-code.enum';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { MailService } from './mail/mail.service';
import { User } from '../auth/entities/user.entity';
import { StageDefinitionResponseDto } from '../workflow/dto/stage-definition.response.dto';
import { GrantCall } from '../grant-call/entities/grant-call.entity';
import { GrantApplicationVotingInfo } from '../voting-outcome/types/voting-service.types';

@Injectable()
export class NotificationsService {
  constructor(
    private notificationPreferenceService: NotificationPreferenceService,
    private mailService: MailService,
  ) {}

  private readonly logger = new Logger(NotificationsService.name);

  async grantCallOpenForApplications(grantCall: GrantCall) {
    const users = await this.notificationPreferenceService.findUsersWithEnabledNotification(
      NotificationType.GRANT_CALL_UPDATE,
    );

    return Promise.all(users.map((u) => this.mailService.sendGrantCallOpenForApplicationsEmail(u.email, grantCall)));
  }

  async grantCallStartedCommunityVoting(grantCall: GrantCall) {
    const users = await this.notificationPreferenceService.findUsersWithEnabledNotification(
      NotificationType.GRANT_CALL_UPDATE,
    );

    return Promise.all(users.map((u) => this.mailService.sendGrantCallCommunityVotingStartedEmail(u.email, grantCall)));
  }

  async grantCallStartedFinalCommunityVoting(grantCall: GrantCall) {
    const users = await this.notificationPreferenceService.findUsersWithEnabledNotification(
      NotificationType.GRANT_CALL_UPDATE,
    );

    return Promise.all(
      users.map((u) => this.mailService.sendGrantCallFinalCommunityVotingStartedEmail(u.email, grantCall)),
    );
  }

  async grantCallFinalized(grantCall: GrantCall) {
    const users = await this.notificationPreferenceService.findUsersWithEnabledNotification(
      NotificationType.GRANT_CALL_UPDATE,
    );

    return Promise.all(users.map((u) => this.mailService.sendGrantCallFinalizedEmail(u.email, grantCall)));
  }

  async applicationCreated(user: User, application: GrantApplication): Promise<boolean> {
    if (!(await this.shouldNotifyAboutApplicationUpdate(user.id))) {
      return true;
    }

    return this.mailService.sendApplicationCreatedEmail(user.email, application);
  }

  async applicationTransitioned(
    application: GrantApplication,
    newStepCode: StageCode,
    stageDefinitions?: StageDefinitionResponseDto[],
  ): Promise<boolean> {
    if (!(await this.shouldNotifyAboutApplicationUpdate(application.createdById))) {
      return true;
    }

    return this.mailService
      .sendApplicationTransitionedEmail(
        application.createdBy.email,
        {
          applicationTitle: application.title,
          grantProgramSlug: application.grantCall.grantProgram.grantProgramSlug,
          grantCallSlug: application.grantCall.grantCallSlug,
          applicationId: application.id,
        },
        newStepCode,
        stageDefinitions,
      )
      .then(() => {
        this.logger.log(`Sent transition notifications for App ${application.id} to stage ${newStepCode}`);

        return true;
      })
      .catch((error) => {
        this.logger.error(
          `Failed to send transition notification for App ${application.id}: ${error.message}`,
          error.stack,
        );

        return false;
      });
  }

  async applicationTransitionedForVoting(
    appVotingInfo: GrantApplicationVotingInfo,
    grantCall: GrantCall,
    newStepCode: StageCode,
    stageDefinitions: StageDefinitionResponseDto[],
  ) {
    if (!(await this.shouldNotifyAboutApplicationUpdate(appVotingInfo.createdBy.id))) {
      return true;
    }

    return this.mailService
      .sendApplicationTransitionedEmail(
        appVotingInfo.createdBy.email,
        {
          applicationTitle: appVotingInfo.title,
          grantProgramSlug: grantCall.grantProgram.grantProgramSlug,
          grantCallSlug: grantCall.grantCallSlug,
          applicationId: appVotingInfo.id,
        },
        newStepCode,
        stageDefinitions,
      )
      .then(() => {
        this.logger.log(`Sent transition notifications for App ${appVotingInfo.id} to stage ${newStepCode}`);

        return true;
      })
      .catch((error) => {
        this.logger.error(
          `Failed to send transition notification for App ${appVotingInfo.id}: ${error.message}`,
          error.stack,
        );

        return false;
      });
  }

  async applicationStatusChange(
    application: GrantApplication,
    newStatus: WorkflowStatus,
    currentStage: StageCode,
    stageDefinitions?: StageDefinitionResponseDto[],
  ) {
    if (
      [WorkflowStatus.APPROVED, WorkflowStatus.REJECTED, WorkflowStatus.WITHDRAWN].includes(newStatus) === false ||
      !(await this.shouldNotifyAboutApplicationUpdate(application.createdById))
    ) {
      return true;
    }

    try {
      const email = application.createdBy.email;
      const context = {
        applicationTitle: application.title,
        grantAmount: application.grantCall.totalGrantAmount,
        grantProgramSlug: application.grantCall.grantProgram.grantProgramSlug,
        grantCallSlug: application.grantCall.grantCallSlug,
        applicationId: application.id,
      };

      if (newStatus === WorkflowStatus.APPROVED) {
        await this.mailService.sendApplicationApprovedEmail(email, context, stageDefinitions);
      } else if (newStatus === WorkflowStatus.REJECTED) {
        await this.mailService.sendApplicationRejectedEmail(email, context, currentStage, stageDefinitions);
      } else if (newStatus === WorkflowStatus.WITHDRAWN) {
        await this.mailService.sendApplicationWithdrawnEmail(email, context);
      }

      this.logger.log(`Sent status update (${newStatus}) notifications for App ${application.id}`);

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to send status update notification for App ${application.id}: ${error.message}`,
        error.stack,
      );

      return false;
    }
  }

  async applicationStatusChangeForVoting(
    appVotingInfo: GrantApplicationVotingInfo,
    grantCall: GrantCall,
    currentStage: StageCode,
    newStatus: WorkflowStatus,
    stageDefinitions: StageDefinitionResponseDto[],
  ) {
    if (
      [WorkflowStatus.APPROVED, WorkflowStatus.REJECTED].includes(newStatus) === false ||
      !(await this.shouldNotifyAboutApplicationUpdate(appVotingInfo.createdBy.id))
    ) {
      return true;
    }

    try {
      const email = appVotingInfo.createdBy.email;
      const context = {
        applicationTitle: appVotingInfo.title,
        grantAmount: grantCall.totalGrantAmount,
        grantProgramSlug: grantCall.grantProgram.grantProgramSlug,
        grantCallSlug: grantCall.grantCallSlug,
        applicationId: appVotingInfo.id,
      };

      if (newStatus === WorkflowStatus.APPROVED) {
        await this.mailService.sendApplicationApprovedEmail(email, context, stageDefinitions);
      } else if (newStatus === WorkflowStatus.REJECTED) {
        await this.mailService.sendApplicationRejectedEmail(email, context, currentStage, stageDefinitions);
      }

      this.logger.log(`Sent status update (${newStatus}) notifications for App ${appVotingInfo.id}`);

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to send status update notification for App ${appVotingInfo.id}: ${error.message}`,
        error.stack,
      );

      return false;
    }
  }

  private async shouldNotifyAboutApplicationUpdate(userId: number) {
    const preferences = await this.notificationPreferenceService.find(userId);

    return preferences[NotificationType.GRANT_APPLICATION_UPDATE];
  }
}
