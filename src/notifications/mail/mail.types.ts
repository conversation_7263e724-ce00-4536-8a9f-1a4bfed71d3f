import { StageCode } from '../../workflow/enums/stage-code.enum';

export type AccountRecoveryContext = {
  name: string;
  token: string;
};

export type EmailVerificationContext = {
  name: string;
  otp: string;
};

export type GrantCallOpenForApplicationsContext = {
  grantCallTitle: string;
  grantProgramSlug: string;
  grantCallSlug: string;
};

export type GrantCallCommunityVotingStartedContext = {
  grantCallTitle: string;
  votingEndDate: string;
  grantProgramSlug: string;
  grantCallSlug: string;
};

export type GrantCallFinalCommunityVotingStartedContext = GrantCallCommunityVotingStartedContext;

export type GrantCallFinalizedContext = GrantCallOpenForApplicationsContext;

export type ApplicationCreatedContext = {
  applicationTitle: string;
  grantProgramSlug: string;
  grantCallSlug: string;
  applicationId: number;
};

export type ApplicationStage = {
  name: string;
  status: 'completed' | 'current' | 'next';
};

export type ApplicationTransitionedContext = {
  applicationTitle: string;
  stage: StageCode;
  stages: ApplicationStage[];
  grantProgramSlug: string;
  grantCallSlug: string;
  applicationId: number;
};

export type ApplicationApprovedContext = Omit<ApplicationTransitionedContext, 'stage'> & { grantAmount: number };
export type ApplicationRejectedContext = Omit<ApplicationTransitionedContext, 'stage'>;
export type ApplicationWithdrawnContext = ApplicationCreatedContext;

export type BalanceAlertContext = {
  balance: number;
  name: string;
};
