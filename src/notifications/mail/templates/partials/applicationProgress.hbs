<div class="application-progress">
  <div class="check-icon {{#if isSuccess}}success{{else}}error{{/if}}">
    {{#if isSuccess}}
      <img src="{{assetsUrl}}/icon-checkCircle.png" alt="" />
    {{else}}
      <img src="{{assetsUrl}}/icon-crossCircle.png" alt="" />
    {{/if}}
  </div>
  <table class="content">
    <tr>
      <td class="stages-list">
      {{#each stages}}
        <div
          class="
            stage
            {{#if (eq status "completed")}}completed{{/if}}
            {{#if (eq status "current")}}current{{/if}}
          "
        >
          <div class="stage-status">
            <div class="stage-status-icon"></div>
            {{#unless @last}}
              <div class="connecting-line"></div>
            {{/unless}}
          </div>
          <b class="stage-name">{{name}}</b>
        </div>
      {{/each}}
    </td>
      <td class="stage-description">
      {{#if isApproved}}
        <div class="header">Congratulations, you were approved for a Grant</div>
        <div>
          We're excited to share that this application successfully made its way through the THG process and was chosen by all of you in the community.<br />
          <br />
          We're thrilled to let you know it's been approved for a Grant!
        </div>
      {{else if (eq isApproved false)}}
        <div class="header">We regret to inform you that your grant application has not been accepted.</div>
        <div>
          We understand this news may be disappointing, but please know that we value your efforts and dedication. The decision was made following the desired community choice.<br />
          <br />
          We encourage you to keep pursuing your goals and consider applying again in the future.
        </div>
      {{else if (eq stage "GA_QUALIFICATION")}}
        <div class="header">Congratulations, you were moved to the stage <span>Qualification</span></div>
        <div>
          The application was approved for screening because it met the initial requirements for submission.
        </div>
      {{else if (eq stage "GA_INTERVIEW")}}
        <div class="header">Congratulations, you were moved to the stage <span>Interview</span></div>
        <div>
          You have successfully been selected by the community to move ot next stage. Next, you will be part of the Interview stage.
        </div>
      {{else if (eq stage "GA_DUE_DILIGENCE")}}
        <div class="header">Congratulations, you were moved to the stage <span>Due Diligence</span></div>
        <div>
          You have passed the Interview and now moved to Due Diligence stage.
        </div>
      {{else if (eq stage "GA_TOWN_HALL")}}
        <div class="header">Congratulations, you were moved to the stage <span>Town Hall</span></div>
        <div>
          You have successfully passed the Due Diligence and moved to Town Hall session.
        </div>
      {{else if (eq stage "GA_QUALIFICATION")}}
        <div class="header">Congratulations, you were moved to the stage <span>Final Qualification</span></div>
        <div>
          The application was approved for Final Qualification because it met the initial requirements for submission.
        </div>
      {{/if}}
    </td>
    </tr>
  </table>
</div>
<style>
  .application-progress .content {
    padding: 0 24px 32px;
  }


  .application-progress .check-icon {
    width: 56px;
    height: 56px;

    padding: 8px;

    border: 8px solid transparent;
    border-radius: 100%;
  }

  .application-progress .check-icon.success {
    border-color: {{style "color-success-50"}};
    background: {{style "color-success-100"}};
  }

  .application-progress .check-icon.error {
    border-color: {{style "color-error-50"}};
    background: {{style "color-error-100"}};
  }

  .application-progress .content {
    margin-top: 32px;
  }

  .application-progress .stages-list {
    width: 42%;

    vertical-align: top;
  }

  .application-progress .stage-description {
    width: 58%;

    padding-left: 11px;

    vertical-align: top;
  }

  .application-progress .stages-list .stage {
    color: {{style "color-gray-700"}};
  }

  .application-progress .stages-list .stage.current {
    color: {{style "color-brand-700"}};
  }

  .application-progress .stages-list .stage .stage-status {
    height: 100%;

    display: inline-block;
    vertical-align: top;
  }

  .application-progress .stages-list .stage .stage-status .stage-status-icon {
    width: 24px;
    height: 24px;

    border: 1.5px solid {{style "color-gray-200"}};
    border-radius: 100%;

    background: url('{{assetsUrl}}/icon-dot-gray.png') center no-repeat;
  }

  .application-progress .stages-list .stage.completed .stage-status .stage-status-icon,
  .application-progress .stages-list .stage.current .stage-status .stage-status-icon {
    background-color: {{style "color-brand-600"}};
    border-color: {{style "color-brand-600"}};
  }

  .application-progress .stages-list .stage.completed .stage-status .stage-status-icon {
    background-image: url('{{assetsUrl}}/icon-checkmark.png');
  }

  .application-progress .stages-list .stage.current .stage-status .stage-status-icon {
    background-image: url('{{assetsUrl}}/icon-dot-white.png');

    outline: 4px solid rgba(158, 119, 237, 0.24);
  }

  .application-progress .stages-list .stage .stage-status .connecting-line {
    width: 2px;
    height: 14px;

    margin: 4px auto;

    background: {{style "color-gray-200"}};

    border-radius: 2px;
  }

  .application-progress .stages-list .stage.completed .stage-status .connecting-line {
    background: {{style "color-brand-600"}};
  }

  .application-progress .stages-list .stage .stage-name {
    margin-left: 12px;

    display: inline-block;
    vertical-align: baseline;

    font-size: {{style "text-sm"}};
    font-weight: {{style "font-semibold"}};
  }

  .application-progress .stage-description div.header {
    margin-bottom: 4px;

    font-size: {{style "text-lg"}};
    line-height: {{style "text-lg-lh"}};
    font-weight: {{style "font-semibold"}};
    color: {{style "color-gray-900"}};
  }

  .application-progress .stage-description span {
    color: {{style "color-brand-600"}};
  }
</style>
