<style>
  body * {
    box-sizing: border-box;
  }

  body {
    margin: 0;
    padding: 0;

    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Helvetica, Arial, 'Noto Sans', 'Ubuntu', Tahoma, sans-serif;
    font-size: {{style "text-base"}};
    font-optical-sizing: auto;
    font-weight: {{style "font-regular"}};
    font-style: normal;
    line-height: 1.5;
    color: {{style "color-gray-600"}};
  }

  body > .container {
    max-width: 680px;

    margin: 0 auto;
    padding: 48px 20px 96px;
  }

  body > .container > .header {
    margin-bottom: 48px;
  }

  /* Title text */
  body > .container > .header h2 {
    font-size: 36px;
    line-height: 44px;
    letter-spacing: -0.02em;
    font-family: 'Styrene A Trial', Arial, 'Segoe UI', 'Helvetica Neue', Helvetica, 'Noto Sans', 'Ubuntu', <PERSON><PERSON><PERSON>, 'Futura', 'Avenir', sans-serif;

    font-weight: bold;
    text-transform: capitalize;
  }

  /* Title image */
  body > .container > .header img {
    max-width: 150px;

    margin-bottom: 40px;
  }

  /* Email content */
  body > .container > .content {
    margin-bottom: 80px;
  }

  body > .container > .content p:not(:last-child) {
    margin: 0 0 24px;
  }

  body > .container > .content p:last-child {
    margin: 0 0 24px;
  }

  /* Solid border */
  hr.solid {
    border-top: 1px solid {{style "color-dividers-default"}};
  }

  /* Footer content */
  body > .container > .footer {
    margin-top: 32px;

    color: {{style "color-gray-400"}};
    font-size: {{style "text-sm"}};
  }

  body > .container > .footer p:not(:last-child) {
    margin: 0 0 32px;
  }

  body > .container > .footer p:last-child {
    margin: 0;
  }

  /* Components: */
  a.button {
    padding: 10px 14px;

    display: inline-block;

    font-size: {{style "text-sm"}};
    font-weight: {{style "font-semibold"}};
    color: white;
    text-decoration: none;
    text-transform: capitalize;
    text-align: center;

    background: {{style "color-brand-600"}};

    border-radius: 4px;

    box-shadow: 0 1px 2px 0 rgba(16, 24, 40, 0.05);
  }

  /* Utils: */
  .w-full {
    width: 100%;
  }

  .text-brand-600 { color: {{style "color-brand-600"}}; }
</style>
