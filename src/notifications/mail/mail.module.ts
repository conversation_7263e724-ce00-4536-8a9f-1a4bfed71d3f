import { ConfigService } from '@nestjs/config';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { MailService } from './mail.service';
import { MailerModule } from '@nestjs-modules/mailer';
import { Module } from '@nestjs/common';
import * as path from 'path';
import { WorkflowModule } from '../../workflow/workflow.module';
import { getAwsSESTransporter, getLocalFileTransporter } from './transporters';
import { handlebarsStyles } from './mail.utils';

@Module({
  imports: [
    WorkflowModule,
    MailerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const enableMailPreview = configService.get<string>('EMAIL_PREVIEW') === 'true';

        const sesTransport = getAwsSESTransporter(
          configService.get<string>('AWS_SES_REGION'),
          configService.get<string>('AWS_SES_ACCESS_KEY'),
          configService.get<string>('AWS_SES_SECRET_KEY'),
          configService.get<string>('AWS_SES_ENDPOINT'),
        );
        const fileTransport = getLocalFileTransporter();

        return {
          transport: enableMailPreview ? fileTransport : sesTransport,
          template: {
            dir: path.join(process.cwd(), 'dist', 'notifications', 'mail', 'templates'),
            adapter: new HandlebarsAdapter(
              {
                currentYear: () => new Date().getFullYear(),
                appUrl: () => configService.get('FRONTEND_BASE_URL'),
                assetsUrl: () => configService.get('EMAIL_ASSETS_URL'),
                eq: (a, b) => a === b,
                style: (value) => handlebarsStyles[value],
              },
              {
                inlineCssEnabled: true,
                inlineCssOptions: {
                  inlineStyleTags: true,
                },
              },
            ),
            options: {
              strict: true,
            },
          },
          options: {
            partials: {
              dir: path.join(process.cwd(), 'dist', 'notifications', 'mail', 'templates', 'partials'),
              options: { strict: true },
            },
          },
        };
      },
    }),
  ],
  providers: [MailService],
  exports: [MailService],
})
export class MailModule {}
