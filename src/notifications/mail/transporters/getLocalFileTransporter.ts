import MailMessage from 'nodemailer/lib/mailer/mail-message';
import fs from 'fs';
import path from 'path';
import { TransportType } from '@nestjs-modules/mailer/dist/interfaces/mailer-options.interface';

export const getLocalFileTransporter = (): TransportType => ({
  name: 'mock',
  send: (mail: MailMessage, callback) => {
    const input = mail.message.createReadStream();
    const chunks: Buffer[] = [];

    input.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
    input.on('end', () => {
      const emailContent = Buffer.concat(chunks);
      const outputDir = '/app/temp';
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir);
      }

      const fileName = `mail-${Date.now()}.eml`;
      const filePath = path.join(outputDir, fileName);

      fs.writeFileSync(filePath, emailContent);
      console.log(`📨 Email saved as .eml: ${filePath}`);

      callback(null, {
        envelope: mail.data.envelope || mail.message.getEnvelope(),
        messageId: mail.message.messageId(),
      });
    });
  },
});
