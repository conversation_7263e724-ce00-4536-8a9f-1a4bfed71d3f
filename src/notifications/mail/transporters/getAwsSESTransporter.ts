import { TransportType } from '@nestjs-modules/mailer/dist/interfaces/mailer-options.interface';
import * as AWS from '@aws-sdk/client-ses';

export const getAwsSESTransporter = (
  region: string,
  accessKeyId: string,
  secretAccessKey: string,
  endpoint?: string,
): TransportType => ({
  SES: {
    ses: new AWS.SES({
      apiVersion: '2010-12-01',
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
      ...(endpoint && { endpoint }),
    }),
    aws: AWS,
  },
  sendingRate: 1, // 1 messages per second
});
