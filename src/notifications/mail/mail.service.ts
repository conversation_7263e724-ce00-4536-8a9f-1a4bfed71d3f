import { Injectable, Logger } from '@nestjs/common';

import { ConfigService } from '@nestjs/config';
import { MailerService } from '@nestjs-modules/mailer';
import { User } from '../../auth/entities/user.entity';
import { StageCode } from '../../workflow/enums/stage-code.enum';
import { WorkflowEntityType } from '../../workflow/enums/workflow-entity-type.enum';
import { WorkflowService } from '../../workflow/workflow.service';
import {
  AccountRecoveryContext,
  ApplicationApprovedContext,
  ApplicationCreatedContext,
  ApplicationRejectedContext,
  ApplicationStage,
  ApplicationTransitionedContext,
  ApplicationWithdrawnContext,
  BalanceAlertContext,
  EmailVerificationContext,
  GrantCallCommunityVotingStartedContext,
  GrantCallFinalCommunityVotingStartedContext,
  GrantCallFinalizedContext,
  GrantCallOpenForApplicationsContext,
} from './mail.types';
import { GrantApplication } from '../../grant-application/entities/grant-application.entity';
import { StageDefinitionResponseDto } from '../../workflow/dto/stage-definition.response.dto';
import { GrantCall } from '../../grant-call/entities/grant-call.entity';
import dayjs from 'dayjs';

@Injectable()
export class MailService {
  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
    private readonly workflowService: WorkflowService,
  ) {}

  private readonly logger = new Logger(MailService.name);

  private async sendEmail<T extends object>(to: string, subject: string, template: string, context: T) {
    try {
      await this.mailerService.sendMail({
        from: `THG Funding Platform <${this.configService.get<string>('AWS_SES_FROM_EMAIL')}>`,
        to,
        subject,
        template,
        context,
      });
    } catch (error) {
      this.logger.error(`Failed sending "${subject}" email: ${error}`);
      return false;
    }
    return true;
  }

  async sendAccountRecoveryEmail(user: User, token: string) {
    return this.sendEmail<AccountRecoveryContext>(user.email, 'Account Recovery', './user/account-recovery.hbs', {
      name: user.displayName,
      token,
    });
  }

  async sendEmailVerificationEmail(user: User, otp: string) {
    return this.sendEmail<EmailVerificationContext>(
      user.email,
      'Email Verification Code',
      './user/user-verification.hbs',
      {
        name: user.displayName,
        otp: otp,
      },
    );
  }

  async sendGrantCallOpenForApplicationsEmail(email: string, grantCall: GrantCall) {
    return this.sendEmail<GrantCallOpenForApplicationsContext>(
      email,
      'Grant Call is Open for Applications',
      './grant-call/open-for-applications.hbs',
      {
        grantCallTitle: grantCall.name,
        grantProgramSlug: grantCall.grantProgram.grantProgramSlug,
        grantCallSlug: grantCall.grantCallSlug,
      },
    );
  }

  async sendGrantCallCommunityVotingStartedEmail(email: string, grantCall: GrantCall) {
    return this.sendEmail<GrantCallCommunityVotingStartedContext>(
      email,
      'Grant Call is on Community voting',
      './grant-call/community-voting.hbs',
      {
        grantCallTitle: grantCall.name,
        grantProgramSlug: grantCall.grantProgram.grantProgramSlug,
        grantCallSlug: grantCall.grantCallSlug,
        votingEndDate: dayjs(grantCall.workflowState.currentStepEndsAt).format('DD-MM-YYYY HH:mm'),
      },
    );
  }

  async sendGrantCallFinalCommunityVotingStartedEmail(email: string, grantCall: GrantCall) {
    return this.sendEmail<GrantCallFinalCommunityVotingStartedContext>(
      email,
      'Grant Call is on Final Community voting',
      './grant-call/final-community-voting.hbs',
      {
        grantCallTitle: grantCall.name,
        grantProgramSlug: grantCall.grantProgram.grantProgramSlug,
        grantCallSlug: grantCall.grantCallSlug,
        votingEndDate: dayjs(grantCall.workflowState.currentStepEndsAt).format('DD-MM-YYYY HH:mm'),
      },
    );
  }

  async sendGrantCallFinalizedEmail(email: string, grantCall: GrantCall) {
    return this.sendEmail<GrantCallFinalizedContext>(email, 'Grant Call is Finalized', './grant-call/finalized.hbs', {
      grantCallTitle: grantCall.name,
      grantProgramSlug: grantCall.grantProgram.grantProgramSlug,
      grantCallSlug: grantCall.grantCallSlug,
    });
  }

  async sendApplicationCreatedEmail(email: string, application: GrantApplication) {
    return this.sendEmail<ApplicationCreatedContext>(
      email,
      'Grant Application Submitted',
      './grant-application/created.hbs',
      {
        applicationTitle: application.title,
        grantProgramSlug: application.grantCall.grantProgram.grantProgramSlug,
        grantCallSlug: application.grantCall.grantCallSlug,
        applicationId: application.id,
      },
    );
  }

  // Pass stageDefinitions property when you don't want to fetch them for application, useful in bulk transitions
  async sendApplicationTransitionedEmail(
    email: string,
    context: Omit<ApplicationTransitionedContext, 'stage' | 'stages'>,
    newStageCode: StageCode,
    stageDefinitions?: StageDefinitionResponseDto[],
  ) {
    return this.sendEmail<ApplicationTransitionedContext>(
      email,
      'Application Moved to Next Stage',
      './grant-application/moved.hbs',
      {
        ...context,
        stage: newStageCode,
        stages: await this.getApplicationStagesProgress(newStageCode, false, stageDefinitions),
      },
    );
  }

  // Pass stageDefinitions property when you don't want to fetch them for application, useful in bulk transitions
  private async getApplicationStagesProgress(
    stageCode: StageCode,
    markEveryStepAsCompleted: boolean = false,
    stageDefinitions?: StageDefinitionResponseDto[],
  ): Promise<ApplicationStage[]> {
    if (!stageDefinitions) {
      stageDefinitions = await this.workflowService.getStageDefinitionsForEntityType(WorkflowEntityType.APPLICATION);
    }

    const currentStageDefinitionIndex = stageDefinitions.findIndex((s) => s.code === stageCode);

    return [
      {
        name: 'Open a Grant Application',
        status: 'completed',
      },
      ...stageDefinitions.map<ApplicationStage>((stage, i) => ({
        name: stage.name,
        status:
          i < currentStageDefinitionIndex || markEveryStepAsCompleted
            ? 'completed'
            : i === currentStageDefinitionIndex
              ? 'current'
              : 'next',
      })),
    ];
  }

  // Pass stageDefinitions property when you don't want to fetch them for application, useful in bulk transitions
  async sendApplicationApprovedEmail(
    email: string,
    context: Omit<ApplicationApprovedContext, 'stage' | 'stages'>,
    stageDefinitions?: StageDefinitionResponseDto[],
  ) {
    return this.sendEmail<ApplicationApprovedContext>(
      email,
      'Grant Application Approved',
      './grant-application/approved.hbs',
      {
        ...context,
        stages: await this.getApplicationStagesProgress(StageCode.GA_FINAL_QUALIFICATION, true, stageDefinitions),
      },
    );
  }

  async sendApplicationRejectedEmail(
    email: string,
    context: Omit<ApplicationRejectedContext, 'stages'>,
    stageCode: StageCode,
    stageDefinitions?: StageDefinitionResponseDto[],
  ) {
    return this.sendEmail<ApplicationRejectedContext>(
      email,
      'Grant Application Rejected',
      './grant-application/rejected.hbs',
      {
        ...context,
        stages: await this.getApplicationStagesProgress(stageCode, false, stageDefinitions),
      },
    );
  }

  async sendApplicationWithdrawnEmail(email: string, context: ApplicationWithdrawnContext) {
    return this.sendEmail<ApplicationWithdrawnContext>(
      email,
      'Grant Application Withdrawn',
      './grant-application/withdrawn.hbs',
      context,
    );
  }

  async sendBalanceAlertEmail(
    email: string,
    alertLevel: 'alert' | 'warning' | 'critical',
    context: BalanceAlertContext,
  ) {
    return this.sendEmail<BalanceAlertContext>(email, 'Balance Alert', `./admin/balance-${alertLevel}.hbs`, context);
  }
}
