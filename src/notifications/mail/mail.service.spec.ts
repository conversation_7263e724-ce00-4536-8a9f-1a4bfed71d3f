import { Test, TestingModule } from '@nestjs/testing';

import { ConfigService } from '@nestjs/config';
import { MailService } from './mail.service';
import { MailerModule, MailerService } from '@nestjs-modules/mailer';
import { WorkflowService } from '../../workflow/workflow.service';
import { doIUseEmail } from '@jsx-email/doiuse-email';
import { User } from '../../auth/entities/user.entity';
import path from 'path';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { handlebarsStyles } from './mail.utils';
import { GrantCall } from '../../grant-call/entities/grant-call.entity';
import { GrantApplication } from '../../grant-application/entities/grant-application.entity';
import { StageCode } from '../../workflow/enums/stage-code.enum';
import { StageDefinitionResponseDto } from '../../workflow/dto/stage-definition.response.dto';
import { StageTransitionType } from '../../workflow/enums/stage-transition-type.enum';

describe('MailService', () => {
  let service: MailService;
  let mailerService: jest.Mocked<MailerService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        MailerModule.forRoot({
          transport: {
            jsonTransport: true, // this makes nodemailer return raw content instead of actually sending
          },
          template: {
            dir: path.join(process.cwd(), 'src', 'notifications', 'mail', 'templates'),
            adapter: new HandlebarsAdapter(
              {
                currentYear: () => new Date().getFullYear(),
                appUrl: () => 'https://frontend.com',
                assetsUrl: () => 'https://frontend-assets.com',
                eq: (a, b) => a === b,
                style: (value) => handlebarsStyles[value],
              },
              {
                inlineCssEnabled: true,
                inlineCssOptions: {
                  inlineStyleTags: true,
                },
              },
            ),
            options: {
              strict: true,
            },
          },
          options: {
            partials: {
              dir: path.join(process.cwd(), 'src', 'notifications', 'mail', 'templates', 'partials'),
              options: { strict: true },
            },
          },
        }),
      ],
      providers: [
        MailService,
        {
          provide: ConfigService,
          useFactory: () => ({
            get: jest.fn((key: string) => {
              if (key === 'AWS_SES_FROM_EMAIL') {
                return '<EMAIL>';
              }

              return null;
            }),
          }),
        },
        {
          provide: WorkflowService,
          useFactory: () => ({
            getStageDefinitionsForEntityType: jest.fn(),
          }),
        },
      ],
    }).compile();

    service = module.get<MailService>(MailService);
    mailerService = module.get(MailerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendEmail', () => {
    it('should send an email successfully', async () => {
      jest.spyOn(mailerService, 'sendMail').mockResolvedValue(undefined); // Mock successful sending

      const result = await service['sendEmail']('<EMAIL>', 'Test Subject', './test.template.hbs', {
        name: 'Test User',
      });

      expect(mailerService.sendMail).toHaveBeenCalledWith({
        from: 'THG Funding Platform <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'Test Subject',
        template: './test.template.hbs',
        context: {
          name: 'Test User',
        },
      });
      expect(result).toBe(true);
    });

    it('should handle failure when sending an email', async () => {
      jest.spyOn(mailerService, 'sendMail').mockRejectedValue(new Error('Failed to send email')); // Mock failure

      const result = await service['sendEmail']('<EMAIL>', 'Test Subject', './test.template.hbs', {
        name: 'Test User',
      });

      expect(mailerService.sendMail).toHaveBeenCalledWith({
        from: 'THG Funding Platform <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'Test Subject',
        template: './test.template.hbs',
        context: {
          name: 'Test User',
        },
      });
      expect(result).toBe(false);
    });
  });

  describe('Email templates compatibility', () => {
    beforeEach(() => {
      // Replace `sendMail` method with the one returning HTML of sent message, so we can check compatibility
      jest
        .spyOn(service, 'sendEmail' as any)
        .mockImplementation(async (to: string, subject: string, template: string, context: object) => {
          const response = await mailerService.sendMail({
            from: `mockfrom`,
            to,
            subject,
            template,
            context,
          });

          return JSON.parse(response.message)['html'];
        });
    });

    const validateEmailContentsSupport = (html: string) => {
      // Strip contents that are added by handlebars even if we don't ask him to do it:
      const validatedHtml = html
        .replace(
          `<html><head>
</head><body`,
          '<div',
        )
        .replace(`</body></html>`, '</div>');

      // List of email clients may be found here:
      // https://www.npmjs.com/package/@jsx-email/doiuse-email
      const result = doIUseEmail(validatedHtml, {
        emailClients: [
          'apple-mail.*',
          'gmail.*',
          '!gmail.mobile-webmail',
          'outlook.*',
          '!outlook.windows',
          '!outlook.windows-mail',
        ],
      });

      const nonErrorProperties = ['box-shadow'];

      if ('errors' in result) {
        const errors = result['errors'] as string[];
        const warns = errors.filter((e) => nonErrorProperties.map((errorProp) => e.includes(`\`${errorProp}\``)));
        const actualErrors = errors.filter((e) => !warns.includes(e));

        if (warns.length > 0) {
          console.warn('These errors are considered warnings and can be ignored:');
          console.warn(warns);
        }

        if (actualErrors.length > 0) {
          console.error(actualErrors);

          expect(true).toBe(false); // Intentionally fail the test
        }
      }
    };

    const mockStageDefinitions: StageDefinitionResponseDto[] = [
      {
        id: 1,
        code: StageCode.GA_SCREENING,
        name: 'Screening Stage',
        position: 10,
        workflowTemplateId: 0,
        transitionType: StageTransitionType.MANUAL,
        isTerminal: false,
      },
      {
        id: 2,
        code: StageCode.GA_QUALIFICATION,
        name: 'Voting Stage',
        position: 20,
        workflowTemplateId: 0,
        transitionType: StageTransitionType.MANUAL,
        isTerminal: false,
      },
      {
        id: 3,
        code: StageCode.GA_FINAL_QUALIFICATION,
        name: 'Final Voting Stage',
        position: 30,
        workflowTemplateId: 0,
        transitionType: StageTransitionType.MANUAL,
        isTerminal: false,
      },
    ];

    describe('sendAccountRecoveryEmail', () => {
      it('should generate no support errors', async () => {
        const renderedHtml = (await service.sendAccountRecoveryEmail(
          {
            email: '<EMAIL>',
            displayName: 'Test User',
          } as User,
          'token',
        )) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });
    });

    describe('sendEmailVerificationEmail', () => {
      it('should generate no support errors', async () => {
        const renderedHtml = (await service.sendEmailVerificationEmail(
          {
            email: '<EMAIL>',
            displayName: 'Test User',
          } as User,
          'otp',
        )) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });
    });

    describe('sendGrantCallOpenForApplicationsEmail', () => {
      it('should generate no support errors', async () => {
        const renderedHtml = (await service.sendGrantCallOpenForApplicationsEmail('<EMAIL>', {
          name: 'test',
          grantProgram: {
            grantProgramSlug: 'abcd',
          },
          grantCallSlug: 'def',
        } as GrantCall)) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });
    });

    describe('sendGrantCallCommunityVotingStartedEmail', () => {
      it('should generate no support errors', async () => {
        const renderedHtml = (await service.sendGrantCallCommunityVotingStartedEmail('<EMAIL>', {
          name: 'test',
          grantProgram: {
            grantProgramSlug: 'abcd',
          },
          grantCallSlug: 'def',
          workflowState: {
            currentStepEndsAt: new Date(),
          },
        } as GrantCall)) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });
    });

    describe('sendGrantCallFinalCommunityVotingStartedEmail', () => {
      it('should generate no support errors', async () => {
        const renderedHtml = (await service.sendGrantCallFinalCommunityVotingStartedEmail('<EMAIL>', {
          name: 'test',
          grantProgram: {
            grantProgramSlug: 'abcd',
          },
          grantCallSlug: 'def',
          workflowState: {
            currentStepEndsAt: new Date(),
          },
        } as GrantCall)) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });
    });

    describe('sendGrantCallFinalizedEmail', () => {
      it('should generate no support errors', async () => {
        const renderedHtml = (await service.sendGrantCallFinalizedEmail('<EMAIL>', {
          name: 'test',
          grantProgram: {
            grantProgramSlug: 'abcd',
          },
          grantCallSlug: 'def',
        } as GrantCall)) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });
    });

    describe('sendApplicationCreatedEmail', () => {
      it('should generate no support errors', async () => {
        const renderedHtml = (await service.sendApplicationCreatedEmail('<EMAIL>', {
          title: 'application title',
          id: 123,
          grantCall: {
            grantProgram: {
              grantProgramSlug: 'abcd',
            },
            grantCallSlug: 'def',
          },
        } as GrantApplication)) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });
    });

    describe('sendApplicationTransitionedEmail', () => {
      it('should generate no support errors', async () => {
        const renderedHtml = (await service.sendApplicationTransitionedEmail(
          '<EMAIL>',
          {
            grantProgramSlug: 'abcd',
            grantCallSlug: 'def',
            applicationId: 123,
            applicationTitle: 'application title',
          },
          StageCode.GA_QUALIFICATION,
          mockStageDefinitions,
        )) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });
    });

    describe('sendApplicationApprovedEmail', () => {
      it('should generate no support errors', async () => {
        const renderedHtml = (await service.sendApplicationApprovedEmail(
          '<EMAIL>',
          {
            grantProgramSlug: 'abcd',
            grantCallSlug: 'def',
            applicationId: 123,
            applicationTitle: 'application title',
            grantAmount: 999,
          },
          mockStageDefinitions,
        )) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });
    });

    describe('sendApplicationRejectedEmail', () => {
      it('should generate no support errors', async () => {
        const renderedHtml = (await service.sendApplicationRejectedEmail(
          '<EMAIL>',
          {
            grantProgramSlug: 'abcd',
            grantCallSlug: 'def',
            applicationId: 123,
            applicationTitle: 'application title',
          },
          StageCode.GA_QUALIFICATION,
          mockStageDefinitions,
        )) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });
    });

    describe('sendApplicationWithdrawnEmail', () => {
      it('should generate no support errors', async () => {
        const renderedHtml = (await service.sendApplicationWithdrawnEmail('<EMAIL>', {
          grantProgramSlug: 'abcd',
          grantCallSlug: 'def',
          applicationId: 123,
          applicationTitle: 'application title',
        })) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });
    });

    describe('sendBalanceAlertEmail', () => {
      it('should generate no support errors for alert template', async () => {
        const renderedHtml = (await service.sendBalanceAlertEmail('<EMAIL>', 'alert', {
          name: 'test name',
          balance: 3,
        })) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });

      it('should generate no support errors for warning template', async () => {
        const renderedHtml = (await service.sendBalanceAlertEmail('<EMAIL>', 'warning', {
          name: 'test name',
          balance: 3,
        })) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });

      it('should generate no support errors for critical template', async () => {
        const renderedHtml = (await service.sendBalanceAlertEmail('<EMAIL>', 'critical', {
          name: 'test name',
          balance: 3,
        })) as unknown as string;

        validateEmailContentsSupport(renderedHtml);
      });
    });
  });
});
