import { Test, TestingModule } from '@nestjs/testing';
import { Vote, VotingType } from '../votes/entities/vote.entity';

import { AuthService } from '../auth/auth.service';
import { ConfigService } from '@nestjs/config';
import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { GrantCallVotingSummaryDto } from './dto/grant-call-voting-summary.dto';
import { Repository } from 'typeorm';
import { VotesService } from '../votes/votes.service';
import { VotingOutcomeService } from './voting-outcome.service';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { getRepositoryToken } from '@nestjs/typeorm';
import { GrantApplicationService } from '../grant-application/grant-application.service';

const mockGrantApplicationData = (id: number, grantCallId: number, workflowStateId: number): GrantApplication => {
  return {
    id,
    grantCall: { id: grantCallId } as any,
    workflowStateId: workflowStateId,
  } as GrantApplication;
};

describe('VotingOutcomeService', () => {
  let service: VotingOutcomeService;
  let voteRepository: jest.Mocked<Repository<Vote>>;
  let votesService: jest.Mocked<VotesService>;
  let authService: jest.Mocked<AuthService>;

  const mockGrantApplicationService = {
    findOneForVotingResult: jest.fn(),
    findForVotingByGrantCallId: jest.fn(),
  };
  const DEFAULT_MIN_QUORUM_PERCENTAGE = 30;
  const DEFAULT_MIN_NET_SCORE_PERCENTAGE = 10;

  beforeEach(async () => {
    const mockVotesService = {
      getGrantCallParticipation: jest.fn(),
    };
    const mockAuthService = {
      getTotalKycVerifiedUsers: jest.fn(),
    };
    const mockVoteRepository = {
      find: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VotingOutcomeService,
        { provide: GrantApplicationService, useValue: mockGrantApplicationService },
        { provide: getRepositoryToken(Vote), useValue: mockVoteRepository },
        { provide: VotesService, useValue: mockVotesService },
        { provide: AuthService, useValue: mockAuthService },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === 'MINIMUM_QUORUM_PERCENTAGE') return DEFAULT_MIN_QUORUM_PERCENTAGE;
              if (key === 'MINIMUM_NET_VOTING_SCORE_PERCENTAGE') return DEFAULT_MIN_NET_SCORE_PERCENTAGE;
              return null;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<VotingOutcomeService>(VotingOutcomeService);
    voteRepository = module.get(getRepositoryToken(Vote));
    votesService = module.get(VotesService);
    authService = module.get(AuthService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processCommunityVotingResults', () => {
    const grantCallId = 1;
    const app1Data = mockGrantApplicationData(101, grantCallId, 1001);
    const app2Data = mockGrantApplicationData(102, grantCallId, 1002);

    beforeEach(() => {
      authService.getTotalKycVerifiedUsers.mockReset();
      votesService.getGrantCallParticipation.mockReset();
      mockGrantApplicationService.findForVotingByGrantCallId.mockReset();
      voteRepository.find.mockReset();
    });

    it('should qualify app1 and reject app2 when quorum is met for community voting', async () => {
      authService.getTotalKycVerifiedUsers.mockResolvedValue(100);
      votesService.getGrantCallParticipation.mockResolvedValue({ grantCallId, participationCount: 30 });
      mockGrantApplicationService.findForVotingByGrantCallId.mockResolvedValue([app1Data, app2Data]);

      voteRepository.find.mockImplementation(async (options: any) => {
        const appIdsInQuery = options.where.grantApplication.id.value;
        const voteTypeInQuery = options.where.voteType.value[0];
        const results: Partial<Vote>[] = [];
        if (voteTypeInQuery === VotingType.COMMUNITY_VOTING) {
          if (appIdsInQuery.includes(app1Data.id)) {
            results.push({
              grantApplication: { id: app1Data.id } as any,
              inFavorVotes: 15,
              againstVotes: 0,
              voteType: VotingType.COMMUNITY_VOTING,
            });
          }
          if (appIdsInQuery.includes(app2Data.id)) {
            results.push({
              grantApplication: { id: app2Data.id } as any,
              inFavorVotes: 5,
              againstVotes: 0,
              voteType: VotingType.COMMUNITY_VOTING,
            });
          }
        }
        return results as Vote[];
      });

      const result: GrantCallVotingSummaryDto = await service.processVotingResultsForGrantCall(
        grantCallId,
        VotingType.COMMUNITY_VOTING,
      );

      expect(
        result.categorizationResult.qualifiedApplications.some((a) => a.workflowStateId === app1Data.workflowStateId),
      ).toBeTruthy();
      expect(
        result.categorizationResult.rejectedApplications.some((a) => a.workflowStateId === app2Data.workflowStateId),
      ).toBeTruthy();
    });

    it('should reject all applications when quorum is NOT met for community voting', async () => {
      authService.getTotalKycVerifiedUsers.mockResolvedValue(100);
      votesService.getGrantCallParticipation.mockResolvedValue({ grantCallId, participationCount: 10 });
      mockGrantApplicationService.findForVotingByGrantCallId.mockResolvedValue([app1Data, app2Data]);
      voteRepository.find.mockResolvedValue([]);

      const result = await service.processVotingResultsForGrantCall(grantCallId, VotingType.COMMUNITY_VOTING);

      expect(result.categorizationResult.qualifiedApplications).toEqual([]);
      expect(result.categorizationResult.rejectedApplications.map((a) => a.workflowStateId)).toEqual(
        expect.arrayContaining([app1Data.workflowStateId, app2Data.workflowStateId]),
      );
    });

    it('should return empty outcomes if no applications are in the grant call for community voting', async () => {
      authService.getTotalKycVerifiedUsers.mockResolvedValue(100);
      votesService.getGrantCallParticipation.mockResolvedValue({ grantCallId, participationCount: 40 });
      mockGrantApplicationService.findForVotingByGrantCallId.mockResolvedValue([]);

      const result = await service.processVotingResultsForGrantCall(grantCallId, VotingType.COMMUNITY_VOTING);

      expect(result.categorizationResult.qualifiedApplications).toEqual([]);
      expect(result.categorizationResult.rejectedApplications).toEqual([]);
    });
  });

  describe('getVotingResultForSingleApplication', () => {
    const applicationId = 101;
    const grantCallId = 1;
    const workflowStateId = 1001;
    const appMockData = mockGrantApplicationData(applicationId, grantCallId, workflowStateId);

    beforeEach(() => {
      authService.getTotalKycVerifiedUsers.mockReset();
      votesService.getGrantCallParticipation.mockReset();
      mockGrantApplicationService.findForVotingByGrantCallId.mockReset();
      mockGrantApplicationService.findOneForVotingResult.mockReset();
    });

    it('should return qualified outcome when all criteria are met', async () => {
      mockGrantApplicationService.findOneForVotingResult.mockResolvedValue(appMockData);
      authService.getTotalKycVerifiedUsers.mockResolvedValue(100);
      votesService.getGrantCallParticipation.mockResolvedValue({ grantCallId, participationCount: 35 });

      voteRepository.find.mockResolvedValue([
        {
          grantApplication: { id: applicationId } as any,
          inFavorVotes: 15,
          againstVotes: 0,
          voteType: VotingType.COMMUNITY_VOTING,
        },
      ] as Vote[]);

      const result = await service.getVotingResultForSingleApplication(applicationId, VotingType.COMMUNITY_VOTING);

      expect(result.isQualified).toBe(true);
      expect(result.status).toBe(WorkflowStatus.READY_FOR_NEXT_STEP);
      expect(result.netScoreCriteriaMet).toBe(true);
      expect(result.netVotingScorePercentage).toBeCloseTo(15);
    });

    it('should return not qualified if grant call quorum is not met', async () => {
      mockGrantApplicationService.findOneForVotingResult.mockResolvedValue(appMockData);
      authService.getTotalKycVerifiedUsers.mockResolvedValue(100);
      votesService.getGrantCallParticipation.mockResolvedValue({ grantCallId, participationCount: 20 });
      voteRepository.find.mockResolvedValue([
        {
          grantApplication: { id: applicationId } as any,
          inFavorVotes: 15,
          againstVotes: 0,
          voteType: VotingType.COMMUNITY_VOTING,
        },
      ] as Vote[]);

      const result = await service.getVotingResultForSingleApplication(applicationId);

      expect(result.isQualified).toBe(false);
      expect(result.status).toBe(WorkflowStatus.REJECTED);
    });

    it('should return not qualified if application net score criteria is not met', async () => {
      mockGrantApplicationService.findOneForVotingResult.mockResolvedValue(appMockData);
      authService.getTotalKycVerifiedUsers.mockResolvedValue(100);
      votesService.getGrantCallParticipation.mockResolvedValue({ grantCallId, participationCount: 35 });
      voteRepository.find.mockResolvedValue([
        {
          grantApplication: { id: applicationId } as any,
          inFavorVotes: 5,
          againstVotes: 0,
          voteType: VotingType.COMMUNITY_VOTING,
        },
      ] as Vote[]);

      const result = await service.getVotingResultForSingleApplication(applicationId);

      expect(result.isQualified).toBe(false);
      expect(result.status).toBe(WorkflowStatus.REJECTED);
    });
  });
});
