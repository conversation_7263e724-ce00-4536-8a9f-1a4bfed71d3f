import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import { WorkflowStatus } from '../../workflow/enums/workflow-status.enum';

export class ApplicationVotingOutcomeDto {
  @ApiProperty({ description: 'The ID of the grant application.' })
  applicationId: number;

  @ApiProperty({ description: 'Number of "in favor" votes received.' })
  inFavorVotes: number;

  @ApiProperty({ description: 'Number of "against" votes received.' })
  againstVotes: number;

  @ApiProperty({ description: 'Net votes (in favor - against).' })
  netVotingScore: number;

  @ApiProperty({ description: 'Net voting score as a percentage of total KYC verified users in the system.' })
  netVotingScorePercentage: number;

  @ApiProperty({ description: 'Indicates if the application met its individual net voting score threshold.' })
  netScoreCriteriaMet: boolean;

  @ApiProperty({
    description:
      'Indicates if the application is qualified based on all criteria (grant call quorum and application net score).',
  })
  isQualified: boolean;

  @ApiPropertyOptional({
    enum: WorkflowStatus,
    enumName: 'WorkflowStatus',
    description: 'The new status of the application after processing (if updated).',
  })
  status: WorkflowStatus;
}
