import { Controller, Get, Param, Query, ParseIntPipe } from '@nestjs/common';
import { VotingOutcomeService } from './voting-outcome.service';
import { ApplicationVotingOutcomeDto } from './dto/application-voting-outcome.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { VotingType } from '../votes/entities/vote.entity';

@ApiTags('Voting Outcomes')
@Controller('voting-outcomes')
export class VotingOutcomeController {
  constructor(private readonly votingOutcomeService: VotingOutcomeService) {}

  @Get(':applicationId')
  @ApiOperation({ summary: 'Get the calculated voting result for a single grant application.' })
  @ApiResponse({
    status: 200,
    description: 'Voting outcome for the specified application.',
    type: ApplicationVotingOutcomeDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Application Not Found. The application with the specified ID does not exist.',
  })
  async getResultForSingleApplication(
    @Param('applicationId', ParseIntPipe) applicationId: number,
    @Query('voteType')
    voteTypeToView?: VotingType,
  ): Promise<ApplicationVotingOutcomeDto> {
    return this.votingOutcomeService.getVotingResultForSingleApplication(applicationId, voteTypeToView);
  }
}
