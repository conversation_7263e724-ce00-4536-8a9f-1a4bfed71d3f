import { forwardRef, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { In, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { VotesService } from '../votes/votes.service';
import { AuthService } from '../auth/auth.service';
import { Vote, VotingType } from '../votes/entities/vote.entity';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';

import { ApplicationVotingOutcomeDto } from './dto/application-voting-outcome.dto';
import {
  ApplicationScoresMap,
  ApplicationVotingInfo,
  CategorizedVotingOutcome,
  GrantApplicationVotingInfo,
} from './types/voting-service.types';
import { GrantCallVotingSummaryDto } from './dto/grant-call-voting-summary.dto';
import { GrantApplicationService } from '../grant-application/grant-application.service';

@Injectable()
export class VotingOutcomeService {
  private readonly logger = new Logger(VotingOutcomeService.name);
  private readonly minimumQuorumPercentage: number;
  private readonly minimumNetScorePercentageCommunityVoting: number;

  constructor(
    @Inject(forwardRef(() => GrantApplicationService))
    private readonly grantApplicationService: GrantApplicationService,
    @InjectRepository(Vote)
    private readonly voteRepository: Repository<Vote>,
    @Inject(forwardRef(() => VotesService))
    private readonly votesService: VotesService,
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {
    this.minimumQuorumPercentage = +this.configService.get('MINIMUM_QUORUM_PERCENTAGE') || 30;
    this.minimumNetScorePercentageCommunityVoting =
      +this.configService.get('MINIMUM_NET_VOTING_SCORE_PERCENTAGE') || 10;
  }

  public async processVotingResultsForGrantCall(
    grantCallId: number,
    voteTypeForThisStage: VotingType,
  ): Promise<GrantCallVotingSummaryDto> {
    const applicationsInCall = await this.grantApplicationService.findForVotingByGrantCallId(grantCallId);

    if (!applicationsInCall.length) {
      this.logger.warn(`No applications found for Grant Call ID: ${grantCallId}.`);

      return {
        categorizationResult: {
          qualifiedApplications: [],
          rejectedApplications: [],
        },
        allAppScoresMap: new Map(),
      };
    }

    const totalKycVerifiedUsers = await this.getTotalKycVerifiedUsers();
    const quorumMet = await this.calculateGrantCallQuorum(grantCallId, voteTypeForThisStage, totalKycVerifiedUsers);

    const appIds = applicationsInCall.map((app) => app.id);

    const typesToFetchForScores = Array.from(new Set([voteTypeForThisStage, VotingType.COMMUNITY_VOTING]));

    const allAppScoresMap = await this.fetchAllVoteScoresForGrantCall(appIds, typesToFetchForScores);

    const allProcessedAppData = applicationsInCall.map((app) =>
      this.prepareApplicationStageData(app, totalKycVerifiedUsers, voteTypeForThisStage, allAppScoresMap),
    );

    let categorizationResult: CategorizedVotingOutcome;

    if (voteTypeForThisStage === VotingType.COMMUNITY_VOTING) {
      categorizationResult = this.categorizeCommunityVotingApps(allProcessedAppData, quorumMet);
    } else if (voteTypeForThisStage === VotingType.FINAL_COMMUNITY_VOTING) {
      categorizationResult = this.categorizeFinalVotingApps(allProcessedAppData, quorumMet);
    }

    return {
      categorizationResult,
      allAppScoresMap,
    };
  }

  public async getVotingResultForSingleApplication(
    applicationId: number,
    voteTypeToView: VotingType = VotingType.COMMUNITY_VOTING,
  ): Promise<ApplicationVotingOutcomeDto> {
    const application = await this.grantApplicationService.findOneForVotingResult(applicationId);

    if (!application) {
      throw new NotFoundException(`Application with ID ${applicationId} not found.`);
    }

    const totalKycVerifiedUsers = await this.getTotalKycVerifiedUsers();

    const quorumMet = await this.calculateGrantCallQuorum(
      application.grantCall.id,
      voteTypeToView,
      totalKycVerifiedUsers,
    );

    const appScoresMap = await this.fetchAllVoteScoresForGrantCall([applicationId], [voteTypeToView]);

    const appSpecificScores = appScoresMap.get(applicationId)?.[voteTypeToView] || {
      netScore: 0,
      inFavor: 0,
      against: 0,
    };

    let netVotingScorePercentage: number | undefined;

    if (voteTypeToView === VotingType.COMMUNITY_VOTING) {
      netVotingScorePercentage =
        totalKycVerifiedUsers > 0 ? (appSpecificScores.netScore / totalKycVerifiedUsers) * 100 : 0;
    }

    const meetsIndividualRule = this.checkIndividualRule(
      voteTypeToView,
      appSpecificScores.netScore,
      netVotingScorePercentage,
    );

    const isOverallQualified = quorumMet && meetsIndividualRule;

    return {
      applicationId: application.id,
      inFavorVotes: appSpecificScores.inFavor,
      againstVotes: appSpecificScores.against,
      netVotingScore: appSpecificScores.netScore,
      netVotingScorePercentage,
      netScoreCriteriaMet: meetsIndividualRule,
      isQualified: isOverallQualified,
      status: isOverallQualified ? WorkflowStatus.READY_FOR_NEXT_STEP : WorkflowStatus.REJECTED,
    };
  }

  public prepareApplicationStageData(
    application: GrantApplicationVotingInfo,
    totalKycVerifiedUsers: number,
    currentStageVoteType: VotingType,
    allAppScoresMap: ApplicationScoresMap,
  ): ApplicationVotingInfo {
    const appScoresForTypes = allAppScoresMap.get(application.id) || {};
    const currentStageScores = appScoresForTypes[currentStageVoteType] || {
      netScore: 0,
      inFavor: 0,
      against: 0,
    };
    const communityVotingScoreData = appScoresForTypes[VotingType.COMMUNITY_VOTING];

    let currentStageNetScorePercentage: number | undefined;

    if (currentStageVoteType === VotingType.COMMUNITY_VOTING) {
      currentStageNetScorePercentage =
        totalKycVerifiedUsers > 0 ? (currentStageScores.netScore / totalKycVerifiedUsers) * 100 : 0;
    }

    return {
      ...application,
      currentStageNetScore: currentStageScores.netScore,
      communityVotingNetScoreForTieBreak: communityVotingScoreData?.netScore ?? -Infinity,
      meetsIndividualRule: this.checkIndividualRule(
        currentStageVoteType,
        currentStageScores.netScore,
        currentStageNetScorePercentage,
      ),
    };
  }

  public static buildScoresMapFromVoteRecords(voteRecords: Vote[]): ApplicationScoresMap {
    return voteRecords.reduce((scoresMap, record) => {
      if (!record.grantApplication?.id) {
        return scoresMap;
      }

      const appId = record.grantApplication.id;

      const appScores = scoresMap.get(appId) ?? {};

      appScores[record.voteType] = {
        inFavor: record.inFavorVotes,
        against: record.againstVotes,
        netScore: record.inFavorVotes - record.againstVotes,
      };

      scoresMap.set(appId, appScores);

      return scoresMap;
    }, new Map() as ApplicationScoresMap);
  }

  public getTotalKycVerifiedUsers(): Promise<number> {
    return this.authService.getTotalKycVerifiedUsers();
  }

  private async fetchAllVoteScoresForGrantCall(
    applicationIds: number[],
    voteTypesToFetch: VotingType[],
  ): Promise<ApplicationScoresMap> {
    const scoresMap: ApplicationScoresMap = new Map();

    if (!applicationIds.length || !voteTypesToFetch.length) {
      return scoresMap;
    }

    const voteRecords = await this.voteRepository.find({
      where: {
        grantApplication: { id: In(applicationIds) },
        voteType: In(voteTypesToFetch),
      },
      relations: { grantApplication: true },
      select: {
        grantApplication: { id: true },
        inFavorVotes: true,
        againstVotes: true,
        voteType: true,
      },
    });

    return VotingOutcomeService.buildScoresMapFromVoteRecords(voteRecords);
  }

  public async calculateGrantCallQuorum(
    grantCallId: number,
    voteTypeForQuorum: VotingType,
    totalKycVerifiedUsers: number,
  ): Promise<boolean> {
    const { participationCount: participationGc } = await this.votesService.getGrantCallParticipation(
      grantCallId,
      voteTypeForQuorum,
    );

    const participationPercentage = totalKycVerifiedUsers > 0 ? (participationGc / totalKycVerifiedUsers) * 100 : 0;

    return participationPercentage >= this.minimumQuorumPercentage;
  }

  private checkIndividualRule(voteType: VotingType, netScore: number, netScorePercentage?: number): boolean {
    if (voteType === VotingType.COMMUNITY_VOTING) {
      return netScorePercentage !== undefined && netScorePercentage >= this.minimumNetScorePercentageCommunityVoting;
    }

    if (voteType === VotingType.FINAL_COMMUNITY_VOTING) {
      return netScore > 0;
    }

    return false;
  }

  private rankAndCategorizeFinalists(
    appsMeetingIndividualRule: ApplicationVotingInfo[],
    topN: number = 3,
  ): CategorizedVotingOutcome {
    if (!appsMeetingIndividualRule.length) {
      return { qualifiedApplications: [], rejectedApplications: [] };
    }

    appsMeetingIndividualRule.sort((a, b) => {
      if (b.currentStageNetScore !== a.currentStageNetScore) return b.currentStageNetScore - a.currentStageNetScore;
      return b.communityVotingNetScoreForTieBreak - a.communityVotingNetScoreForTieBreak;
    });

    return {
      qualifiedApplications: appsMeetingIndividualRule.slice(0, topN),
      rejectedApplications: appsMeetingIndividualRule.slice(topN),
    };
  }

  public categorizeCommunityVotingApps(
    appDataList: ApplicationVotingInfo[],
    quorumMet: boolean,
  ): CategorizedVotingOutcome {
    const result: CategorizedVotingOutcome = {
      qualifiedApplications: [],
      rejectedApplications: [],
    };

    appDataList.forEach((app) => {
      const isOverallQualified = quorumMet && app.meetsIndividualRule;

      if (isOverallQualified) {
        result.qualifiedApplications.push(app);
      } else {
        result.rejectedApplications.push(app);
      }
    });

    return result;
  }

  public categorizeFinalVotingApps(appDataList: ApplicationVotingInfo[], quorumMet: boolean): CategorizedVotingOutcome {
    if (!quorumMet) {
      return {
        qualifiedApplications: [],
        rejectedApplications: appDataList,
      };
    }

    const initiallyEligible = appDataList.filter((app) => app.meetsIndividualRule);

    const notEligibleFromStartIds = appDataList.filter((app) => !app.meetsIndividualRule);

    const { qualifiedApplications, rejectedApplications: rejectedOutcomes } =
      this.rankAndCategorizeFinalists(initiallyEligible);

    return {
      qualifiedApplications,
      rejectedApplications: [...rejectedOutcomes, ...notEligibleFromStartIds],
    };
  }
}
