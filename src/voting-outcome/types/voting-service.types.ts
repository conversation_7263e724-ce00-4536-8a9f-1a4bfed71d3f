import { PickType } from '@nestjs/swagger';
import { VotingType } from '../../votes/entities/vote.entity';
import { GrantApplication } from '../../grant-application/entities/grant-application.entity';
import { User } from '../../auth/entities/user.entity';

export type BasicScoreData = { netScore: number; inFavor: number; against: number };

export type ApplicationScoresMap = Map<number, Partial<Record<VotingType, BasicScoreData>>>;

export class GrantApplicationVotingInfo extends PickType(GrantApplication, [
  'id',
  'title',
  'workflowStateId',
  'actionTopicId',
  'votingTopicId',
  'finalVotingTopicId',
] as const) {
  createdBy: Pick<User, 'id' | 'email'>;
}

export type ApplicationVotingInfo = GrantApplicationVotingInfo & {
  currentStageNetScore: number;
  communityVotingNetScoreForTieBreak: number;
  meetsIndividualRule: boolean;
};

export type CategorizedVotingOutcome = {
  qualifiedApplications: ApplicationVotingInfo[];
  rejectedApplications: ApplicationVotingInfo[];
};
