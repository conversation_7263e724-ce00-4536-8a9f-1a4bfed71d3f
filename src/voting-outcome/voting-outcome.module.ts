import { AuthModule } from '../auth/auth.module';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Vote } from '../votes/entities/vote.entity';
import { VotesModule } from '../votes/votes.module';
import { VotingOutcomeController } from './voting-outcome.controller';
import { VotingOutcomeService } from './voting-outcome.service';
import { GrantApplicationModule } from '../grant-application/grant-application.module';

@Module({
  imports: [TypeOrmModule.forFeature([Vote]), forwardRef(() => GrantApplicationModule), VotesModule, AuthModule],
  controllers: [VotingOutcomeController],
  providers: [VotingOutcomeService],
  exports: [VotingOutcomeService],
})
export class VotingOutcomeModule {}
