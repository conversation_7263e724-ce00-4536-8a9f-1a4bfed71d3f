import { Injectable, NotFoundException, UnprocessableEntityException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager, DataSource, MoreThan, In } from 'typeorm';
import { WorkflowState } from './entities/workflow-state.entity';
import { WorkflowStepDefinition } from './entities/workflow-step-definition.entity';
import { GrantCallStageSetting } from '../grant-call/entities/grant-call-stage-setting.entity';
import { WorkflowStatus } from './enums/workflow-status.enum';
import { WorkflowEntityType } from './enums/workflow-entity-type.enum';
import { GrantCall } from '../grant-call/entities/grant-call.entity';
import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { StageCode } from './enums/stage-code.enum';
import { SingleTransitionResponseDto } from './dto/single-transition-response.dto';
import { BulkTransitionResponseDto } from './dto/bulk-transition-response.dto';
import { GrantProgram } from '../grant-program/entities/grant-program.entity';

@Injectable()
export class WorkflowTransitionService {
  private readonly logger = new Logger(WorkflowTransitionService.name);

  constructor(
    @InjectRepository(WorkflowStepDefinition)
    private readonly stepDefinitionRepository: Repository<WorkflowStepDefinition>,
    private readonly dataSource: DataSource,
  ) {}

  async transitionSingleStateToNextStep(
    entityType: WorkflowEntityType,
    entityId: number,
    transactionalEntityManager?: EntityManager,
  ): Promise<SingleTransitionResponseDto> {
    const manager = transactionalEntityManager ?? this.dataSource.manager;

    const currentState = await this.findWorkflowStateByEntity(entityType, entityId, transactionalEntityManager);
    const nextStepDefinition = await this.getNextStepDefinition(currentState.currentStepDefinition);

    await this.performStateTransition(
      currentState,
      nextStepDefinition,
      WorkflowStatus.IN_PROGRESS,
      entityType,
      entityId,
      manager,
    );

    return { stepDefinitionCode: nextStepDefinition.code, status: currentState.status };
  }

  async setSpecificStage(
    entityType: WorkflowEntityType,
    entityId: number,
    targetStageCode: StageCode,
    transactionalEntityManager?: EntityManager,
  ): Promise<SingleTransitionResponseDto> {
    const manager = transactionalEntityManager ?? this.dataSource.manager;
    const currentState = await this.findWorkflowStateByEntity(entityType, entityId, manager);

    this.logger.log(
      `Setting specific stage: ${targetStageCode} for ${entityType} ID ${entityId}, workflow template ID: ${currentState.workflowTemplateId}`,
    );

    const targetStepDefinition = await manager.getRepository(WorkflowStepDefinition).findOne({
      where: { workflowTemplateId: currentState.workflowTemplateId, code: targetStageCode },
    });

    if (!targetStepDefinition) {
      this.logger.error(
        `Target step definition not found: code=${targetStageCode}, workflowTemplateId=${currentState.workflowTemplateId}`,
      );

      // Let's also check what step definitions exist for this template
      const availableSteps = await manager.getRepository(WorkflowStepDefinition).find({
        where: { workflowTemplateId: currentState.workflowTemplateId },
        select: ['id', 'code', 'name'],
      });

      this.logger.error(
        `Available step definitions for template ${currentState.workflowTemplateId}: ${JSON.stringify(availableSteps)}`,
      );

      throw new UnprocessableEntityException(
        `Target step definition '${targetStageCode}' not found for workflow template ${currentState.workflowTemplateId}. Available steps: ${availableSteps.map((s) => s.code).join(', ')}`,
      );
    }

    await this.performStateTransition(
      currentState,
      targetStepDefinition,
      currentState.status,
      entityType,
      entityId,
      manager,
    );
    return { stepDefinitionCode: targetStepDefinition.code, status: currentState.status };
  }

  async bulkTransitionReadyApplicationsForCall(
    grantCallId: number,
    currentStepDefId: number,
    transactionalEntityManager?: EntityManager,
  ): Promise<BulkTransitionResponseDto> {
    const manager = transactionalEntityManager ?? this.dataSource.manager;

    const currentStepDef = await this.stepDefinitionRepository.findOneBy({ id: currentStepDefId });

    if (!currentStepDef) {
      throw new UnprocessableEntityException(`Current step definition (ID: ${currentStepDefId}) not found.`);
    }

    return manager
      .transaction(async (transactionalEntityManager) => {
        let transitionedCount = 0;

        const nextStepDef = await this.getNextStepDefinition(currentStepDef);

        const relevantApplications = await this.getActiveApplicationStatesInStep(
          grantCallId,
          currentStepDefId,
          transactionalEntityManager,
        );

        const totalRelevantApplications = relevantApplications.length;

        if (totalRelevantApplications > 0) {
          const readyApplicationsCount = relevantApplications.filter(
            (app) => app.status === WorkflowStatus.READY_FOR_NEXT_STEP,
          ).length;

          if (readyApplicationsCount < totalRelevantApplications) {
            this.logger.warn(
              `Bulk App Tx PREVENTED for Call ${grantCallId}, App Stage ${currentStepDef.code}: ` +
                `${readyApplicationsCount}/${totalRelevantApplications} applications are ready. All must be ready.`,
            );

            throw new UnprocessableEntityException(
              `Cannot transition applications for Grant Call ${grantCallId}: Not all applications ` +
                `(${readyApplicationsCount}/${totalRelevantApplications}) in stage '${currentStepDef.name}' ` +
                `are flagged as 'Ready for Next Step'.`,
            );
          }
        }

        const eligibleStateIds = relevantApplications.map((state) => state.id);

        if (eligibleStateIds.length > 0) {
          this.logger.log(
            `Bulk Tx: Found ${eligibleStateIds.length} ready apps for Call ${grantCallId} at Step ${currentStepDef.code}. Moving to ${nextStepDef.code}.`,
          );

          const transitionTime = new Date();

          await transactionalEntityManager
            .getRepository(WorkflowState)
            .createQueryBuilder()
            .update(WorkflowState)
            .set({ currentStepEndsAt: transitionTime })
            .whereInIds(eligibleStateIds)
            .andWhere('currentStepEndsAt IS NULL')
            .execute();

          const updateResult = await transactionalEntityManager
            .getRepository(WorkflowState)
            .createQueryBuilder()
            .update(WorkflowState)
            .set({
              currentStepDefinitionId: nextStepDef.id,
              status: WorkflowStatus.IN_PROGRESS,
              currentStepTransitionedAt: transitionTime,
              currentStepEndsAt: null,
            })
            .whereInIds(eligibleStateIds)
            .execute();

          transitionedCount = updateResult.affected ?? 0;
        } else {
          this.logger.log(
            `Bulk Tx: No Applications for Call ${grantCallId} found ready at Step ${currentStepDef.code}.`,
          );
        }

        this.logger.log(`Bulk Tx completed for Call ${grantCallId}: ${transitionedCount} apps moved.`);

        return {
          transitionedCount,
          transitionedApplicationIds: relevantApplications.map((workflowState) => workflowState.applicationid),
        };
      })
      .catch((err) => {
        this.logger.error(
          `Bulk Tx Error (Call ${grantCallId}, From Step ${currentStepDefId}): ${err.message}`,
          err.stack,
        );

        throw err;
      });
  }

  async bulkSetStatusForWorkflowStateIds(
    workflowStateIds: number[],
    newStatus: WorkflowStatus,
    transactionalEntityManager?: EntityManager,
  ): Promise<number> {
    const manager = transactionalEntityManager ?? this.dataSource.manager;

    const result = await manager
      .getRepository(WorkflowState)
      .update({ id: In(workflowStateIds) }, { status: newStatus });

    this.logger.log(`Bulk set status to '${newStatus}' for ${result.affected || 0} WorkflowStates.`);

    return result.affected || 0;
  }

  async updateStateStatus(
    entityType: WorkflowEntityType,
    entityId: number,
    newStatus: WorkflowStatus,
    transactionalEntityManager?: EntityManager,
  ): Promise<WorkflowState> {
    const manager = transactionalEntityManager ?? this.dataSource.manager;
    const state = await this.findWorkflowStateByEntity(entityType, entityId, manager);

    this.validateStatus(state, newStatus);

    state.status = newStatus;
    this.logger.log(`Updating WFS ID ${state.id} status to ${newStatus}.`);

    return manager.save(WorkflowState, state);
  }

  async findWorkflowStateByEntity(
    entityType: WorkflowEntityType,
    entityId: number,
    transactionalEntityManager?: EntityManager,
  ): Promise<WorkflowState> {
    const manager = transactionalEntityManager ?? this.dataSource.manager;

    if (![WorkflowEntityType.CALL, WorkflowEntityType.APPLICATION, WorkflowEntityType.PROGRAM].includes(entityType)) {
      throw new UnprocessableEntityException(`Workflow state lookup not supported for entity type '${entityType}'.`);
    }

    const grantEntityMap = {
      [WorkflowEntityType.CALL]: GrantCall,
      [WorkflowEntityType.APPLICATION]: GrantApplication,
      [WorkflowEntityType.PROGRAM]: GrantProgram,
    };

    const grantEntity = grantEntityMap[entityType];

    const entityQueryBuilder = manager
      .createQueryBuilder(grantEntity, 'ge')
      .leftJoinAndSelect('ge.workflowState', 'ws')
      .leftJoinAndSelect('ws.currentStepDefinition', 'wsd')
      .where('ge.id = :entityId', { entityId });

    const entityWithState = await entityQueryBuilder.getOne();
    const workflowState = entityWithState?.workflowState;

    if (!entityWithState?.workflowState?.currentStepDefinition?.id || !entityWithState?.workflowState?.id) {
      const errorMessage = `Workflow state or definition not found/linked for ${entityType} ID ${entityId}.`;

      this.logger.error(errorMessage + ` Entity found: ${!!entityWithState}, WF State found: ${!!workflowState}`);

      throw new NotFoundException(errorMessage);
    }

    return workflowState;
  }

  private async performStateTransition(
    currentState: WorkflowState,
    newStepDefinition: WorkflowStepDefinition,
    newStatus: WorkflowStatus,
    entityTypeForEndDateCalc: WorkflowEntityType,
    entityIdForEndDateCalc: number,
    manager: EntityManager,
  ): Promise<void> {
    const transitionTime = new Date();
    let newStepEndDate: Date | null = null;

    currentState.currentStepDefinition = newStepDefinition;
    currentState.currentStepDefinitionId = newStepDefinition.id;
    currentState.status = newStatus;
    currentState.currentStepTransitionedAt = transitionTime;

    if (entityTypeForEndDateCalc === WorkflowEntityType.CALL) {
      newStepEndDate = await this.calculateStepEndDate(
        transitionTime,
        newStepDefinition,
        entityIdForEndDateCalc,
        manager,
      );
    }
    currentState.currentStepEndsAt = newStepEndDate;

    await manager.save(WorkflowState, currentState);
  }

  private async getNextStepDefinition(currentStepDefinition: WorkflowStepDefinition): Promise<WorkflowStepDefinition> {
    if (currentStepDefinition.isTerminal) {
      throw new UnprocessableEntityException(`Cannot transition from terminal step '${currentStepDefinition.name}'.`);
    }

    const nextStepDefinition = await this.stepDefinitionRepository.findOne({
      where: {
        workflowTemplateId: currentStepDefinition.workflowTemplateId,
        sequenceNumber: MoreThan(currentStepDefinition.sequenceNumber),
      },
      order: {
        sequenceNumber: 'ASC',
      },
    });

    if (!nextStepDefinition) {
      throw new UnprocessableEntityException(
        `Next step definition not found (after ${currentStepDefinition.sequenceNumber}) for template ${currentStepDefinition.workflowTemplateId}.`,
      );
    }

    return nextStepDefinition;
  }

  private validateStatus(currentState: WorkflowState, newStatus: WorkflowStatus) {
    const currentStatus = currentState.status;

    // Check if current status is terminal (cannot be changed)
    if ([WorkflowStatus.REJECTED, WorkflowStatus.WITHDRAWN, WorkflowStatus.CLOSED].includes(currentStatus)) {
      throw new UnprocessableEntityException(
        `Cannot change workflow status from existing terminal status '${currentStatus}'.`,
      );
    }

    if (currentStatus === newStatus) {
      this.logger.warn(
        `Attempting to set workflow status to '${newStatus}' which is already the current status for WFS ID ${currentState.id}.`,
      );

      throw new UnprocessableEntityException(`Workflow status is already '${newStatus}'.`);
    }

    // Application-specific validation for APPROVED status
    if (
      newStatus === WorkflowStatus.APPROVED &&
      currentState.currentStepDefinition?.code !== StageCode.GA_FINAL_QUALIFICATION
    ) {
      throw new UnprocessableEntityException(
        `Applications can only have status set to APPROVED when in stage '${StageCode.GA_FINAL_QUALIFICATION}'. Current: ${currentState.currentStepDefinition?.code}`,
      );
    }
  }

  private async calculateStepEndDate(
    startDate: Date,
    stepDefinition: WorkflowStepDefinition,
    grantCallId: number,
    transactionalEntityManager: EntityManager,
  ): Promise<Date | null> {
    try {
      const setting = await transactionalEntityManager.findOne(GrantCallStageSetting, {
        where: { grantCallId, workflowStepDefinitionId: stepDefinition.id },
      });

      if (setting?.endDate) {
        return setting.endDate;
      }

      if (setting?.durationSeconds) {
        const endDate = new Date(startDate.getTime());
        endDate.setSeconds(endDate.getSeconds() + setting.durationSeconds);
        return endDate;
      }
    } catch (error) {
      this.logger.error(`Error fetching stage setting for call ${grantCallId}, step ${stepDefinition.id}`, error.stack);
    }

    return null;
  }

  private async getActiveApplicationStatesInStep(
    grantCallId: number,
    currentStepDefId: number,
    manager: EntityManager,
  ): Promise<(Pick<WorkflowState, 'id' | 'status'> & { applicationid: number })[]> {
    const queryBuilder = manager.createQueryBuilder(WorkflowState, 'ws');

    queryBuilder
      .select(['ws.id AS id', 'ws.status AS status', 'ga.id AS applicationid'])
      .innerJoin('grant_application', 'ga', 'ga.workflowStateId = ws.id')
      .where('ga.grantCallId = :grantCallId', { grantCallId })
      .andWhere('ws.currentStepDefinitionId = :currentStepDefId', { currentStepDefId })
      .andWhere('ws.status NOT IN (:...excludedStatuses)', {
        excludedStatuses: [WorkflowStatus.REJECTED, WorkflowStatus.WITHDRAWN],
      });

    return await queryBuilder.getRawMany<{ id: number; status: WorkflowStatus; applicationid: number }>();
  }
}
