import { ApiProperty } from '@nestjs/swagger';

export class WorkflowStateDto {
  @ApiProperty({ example: 12, description: 'Internal workflow-state ID' })
  id: number;

  @ApiProperty({
    example: '2025-05-29T18:02:02.531Z',
    description: 'When the current step began',
  })
  currentStepTransitionedAt: Date;

  @ApiProperty({
    example: '2025-05-28T18:30:00.000Z',
    description: 'When the current step end',
  })
  currentStepEndsAt: Date;
}
