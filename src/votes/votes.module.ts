import { ApplicationVotesController } from './application-votes.controller';
import { AuthModule } from '../auth/auth.module';
import { HederaModule } from '../hedera/hedera.module';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../auth/entities/user.entity';
import { Vote } from './entities/vote.entity';
import { VotesService } from './votes.service';
import { GrantApplicationModule } from '../grant-application/grant-application.module';

@Module({
  imports: [TypeOrmModule.forFeature([Vote, User]), AuthModule, HederaModule, forwardRef(() => GrantApplicationModule)],
  controllers: [ApplicationVotesController],
  providers: [VotesService],
  exports: [VotesService],
})
export class VotesModule {}
