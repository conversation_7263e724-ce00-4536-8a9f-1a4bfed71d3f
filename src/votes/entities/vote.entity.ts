import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON>ty,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { GrantApplication } from '../../grant-application/entities/grant-application.entity';

export enum VotingType {
  COMMUNITY_VOTING = 'community_voting',
  FINAL_COMMUNITY_VOTING = 'final_community_voting',
}

@Entity('votes')
export class Vote {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => GrantApplication)
  @JoinColumn()
  grantApplication: GrantApplication;

  @Column({ type: 'int', default: 0 })
  inFavorVotes: number;

  @Column({ type: 'int', default: 0 })
  againstVotes: number;

  @Column({ type: 'varchar', array: true })
  walletsInFavor: string[];

  @Column({ type: 'varchar', array: true })
  walletsAgainst: string[];

  @Column({ type: 'enum', enum: VotingType, enumName: 'VotingType' })
  voteType: VotingType;

  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;
}
