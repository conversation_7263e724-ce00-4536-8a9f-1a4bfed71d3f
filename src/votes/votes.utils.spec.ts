import { castVoteMessageToVoteType, generateVoteMessage, VoteMessage } from './votes.utils';
import { VoteType } from './dto/prepare-vote.dto';

describe('Votes utils', () => {
  const walletId = '0.0.123';
  const voteRemoveMessage = `${walletId} removed the vote for the application.` as VoteMessage;
  const voteInFavorMessage = `${walletId} voted in favor for the application.` as VoteMessage;
  const voteAgainstMessage = `${walletId} voted against for the application.` as VoteMessage;
  const voteChangedToInFavorMessage =
    `${walletId} changes the vote from being against to in favor for the application.` as VoteMessage;
  const voteChangedToAgainstMessage =
    `${walletId} changes the vote from being in favor to against for the application.` as VoteMessage;

  describe('generateVoteMessage', () => {
    it('generates correct vote remove message', () => {
      expect(generateVoteMessage(true, false, VoteType.REMOVE, walletId)).toEqual(voteRemoveMessage);
    });

    it('generates correct vote in favor message', () => {
      expect(generateVoteMessage(false, false, VoteType.IN_FAVOR, walletId)).toEqual(voteInFavorMessage);
    });

    it('generates correct vote against message', () => {
      expect(generateVoteMessage(false, false, VoteType.AGAINST, walletId)).toEqual(voteAgainstMessage);
    });

    it('generates correct vote change from against to in favor message', () => {
      expect(generateVoteMessage(false, true, VoteType.IN_FAVOR, walletId)).toEqual(voteChangedToInFavorMessage);
    });

    it('generates correct vote change from in favor to against message', () => {
      expect(generateVoteMessage(true, false, VoteType.AGAINST, walletId)).toEqual(voteChangedToAgainstMessage);
    });

    it('returns null for unknown vote situation', () => {
      expect(generateVoteMessage(false, false, 'unknown' as VoteType, walletId)).toEqual(null);
    });
  });

  describe('castVoteMessageToVoteType', () => {
    it('casts remove vote type for in remove vote message', () => {
      expect(castVoteMessageToVoteType(voteRemoveMessage)).toEqual(VoteType.REMOVE);
    });

    it('casts in favor vote type for in favor vote message', () => {
      expect(castVoteMessageToVoteType(voteInFavorMessage)).toEqual(VoteType.IN_FAVOR);
    });

    it('casts against vote type for against vote message', () => {
      expect(castVoteMessageToVoteType(voteAgainstMessage)).toEqual(VoteType.AGAINST);
    });

    it('casts in favor vote type for changed to in favor vote message', () => {
      expect(castVoteMessageToVoteType(voteChangedToInFavorMessage)).toEqual(VoteType.IN_FAVOR);
    });

    it('casts against vote type for changed to against vote message', () => {
      expect(castVoteMessageToVoteType(voteChangedToAgainstMessage)).toEqual(VoteType.AGAINST);
    });

    it('returns null for unknown vote message', () => {
      expect(castVoteMessageToVoteType('unknown' as VoteMessage)).toEqual(null);
    });
  });
});
