import { VoteType } from './dto/prepare-vote.dto';

export type VoteMessage = string & { __brand: 'VoteMessage' };

export const generateVoteMessage = (
  existingInFavorVote: boolean,
  existingAgainstVote: boolean,
  voteType: VoteType,
  accountId: string,
): null | VoteMessage => {
  if (voteType === VoteType.REMOVE) {
    return `${accountId} removed the vote for the application.` as VoteMessage;
  } else if (voteType === VoteType.IN_FAVOR && existingAgainstVote) {
    return `${accountId} changes the vote from being against to in favor for the application.` as VoteMessage;
  } else if (voteType === VoteType.AGAINST && existingInFavorVote) {
    return `${accountId} changes the vote from being in favor to against for the application.` as VoteMessage;
  } else if (voteType === VoteType.IN_FAVOR) {
    return `${accountId} voted in favor for the application.` as VoteMessage;
  } else if (voteType === VoteType.AGAINST) {
    return `${accountId} voted against for the application.` as VoteMessage;
  }

  return null;
};

export const castVoteMessageToVoteType = (message: VoteMessage): VoteType | null => {
  const removeMessageRegex = /removed the vote for the application.$/;
  const changedToInFavorRegex = /changes the vote from being against to in favor for the application.$/;
  const changedToAgainstRegex = /changes the vote from being in favor to against for the application.$/;
  const inFavorRegex = /voted in favor for the application.$/;
  const againstRegex = /voted against for the application.$/;

  if (removeMessageRegex.test(message)) {
    return VoteType.REMOVE;
  }

  if (changedToInFavorRegex.test(message) || inFavorRegex.test(message)) {
    return VoteType.IN_FAVOR;
  }

  if (changedToAgainstRegex.test(message) || againstRegex.test(message)) {
    return VoteType.AGAINST;
  }

  return null;
};
