import {
  BadRequestException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  UnprocessableEntityException,
} from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { PrepareVoteRequestDTO, VoteType } from './dto/prepare-vote.dto';
import { TopicMessageSubmitTransaction, Transaction } from '@hashgraph/sdk';

import { HederaService } from '../hedera/hedera.service';
import { User } from '../auth/entities/user.entity';
import { Vote, VotingType } from './entities/vote.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { StageCode } from '../workflow/enums/stage-code.enum';
import { castVoteMessageToVoteType, generateVoteMessage, VoteMessage } from './votes.utils';
import { GrantApplicationService } from '../grant-application/grant-application.service';
import { LoggedInUser } from 'src/auth/decorators/logged-in-user.decorator';

@Injectable()
export class VotesService {
  private readonly logger = new Logger(VotesService.name);

  constructor(
    @Inject(forwardRef(() => GrantApplicationService))
    private readonly grantApplicationService: GrantApplicationService,
    @InjectRepository(Vote)
    private readonly voteRepository: Repository<Vote>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly hederaService: HederaService,
    private readonly dataSource: DataSource,
  ) {}

  async prepareVote(
    loggedInUser: LoggedInUser,
    applicationId: number,
    { voteType, walletId: accountId }: PrepareVoteRequestDTO,
  ) {
    const application = await this.grantApplicationService.getGrantApplicationById(applicationId);
    const user = await this.userRepository.findOne({
      where: { id: loggedInUser.id },
    });

    this.validateGrantApplicationVotingStatus(application);
    this.validateUserVoteEligibility(user, accountId);
    const voteMessage = await this.validateVoteType(application, user, accountId, voteType);

    this.logger.log(`User ${loggedInUser.id} is eligible for voting (phone KYC verified).`);

    const voteTopicId =
      application.grantCall.workflowState.currentStepDefinition.code === StageCode.GC_COMMUNITY_VOTING
        ? application.votingTopicId
        : application.finalVotingTopicId;

    const { transaction, estimatedGasFee, payerAccountBalance } =
      await this.hederaService.prepareSubmitMessageTransaction(
        voteMessage,
        voteTopicId,
        accountId,
        applicationId.toString(),
      );

    return {
      transaction,
      estimatedGasFee,
      payerAccountBalance,
    };
  }

  private async createOrGetVoteForApplication(application: GrantApplication, votingType: VotingType) {
    const votes = await this.voteRepository.findOne({
      where: {
        voteType: votingType,
        grantApplication: {
          id: application.id,
        },
      },
    });

    if (votes) {
      return votes;
    }

    const vote = this.voteRepository.create({
      grantApplication: application,
      walletsInFavor: [],
      walletsAgainst: [],
      voteType: votingType,
    });

    return this.voteRepository.save(vote);
  }

  async castPreparedVote(applicationId: number, transactionBase64: string, userId: number) {
    const transaction = Transaction.fromBytes(Buffer.from(transactionBase64, 'base64'));

    if (!(transaction instanceof TopicMessageSubmitTransaction)) {
      throw new BadRequestException('Invalid transaction type provided.');
    }

    const voteMessage = Buffer.from(transaction.getMessage()).toString() as VoteMessage;
    const voteType = castVoteMessageToVoteType(voteMessage);

    if (!voteType) {
      throw new InternalServerErrorException('Invalid vote message');
    }

    const transactionApplicationId = parseInt(transaction.transactionMemo, 10);

    if (transactionApplicationId !== applicationId) {
      throw new BadRequestException('Transaction was signed for different application.');
    }

    const application = await this.grantApplicationService.getGrantApplicationById(applicationId);
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    const accountId = transaction.transactionId.accountId.toString();

    this.validateGrantApplicationVotingStatus(application);
    this.validateUserVoteEligibility(user, accountId);
    await this.validateVoteType(application, user, accountId, voteType);

    const { record } = await this.hederaService.executeSignedTransaction(transaction);
    const walletId = record.transactionId.accountId.toString();
    const votingType =
      application.grantCall.workflowState.currentStepDefinition.code === StageCode.GC_COMMUNITY_VOTING
        ? VotingType.COMMUNITY_VOTING
        : VotingType.FINAL_COMMUNITY_VOTING;

    return this.adjustApplicationVoteCount(votingType, applicationId, walletId, voteType);
  }

  private async adjustApplicationVoteCount(
    votingType: VotingType,
    applicationId: number,
    walletId: string,
    voteType: VoteType,
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const grantApplicationVotes = await queryRunner.manager.findOne(Vote, {
      where: {
        voteType: votingType,
        grantApplication: {
          id: applicationId,
        },
      },
      lock: {
        mode: 'pessimistic_write',
      },
    });

    const existingInFavorVoteIndex = grantApplicationVotes.walletsInFavor.indexOf(walletId);
    const existingAgainstVoteIndex = grantApplicationVotes.walletsAgainst.indexOf(walletId);

    if (existingInFavorVoteIndex > -1) {
      grantApplicationVotes.walletsInFavor.splice(existingInFavorVoteIndex, 1);
      grantApplicationVotes.inFavorVotes--;
    } else if (existingAgainstVoteIndex > -1) {
      grantApplicationVotes.walletsAgainst.splice(existingAgainstVoteIndex, 1);
      grantApplicationVotes.againstVotes--;
    }

    if (voteType === VoteType.IN_FAVOR) {
      grantApplicationVotes.walletsInFavor.push(walletId);
      grantApplicationVotes.inFavorVotes++;
    } else if (voteType === VoteType.AGAINST) {
      grantApplicationVotes.walletsAgainst.push(walletId);
      grantApplicationVotes.againstVotes++;
    }

    await queryRunner.manager.save(grantApplicationVotes);
    await queryRunner.commitTransaction();
    await queryRunner.release();

    return grantApplicationVotes;
  }

  async getVotesForGrantApplication(applicationId: number): Promise<Pick<Vote, 'inFavorVotes' | 'againstVotes'>> {
    try {
      const voteRecord = await this.voteRepository
        .createQueryBuilder('vote')
        .select(['vote.inFavorVotes', 'vote.againstVotes'])
        .innerJoin('vote.grantApplication', 'ga')
        .where('ga.id = :applicationId', { applicationId: applicationId })
        .getOne();

      if (!voteRecord) {
        this.logger.warn(`No vote record found for application ID ${applicationId}. Returning 0 votes.`);

        return { inFavorVotes: 0, againstVotes: 0 };
      }

      return {
        inFavorVotes: voteRecord.inFavorVotes,
        againstVotes: voteRecord.againstVotes,
      };
    } catch (error) {
      this.logger.error(
        `QueryBuilder failed fetching votes for application ID ${applicationId}: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(`Failed to retrieve vote data for application ${applicationId}.`);
    }
  }

  async getGrantCallParticipation(
    grantCallId: number,
    voteType: VotingType,
  ): Promise<{ grantCallId: number; participationCount: number }> {
    const votesInCall = await this.voteRepository.find({
      where: {
        grantApplication: {
          grantCall: {
            id: grantCallId,
          },
        },
        voteType: voteType,
      },
      relations: { grantApplication: { grantCall: true } },
      select: ['walletsInFavor', 'walletsAgainst'],
    });

    if (!votesInCall || votesInCall.length === 0) {
      this.logger.log(`No votes found for any applications within grant call ID: ${grantCallId}`);

      return { grantCallId, participationCount: 0 };
    }

    const uniqueWallets = new Set<string>();

    votesInCall.forEach((vote) => {
      vote.walletsInFavor.forEach((wallet) => uniqueWallets.add(wallet));
      vote.walletsAgainst.forEach((wallet) => uniqueWallets.add(wallet));
    });

    return { grantCallId, participationCount: uniqueWallets.size };
  }

  async getUserVotesCount(grantCallId: number, user: User, excludedApplicationId?: number): Promise<number> {
    let applications = await this.grantApplicationService.findForVotingByGrantCallId(grantCallId);

    if (excludedApplicationId) {
      applications = applications.filter((app) => app.id !== excludedApplicationId);
    }

    const votes = applications.map((app) => app.votes).flat();

    let count = 0;
    for (const address of user.addresses) {
      const userVotes = votes.filter(
        (vote) =>
          vote.voteType === VotingType.FINAL_COMMUNITY_VOTING &&
          (vote.walletsInFavor.includes(address) || vote.walletsAgainst.includes(address)),
      );
      count += userVotes.length;
    }

    return count;
  }

  canUserVoteForApplication(application: GrantApplication, user?: User) {
    try {
      this.validateGrantApplicationVotingStatus(application);
      this.validateUserVoteEligibility(user);

      return true;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      return false;
    }
  }

  private validateGrantApplicationVotingStatus(application: GrantApplication) {
    const allowedVoteStages = [StageCode.GC_COMMUNITY_VOTING, StageCode.GC_FINAL_COMMUNITY_VOTING];

    if (
      !allowedVoteStages.includes(application.grantCall.workflowState.currentStepDefinition.code) ||
      [WorkflowStatus.REJECTED, WorkflowStatus.WITHDRAWN].includes(application.workflowState.status)
    ) {
      throw new BadRequestException('Voting is not possible for this application.');
    }
  }

  private validateUserVoteEligibility(user?: User, walletId?: string) {
    if (!user) {
      throw new InternalServerErrorException(`User not found while validating vote eligibility.`);
    }

    if (!user.phoneNumber) {
      throw new ForbiddenException('You are not eligible to vote because you have not provided a phone number.');
    }

    if (!user.isPhoneVerified) {
      throw new ForbiddenException('You are not eligible to vote because your phone number is not verified.');
    }

    if (walletId && !user.addresses.some((address) => address === walletId)) {
      throw new ForbiddenException('You are not eligible to vote because you do not have access to this wallet.');
    }
  }

  private async validateVoteType(application: GrantApplication, user: User, accountId: string, voteType?: VoteType) {
    const votingType =
      application.grantCall.workflowState.currentStepDefinition.code === StageCode.GC_COMMUNITY_VOTING
        ? VotingType.COMMUNITY_VOTING
        : VotingType.FINAL_COMMUNITY_VOTING;
    const grantApplicationVotes = await this.createOrGetVoteForApplication(application, votingType);

    const existingInFavorVote = grantApplicationVotes.walletsInFavor.includes(accountId);
    const existingAgainstVote = grantApplicationVotes.walletsAgainst.includes(accountId);
    const hasVoteForApplication = existingInFavorVote || existingAgainstVote;

    if (!hasVoteForApplication && voteType === VoteType.REMOVE) {
      throw new BadRequestException('Cannot prepare to clear a vote that does not exist.');
    }

    const isDuplicateVote =
      (existingInFavorVote && voteType === VoteType.IN_FAVOR) || (existingAgainstVote && voteType === VoteType.AGAINST);

    if (isDuplicateVote) {
      throw new BadRequestException(`You have already voted '${voteType}' for this application.`);
    }

    const userVotes = await this.getUserVotesCount(application.grantCall.id, user, application.id);

    if (votingType === VotingType.COMMUNITY_VOTING && userVotes >= 1 && !hasVoteForApplication) {
      throw new UnprocessableEntityException('You have already used your vote for this grant call.');
    } else if (votingType === VotingType.FINAL_COMMUNITY_VOTING && userVotes >= 2 && !hasVoteForApplication) {
      throw new UnprocessableEntityException('You have already used your two votes for this grant call.');
    }

    const voteMessage = generateVoteMessage(existingInFavorVote, existingAgainstVote, voteType, accountId);

    if (!voteMessage) {
      throw new InternalServerErrorException('Invalid vote type');
    }

    return voteMessage;
  }
}
