import { BadRequestException, ForbiddenException, UnprocessableEntityException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { AccountId, TopicMessageSubmitTransaction, Transaction, TransactionId } from '@hashgraph/sdk';

import { DataSource } from 'typeorm';
import { HederaService } from '../hedera/hedera.service';
import { StageCode } from '../workflow/enums/stage-code.enum';
import { User } from '../auth/entities/user.entity';
import { Vote } from './entities/vote.entity';
import { VoteType } from './dto/prepare-vote.dto';
import { VotesService } from './votes.service';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { generateVoteMessage } from './votes.utils';
import { GrantApplicationService } from '../grant-application/grant-application.service';
import { Role } from '../auth/role.enum';
import { LoggedInUser } from '../auth/decorators/logged-in-user.decorator';

describe('VotesService', () => {
  let service: VotesService;

  const mockVoteRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  const mockGrantApplicationService = {
    getGrantApplicationById: jest.fn(),
    findForVotingByGrantCallId: jest.fn(),
  };

  const mockHederaService = {
    prepareSubmitMessageTransaction: jest.fn(),
    executeSignedTransaction: jest.fn(),
  };

  const mockDataSource = {
    createQueryRunner: jest.fn().mockReturnValue({
      connect: jest.fn(),
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      release: jest.fn(),
      manager: {
        findOne: jest.fn(),
        save: jest.fn(),
      },
    }),
  };

  const mockUser: LoggedInUser = {
    id: 1,
    email: '<EMAIL>',
    emailVerified: true,
    phoneNumber: null,
    isPhoneVerified: null,
    otp: null,
    otpExpiresAt: null,
    role: Role.COMMUNITY_MEMBER,
  };
  const mockApplicationId = 1;
  const mockWalletId = '0.0.12345';
  const mockTransactionBase64 = 'mock-transaction-base64';

  const mockApplication = {
    id: mockApplicationId,
    votingTopicId: '0.0.123456',
    finalVotingTopicId: '0.0.789012',
    workflowState: {
      status: WorkflowStatus.IN_PROGRESS,
    },
    currentStepDefinition: {
      id: 20,
      code: StageCode.GA_QUALIFICATION,
    } as WorkflowStepDefinition,
    grantCall: {
      workflowState: {
        currentStepDefinition: {
          id: 20,
          code: StageCode.GC_COMMUNITY_VOTING,
        } as WorkflowStepDefinition,
      },
    },
  };

  const mockUserEntity = {
    id: mockUser.id,
    phoneNumber: '+**********',
    isPhoneVerified: true,
    addresses: [mockWalletId],
  };

  const mockVote = {
    grantApplication: { id: mockApplicationId },
    walletsInFavor: [],
    walletsAgainst: [],
    inFavorVotes: 0,
    againstVotes: 0,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VotesService,
        {
          provide: getRepositoryToken(Vote),
          useValue: mockVoteRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        { provide: GrantApplicationService, useValue: mockGrantApplicationService },
        {
          provide: HederaService,
          useValue: mockHederaService,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    service = module.get<VotesService>(VotesService);

    mockGrantApplicationService.getGrantApplicationById.mockResolvedValue(mockApplication);
    mockGrantApplicationService.findForVotingByGrantCallId.mockResolvedValue([mockApplication]);
    mockUserRepository.findOne.mockResolvedValue(mockUserEntity);
    mockVoteRepository.findOne.mockResolvedValue(mockVote);
    mockHederaService.prepareSubmitMessageTransaction.mockResolvedValue({
      transaction: 'mock-transaction',
      estimatedGasFee: 0,
      payerAccountBalance: 0,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('prepareVote', () => {
    it('should prepare a new in-favor vote successfully', async () => {
      const result = await service.prepareVote(mockUser, mockApplicationId, {
        voteType: VoteType.IN_FAVOR,
        walletId: mockWalletId,
      });

      expect(result).toEqual({ transaction: 'mock-transaction', estimatedGasFee: 0, payerAccountBalance: 0 });
      expect(mockHederaService.prepareSubmitMessageTransaction).toHaveBeenCalledWith(
        generateVoteMessage(false, false, VoteType.IN_FAVOR, mockWalletId),
        mockApplication.votingTopicId,
        mockWalletId,
        mockApplicationId.toString(),
      );
    });

    it('should prepare a new against vote successfully', async () => {
      const result = await service.prepareVote(mockUser, mockApplicationId, {
        voteType: VoteType.AGAINST,
        walletId: mockWalletId,
      });

      expect(result).toEqual({ transaction: 'mock-transaction', estimatedGasFee: 0, payerAccountBalance: 0 });
      expect(mockHederaService.prepareSubmitMessageTransaction).toHaveBeenCalledWith(
        generateVoteMessage(false, false, VoteType.AGAINST, mockWalletId),
        mockApplication.votingTopicId,
        mockWalletId,
        mockApplicationId.toString(),
      );
    });

    it('should prepare a vote change from against to in-favor', async () => {
      mockVoteRepository.findOne.mockResolvedValue({
        ...mockVote,
        walletsAgainst: [mockWalletId],
      });

      const result = await service.prepareVote(mockUser, mockApplicationId, {
        voteType: VoteType.IN_FAVOR,
        walletId: mockWalletId,
      });

      expect(result).toEqual({ transaction: 'mock-transaction', estimatedGasFee: 0, payerAccountBalance: 0 });
      expect(mockHederaService.prepareSubmitMessageTransaction).toHaveBeenCalledWith(
        generateVoteMessage(false, true, VoteType.IN_FAVOR, mockWalletId),
        mockApplication.votingTopicId,
        mockWalletId,
        mockApplicationId.toString(),
      );
    });

    it('should prepare a vote change from in-favor to against', async () => {
      mockVoteRepository.findOne.mockResolvedValue({
        ...mockVote,
        walletsInFavor: [mockWalletId],
      });

      const result = await service.prepareVote(mockUser, mockApplicationId, {
        voteType: VoteType.AGAINST,
        walletId: mockWalletId,
      });

      expect(result).toEqual({ transaction: 'mock-transaction', estimatedGasFee: 0, payerAccountBalance: 0 });
      expect(mockHederaService.prepareSubmitMessageTransaction).toHaveBeenCalledWith(
        generateVoteMessage(true, false, VoteType.AGAINST, mockWalletId),
        mockApplication.votingTopicId,
        mockWalletId,
        mockApplicationId.toString(),
      );
    });

    it('should throw UnprocessableEntityException when already voted for another application', async () => {
      jest.spyOn(service, 'getUserVotesCount').mockResolvedValue(1);

      expect(
        service.prepareVote(mockUser, mockApplicationId, {
          voteType: VoteType.IN_FAVOR,
          walletId: mockWalletId,
        }),
      ).rejects.toThrowError(new UnprocessableEntityException('You have already used your vote for this grant call.'));
    });

    it('should throw BadRequestException when trying to clear a non-existent vote', async () => {
      await expect(
        service.prepareVote(mockUser, mockApplicationId, {
          voteType: VoteType.REMOVE,
          walletId: mockWalletId,
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException when trying to vote the same way again', async () => {
      mockVoteRepository.findOne.mockResolvedValue({
        ...mockVote,
        walletsInFavor: [mockWalletId],
      });

      await expect(
        service.prepareVote(mockUser, mockApplicationId, {
          voteType: VoteType.IN_FAVOR,
          walletId: mockWalletId,
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException when voting is not open', async () => {
      mockGrantApplicationService.getGrantApplicationById.mockResolvedValue({
        ...mockApplication,
        workflowState: {
          status: WorkflowStatus.WITHDRAWN,
        },
      });

      await expect(
        service.prepareVote(mockUser, mockApplicationId, {
          voteType: VoteType.IN_FAVOR,
          walletId: mockWalletId,
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw ForbiddenException when user has no phone number', async () => {
      mockUserRepository.findOne.mockResolvedValue({
        ...mockUserEntity,
        phoneNumber: null,
      });

      await expect(
        service.prepareVote(mockUser, mockApplicationId, {
          voteType: VoteType.IN_FAVOR,
          walletId: mockWalletId,
        }),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException when user phone is not verified', async () => {
      mockUserRepository.findOne.mockResolvedValue({
        ...mockUserEntity,
        isPhoneVerified: false,
      });

      await expect(
        service.prepareVote(mockUser, mockApplicationId, {
          voteType: VoteType.IN_FAVOR,
          walletId: mockWalletId,
        }),
      ).rejects.toThrow(ForbiddenException);
    });

    describe('Final Community Voting', () => {
      beforeEach(() => {
        const mockFinalCommunityVotingApplication = {
          ...mockApplication,
          grantCall: {
            workflowState: {
              currentStepDefinition: {
                ...mockApplication.grantCall.workflowState.currentStepDefinition,
                code: StageCode.GC_FINAL_COMMUNITY_VOTING,
              },
            },
          },
        };

        mockGrantApplicationService.getGrantApplicationById.mockResolvedValue(mockFinalCommunityVotingApplication);
      });

      it('should prepare second vote for another application', async () => {
        jest.spyOn(service, 'getUserVotesCount').mockResolvedValue(1);

        const result = await service.prepareVote(mockUser, mockApplicationId, {
          voteType: VoteType.IN_FAVOR,
          walletId: mockWalletId,
        });

        expect(result).toEqual({ transaction: 'mock-transaction', estimatedGasFee: 0, payerAccountBalance: 0 });
        expect(mockHederaService.prepareSubmitMessageTransaction).toHaveBeenCalledWith(
          generateVoteMessage(false, false, VoteType.IN_FAVOR, mockWalletId),
          mockApplication.finalVotingTopicId,
          mockWalletId,
          mockApplicationId.toString(),
        );
      });

      it('should prepare a vote change from against to in-favor when already voted for another application', async () => {
        mockVoteRepository.findOne.mockResolvedValue({
          ...mockVote,
          walletsAgainst: [mockWalletId],
        });
        jest.spyOn(service, 'getUserVotesCount').mockResolvedValue(2);

        const result = await service.prepareVote(mockUser, mockApplicationId, {
          voteType: VoteType.IN_FAVOR,
          walletId: mockWalletId,
        });

        expect(result).toEqual({ transaction: 'mock-transaction', estimatedGasFee: 0, payerAccountBalance: 0 });
        expect(mockHederaService.prepareSubmitMessageTransaction).toHaveBeenCalledWith(
          generateVoteMessage(false, true, VoteType.IN_FAVOR, mockWalletId),
          mockApplication.finalVotingTopicId,
          mockWalletId,
          mockApplicationId.toString(),
        );
      });

      it('should throw UnprocessableEntityException when already voted twice for another application', async () => {
        jest.spyOn(service, 'getUserVotesCount').mockResolvedValue(2);

        expect(
          service.prepareVote(mockUser, mockApplicationId, {
            voteType: VoteType.IN_FAVOR,
            walletId: mockWalletId,
          }),
        ).rejects.toThrowError(
          new UnprocessableEntityException('You have already used your two votes for this grant call.'),
        );
      });
    });
  });

  describe('castPreparedVote', () => {
    const mockApplicationId = 1;
    const mockWalletId = '0.0.12345';

    // Create a proper mock that passes instanceof checks
    class MockTopicMessageSubmitTransaction extends TopicMessageSubmitTransaction {
      getMessage = jest.fn().mockReturnValue(Buffer.from(JSON.stringify({ voteType: VoteType.IN_FAVOR })));
      get transactionMemo() {
        return '1';
      }

      get transactionId(): TransactionId | null {
        return new TransactionId(AccountId.fromString(mockWalletId), null, null, null);
      }
    }

    const mockTransaction = new MockTopicMessageSubmitTransaction();

    const mockRecord = {
      transactionId: {
        accountId: {
          toString: jest.fn().mockReturnValue(mockWalletId),
        },
      },
    };

    const mockQueryRunner = {
      connect: jest.fn(),
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      release: jest.fn(),
      manager: {
        findOne: jest.fn(),
        save: jest.fn(),
      },
    };

    beforeEach(() => {
      jest.spyOn(Transaction, 'fromBytes').mockReturnValue(mockTransaction);
      mockHederaService.executeSignedTransaction.mockResolvedValue({ record: mockRecord });
      mockDataSource.createQueryRunner.mockReturnValue(mockQueryRunner);
    });

    it('should add a new in-favor vote and update counts correctly', async () => {
      const initialVote = {
        walletsInFavor: [],
        walletsAgainst: [],
        inFavorVotes: 0,
        againstVotes: 0,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(
        Buffer.from(generateVoteMessage(false, false, VoteType.IN_FAVOR, mockWalletId)),
      );

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64, 1);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        ...initialVote,
        walletsInFavor: [mockWalletId],
        inFavorVotes: 1,
      });
    });

    it('should add a new against vote and update counts correctly', async () => {
      const initialVote = {
        walletsInFavor: [],
        walletsAgainst: [],
        inFavorVotes: 0,
        againstVotes: 0,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(
        Buffer.from(generateVoteMessage(false, false, VoteType.AGAINST, mockWalletId)),
      );

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64, 1);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        ...initialVote,
        walletsAgainst: [mockWalletId],
        againstVotes: 1,
      });
    });

    it('should change vote from in-favor to against and update counts correctly', async () => {
      const initialVote = {
        walletsInFavor: [mockWalletId],
        walletsAgainst: [],
        inFavorVotes: 1,
        againstVotes: 0,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(
        Buffer.from(generateVoteMessage(true, false, VoteType.AGAINST, mockWalletId)),
      );

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64, 1);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        walletsInFavor: [],
        walletsAgainst: [mockWalletId],
        inFavorVotes: 0,
        againstVotes: 1,
      });
    });

    it('should change vote from against to in-favor and update counts correctly', async () => {
      const initialVote = {
        walletsInFavor: [],
        walletsAgainst: [mockWalletId],
        inFavorVotes: 0,
        againstVotes: 1,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(
        Buffer.from(generateVoteMessage(false, true, VoteType.IN_FAVOR, mockWalletId)),
      );

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64, 1);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        walletsInFavor: [mockWalletId],
        walletsAgainst: [],
        inFavorVotes: 1,
        againstVotes: 0,
      });
    });

    it('should clear an existing in-favor vote and update counts correctly', async () => {
      const initialVote = {
        walletsInFavor: [mockWalletId],
        walletsAgainst: [],
        inFavorVotes: 1,
        againstVotes: 0,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockVoteRepository.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(
        Buffer.from(generateVoteMessage(true, false, VoteType.REMOVE, mockWalletId)),
      );

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64, 1);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        walletsInFavor: [],
        walletsAgainst: [],
        inFavorVotes: 0,
        againstVotes: 0,
      });
    });

    it('should clear an existing against vote and update counts correctly', async () => {
      const initialVote = {
        walletsInFavor: [],
        walletsAgainst: [mockWalletId],
        inFavorVotes: 0,
        againstVotes: 1,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockVoteRepository.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(
        Buffer.from(generateVoteMessage(false, true, VoteType.REMOVE, mockWalletId)),
      );

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64, 1);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        walletsInFavor: [],
        walletsAgainst: [],
        inFavorVotes: 0,
        againstVotes: 0,
      });
    });

    it('should handle multiple wallets voting correctly', async () => {
      const otherWalletId = '0.0.67890';
      const initialVote = {
        walletsInFavor: [otherWalletId],
        walletsAgainst: [],
        inFavorVotes: 1,
        againstVotes: 0,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(
        Buffer.from(generateVoteMessage(false, false, VoteType.IN_FAVOR, mockWalletId)),
      );

      await service.castPreparedVote(mockApplicationId, mockTransactionBase64, 1);

      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
        walletsInFavor: [otherWalletId, mockWalletId],
        walletsAgainst: [],
        inFavorVotes: 2,
        againstVotes: 0,
      });
    });

    it('should throw BadRequestException for invalid transaction signature', async () => {
      const initialVote = {
        walletsInFavor: [],
        walletsAgainst: [],
        inFavorVotes: 0,
        againstVotes: 0,
      };

      mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
      mockVoteRepository.findOne.mockResolvedValue(initialVote);
      mockTransaction.getMessage.mockReturnValue(
        Buffer.from(generateVoteMessage(false, false, VoteType.IN_FAVOR, mockWalletId)),
      );

      mockHederaService.executeSignedTransaction.mockRejectedValue(
        new BadRequestException('Invalid transaction signature'),
      );

      await expect(service.castPreparedVote(mockApplicationId, mockTransactionBase64, 1)).rejects.toThrow(
        BadRequestException,
      );
    });

    describe('Final Community Voting', () => {
      beforeEach(() => {
        const mockFinalCommunityVotingApplication = {
          ...mockApplication,
          grantCall: {
            workflowState: {
              currentStepDefinition: {
                ...mockApplication.grantCall.workflowState.currentStepDefinition,
                code: StageCode.GC_FINAL_COMMUNITY_VOTING,
              },
            },
          },
        };

        mockGrantApplicationService.getGrantApplicationById.mockResolvedValue(mockFinalCommunityVotingApplication);
      });

      it('should cast second vote for another application', async () => {
        jest.spyOn(service, 'getUserVotesCount').mockResolvedValue(1);

        const initialVote = {
          walletsInFavor: [],
          walletsAgainst: [],
          inFavorVotes: 0,
          againstVotes: 0,
        };

        mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
        mockTransaction.getMessage.mockReturnValue(
          Buffer.from(generateVoteMessage(false, false, VoteType.IN_FAVOR, mockWalletId)),
        );

        await service.castPreparedVote(mockApplicationId, mockTransactionBase64, 1);

        expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
          ...initialVote,
          walletsInFavor: [mockWalletId],
          inFavorVotes: 1,
        });
      });

      it('should change vote from against to in-favor if already voted on another application', async () => {
        const initialVote = {
          walletsInFavor: [],
          walletsAgainst: [mockWalletId],
          inFavorVotes: 0,
          againstVotes: 1,
        };

        mockQueryRunner.manager.findOne.mockResolvedValue(initialVote);
        mockVoteRepository.findOne.mockResolvedValue(initialVote);
        jest.spyOn(service, 'getUserVotesCount').mockResolvedValue(2);
        mockTransaction.getMessage.mockReturnValue(
          Buffer.from(generateVoteMessage(false, true, VoteType.IN_FAVOR, mockWalletId)),
        );

        await service.castPreparedVote(mockApplicationId, mockTransactionBase64, 1);

        expect(mockQueryRunner.manager.save).toHaveBeenCalledWith({
          walletsInFavor: [mockWalletId],
          walletsAgainst: [],
          inFavorVotes: 1,
          againstVotes: 0,
        });
      });

      it('should throw UnprocessableEntityException when already voted twice for another application', async () => {
        jest.spyOn(service, 'getUserVotesCount').mockResolvedValue(2);

        expect(
          service.prepareVote(mockUser, mockApplicationId, {
            voteType: VoteType.IN_FAVOR,
            walletId: mockWalletId,
          }),
        ).rejects.toThrowError(
          new UnprocessableEntityException('You have already used your two votes for this grant call.'),
        );
      });
    });
  });
});
