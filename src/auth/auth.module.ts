import { Auth<PERSON>ontroller } from './auth.controller';
import { AuthService } from './auth.service';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MailModule } from '../notifications/mail/mail.module';
import { Module } from '@nestjs/common';
import { SocialLoginModule } from './social-login/social-login.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { JwtStrategy } from './guards/jwt.strategy';

@Module({
  imports: [
    // Todo: Implement CHALLENGE_TOKEN and RECOVERY_TOKEN logic in guard
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
      }),
    }),
    TypeOrmModule.forFeature([User]),
    MailModule,
    SocialLoginModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy],
  exports: [AuthService],
})
export class AuthModule {}
