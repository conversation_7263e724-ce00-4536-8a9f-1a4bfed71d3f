jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

import { HttpException, HttpStatus } from '@nestjs/common';
import { SocialAuthUserDto, SocialLoginRequestDto } from '../dto/social-auth.dto';
import { SocialLoginService, SocialUserPayload } from './social-login.service';
import { Test, TestingModule } from '@nestjs/testing';
import { User } from '../entities/user.entity';
import { Role } from '../role.enum';

import { AuthService } from '../auth.service';
import { EntityManager } from 'typeorm';
import { GithubService } from './github/github.service';
import { GoogleService } from './google/google.service';
import { LinkedinService } from './linkedin/linkedin.service';
import { TwitterService } from './twitter/twitter.service';
import { getRepositoryToken } from '@nestjs/typeorm';

const mockAuthRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  createQueryBuilder: jest.fn().mockReturnValue({
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getOne: jest.fn().mockResolvedValue(null),
    withDeleted: jest.fn().mockReturnThis(),
  }),
  manager: {
    transaction: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  },
});

const mockGoogleService = () => ({ googleLogin: jest.fn() });
const mockGithubService = () => ({ githubLogin: jest.fn() });
const mockTwitterService = () => ({ twitterLogin: jest.fn() });
const mockLinkedinService = () => ({ linkedinLogin: jest.fn() });
const mockAuthService = () => ({
  validateWallet: jest.fn(),
  generateSessionToken: jest.fn(),
  transformUser: jest.fn(),
  fetchPublicKeyForAddress: jest.fn(),
  checkSig: jest.fn(),
});

describe('SocialLoginService', () => {
  let service: SocialLoginService;
  let authRepository: ReturnType<typeof mockAuthRepository>;
  let googleService: ReturnType<typeof mockGoogleService>;
  let githubService: ReturnType<typeof mockGithubService>;
  let twitterService: ReturnType<typeof mockTwitterService>;
  let linkedinService: ReturnType<typeof mockLinkedinService>;
  let authService: ReturnType<typeof mockAuthService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SocialLoginService,
        { provide: getRepositoryToken(User), useFactory: mockAuthRepository },
        { provide: GoogleService, useFactory: mockGoogleService },
        { provide: GithubService, useFactory: mockGithubService },
        { provide: TwitterService, useFactory: mockTwitterService },
        { provide: LinkedinService, useFactory: mockLinkedinService },
        { provide: AuthService, useFactory: mockAuthService },
      ],
    }).compile();

    service = module.get<SocialLoginService>(SocialLoginService);
    authRepository = module.get(getRepositoryToken(User));
    googleService = module.get(GoogleService);
    githubService = module.get(GithubService);
    twitterService = module.get(TwitterService);
    linkedinService = module.get(LinkedinService);
    authService = module.get(AuthService);

    // Mock transaction manager's transaction method
    authRepository.manager.transaction.mockImplementation(async (transactionCode) => {
      return await transactionCode(authRepository as unknown as EntityManager);
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('socialLogin', () => {
    const socialLoginDto: SocialLoginRequestDto = {
      provider: 'google',
      token: 'test-token',
      expires: '2023-12-31T23:59:59.999Z',
      user: {
        name: 'Test User',
        address: 'test-address',
        signature: 'test-signature',
        challengeToken: 'test-challenge',
      } as SocialAuthUserDto,
    };
    const mockSocialUserPayload: SocialUserPayload = {
      sub: 'google-id',
      email: '<EMAIL>',
      username: 'Test User',
      linkedAccount: { googleId: 'google-id' },
    };

    const mockSessionToken = 'mock-session-token';
    const mockTransformedUser = { id: 'transformed-user-id' };

    it('should call googleLogin when provider is google', async () => {
      socialLoginDto.provider = 'google';
      googleService.googleLogin.mockResolvedValue(mockSocialUserPayload);
      authService.validateWallet.mockResolvedValue(true);

      jest.spyOn(service as any, 'resolveSocialUserAccount').mockResolvedValue({
        success: true,
        sessionToken: mockSessionToken,
        isNewUser: false,
        user: mockTransformedUser,
      });

      await service.socialLogin(socialLoginDto);

      expect(googleService.googleLogin).toHaveBeenCalledWith(socialLoginDto);
    });

    it('should call githubLogin when provider is github', async () => {
      socialLoginDto.provider = 'github';
      githubService.githubLogin.mockResolvedValue(mockSocialUserPayload);
      authService.validateWallet.mockResolvedValue(true);

      jest.spyOn(service as any, 'resolveSocialUserAccount').mockResolvedValue({
        success: true,
        sessionToken: mockSessionToken,
        isNewUser: false,
        user: mockTransformedUser,
      });
      await service.socialLogin(socialLoginDto);
      expect(githubService.githubLogin).toHaveBeenCalledWith(socialLoginDto);
    });

    it('should call linkedinLogin when provider is linkedin', async () => {
      socialLoginDto.provider = 'linkedin';
      linkedinService.linkedinLogin.mockResolvedValue(mockSocialUserPayload);
      authService.validateWallet.mockResolvedValue(true);

      jest.spyOn(service as any, 'resolveSocialUserAccount').mockResolvedValue({
        success: true,
        sessionToken: mockSessionToken,
        isNewUser: false,
        user: mockTransformedUser,
      });
      await service.socialLogin(socialLoginDto);
      expect(linkedinService.linkedinLogin).toHaveBeenCalledWith(socialLoginDto);
    });

    it('should call twitterLogin when provider is twitter', async () => {
      socialLoginDto.provider = 'twitter';
      twitterService.twitterLogin.mockResolvedValue(mockSocialUserPayload);
      authService.validateWallet.mockResolvedValue(true);

      jest.spyOn(service as any, 'resolveSocialUserAccount').mockResolvedValue({
        success: true,
        sessionToken: mockSessionToken,
        isNewUser: false,
        user: mockTransformedUser,
      });
      await service.socialLogin(socialLoginDto);
      expect(twitterService.twitterLogin).toHaveBeenCalledWith(socialLoginDto);
    });

    it('should throw BadRequestException for unsupported provider', async () => {
      socialLoginDto.provider = 'invalid-provider' as any;
      await expect(service.socialLogin(socialLoginDto)).rejects.toThrowError(HttpException);
      await expect(service.socialLogin(socialLoginDto)).rejects.toThrowError('Unsupported social login provider.');
      await expect(service.socialLogin(socialLoginDto)).rejects.toHaveProperty('status', HttpStatus.BAD_REQUEST);
    });
  });

  describe('resolveSocialUserAccount', () => {
    const socialWallet: SocialAuthUserDto = {
      name: 'Test Wallet User',
      image: '',
      address: 'test-wallet-address', // Use this address in mockExistingUser.addresses
      signature: 'test-wallet-signature',
      challengeToken: 'test-wallet-challenge',
    };
    const socialUser: SocialUserPayload = {
      sub: 'google-id',
      email: '<EMAIL>',
      username: 'Test Social User',
      linkedAccount: { googleId: 'google-id' },
    };
    const mockExistingUser: User = {
      id: 1,
      addresses: ['existing-address', socialWallet.address], // Include socialWallet.address here
      email: '<EMAIL>',
      displayName: 'Existing User',
      emailVerified: true,
      role: Role.COMMUNITY_MEMBER,
      linkedAccounts: { googleId: 'existing-google-id', ...socialUser.linkedAccount },
      createdAt: new Date(),
    } as unknown as User;
    const mockSessionToken = 'mock-session-token';
    const mockTransformedUser = { id: 'transformed-user-id' };

    beforeEach(() => {
      authService.validateWallet.mockResolvedValue(true);
      authService.generateSessionToken.mockReturnValue(mockSessionToken);
      authService.transformUser.mockReturnValue(mockTransformedUser);
    });

    it('should login existing user by social id', async () => {
      authRepository.findOne.mockResolvedValue(mockExistingUser);
      const result = await service.resolveSocialUserAccount(socialWallet, socialUser);

      expect(authRepository.findOne).toHaveBeenCalledWith(User, {
        where: { linkedAccounts: socialUser.linkedAccount, deletedAt: null },
      });

      expect(result).toEqual({
        success: true,
        sessionToken: mockSessionToken,
        isNewUser: false,
        user: mockTransformedUser,
      });
    });

    it('should login existing user by email and link social account', async () => {
      authRepository.findOne.mockResolvedValueOnce(null); // Mock for no user found by socialId

      authRepository
        .createQueryBuilder()
        .where()
        .getOne.mockImplementationOnce(() => {
          return Promise.resolve(mockExistingUser); // Resolve with mock user
        });

      const result = await service.resolveSocialUserAccount(socialWallet, socialUser);

      expect(authRepository.findOne).toHaveBeenCalledTimes(1); // Corrected to 1 call for socialId check
      expect(authRepository.findOne).toHaveBeenCalledWith(User, {
        where: { linkedAccounts: socialUser.linkedAccount, deletedAt: null },
      }); // Verify call with socialId

      expect(authRepository.createQueryBuilder().where().getOne).toHaveBeenCalledTimes(1); // Verify query builder used for email

      expect(authRepository.save).toHaveBeenCalledWith(
        User,
        expect.objectContaining({
          ...mockExistingUser,
          linkedAccounts: {
            ...mockExistingUser.linkedAccounts,
            linkedAccount: socialUser.linkedAccount,
          },
        }),
      );
      expect(result).toEqual({
        success: true,
        sessionToken: mockSessionToken,
        isNewUser: false,
        user: mockTransformedUser,
      });
    });

    it('should throw ConflictException if user already exists during creation (duplicate key error)', async () => {
      authRepository.findOne.mockResolvedValue(null);
      authRepository.createQueryBuilder().where().getOne.mockResolvedValue(null);
      authService.fetchPublicKeyForAddress.mockResolvedValue('mock-pub-key');
      authService.checkSig.mockResolvedValue(true);
      authRepository.create.mockReturnValue(mockExistingUser);
      authRepository.save.mockRejectedValue({ code: 23505 }); // Mock duplicate key error

      await expect(service.resolveSocialUserAccount(socialWallet, socialUser)).rejects.toThrowError(HttpException);
      await expect(service.resolveSocialUserAccount(socialWallet, socialUser)).rejects.toThrowError(
        'User already exists.',
      );
      await expect(service.resolveSocialUserAccount(socialWallet, socialUser)).rejects.toHaveProperty(
        'status',
        HttpStatus.CONFLICT,
      );
      expect(authRepository.create).toHaveBeenCalled();
      expect(authRepository.save).toHaveBeenCalled();
    });

    it('should create a new user if not found by social id or email', async () => {
      authRepository.findOne.mockResolvedValue(null);
      authRepository.createQueryBuilder().where().getOne.mockResolvedValue(null);
      authService.fetchPublicKeyForAddress.mockResolvedValue('mock-pub-key');
      authService.checkSig.mockResolvedValue(true);
      authRepository.create.mockReturnValue(mockExistingUser);
      authRepository.save.mockResolvedValue(mockExistingUser);

      const result = await service.resolveSocialUserAccount(socialWallet, socialUser);

      expect(authRepository.create).toHaveBeenCalled();
      expect(authRepository.save).toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        sessionToken: mockSessionToken,
        isNewUser: true,
        user: mockTransformedUser,
      });
    });
  });
});
