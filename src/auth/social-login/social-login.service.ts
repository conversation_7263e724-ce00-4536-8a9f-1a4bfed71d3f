import { Injectable, HttpException, HttpStatus, BadRequestException } from '@nestjs/common';
import { IsNull, Not, Repository } from 'typeorm';
import { GoogleService } from './google/google.service';
import { GithubService } from './github/github.service';
import { TwitterService } from './twitter/twitter.service';
import { LinkedinService } from './linkedin/linkedin.service';
import { SocialAuthUserDto, SocialLoginRequestDto } from '../dto/social-auth.dto';
import { User } from '../entities/user.entity';
import { Role } from '../role.enum';
import { InjectRepository } from '@nestjs/typeorm';
import { AuthService } from '../auth.service';
import { sanitizeInput } from '../../utils/sanitize.util';
import { DatabaseErrorCode } from '../../database/database-error-code.enum';

export type SocialUserPayload = {
  sub: string;
  email: string;
  username: string;
  linkedAccount: LinkedAccount;
};

type LinkedAccount = {
  googleId?: string;
  linkedinId?: string;
  githubId?: string;
  twitterId?: string;
};

@Injectable()
export class SocialLoginService {
  constructor(
    @InjectRepository(User)
    private readonly authRepository: Repository<User>,
    private readonly googleService: GoogleService,
    private readonly githubService: GithubService,
    private readonly twitterService: TwitterService,
    private readonly linkedinService: LinkedinService,
    private readonly authService: AuthService,
  ) {}

  async socialLogin(socialLoginDto: SocialLoginRequestDto) {
    const { provider } = socialLoginDto;

    const socialLoginProviders = {
      google: (dto) => this.googleService.googleLogin(dto),
      github: (dto) => this.githubService.githubLogin(dto),
      linkedin: (dto) => this.linkedinService.linkedinLogin(dto),
      twitter: (dto) => this.twitterService.twitterLogin(dto),
    } as const;

    const socialLoginProvider = socialLoginProviders[provider];

    if (!socialLoginProvider) {
      throw new BadRequestException('Unsupported social login provider.');
    }

    const authenticatedSocialUser = await socialLoginProvider(socialLoginDto);

    return this.resolveSocialUserAccount(socialLoginDto.user, authenticatedSocialUser);
  }

  async resolveSocialUserAccount(socialWallet: SocialAuthUserDto, socialUser: SocialUserPayload) {
    return this.authRepository.manager.transaction(async (transactionalEntityManager) => {
      const { address: walletAddress, signature, challengeToken: challenge } = socialWallet;
      const { sub, email, username, linkedAccount } = socialUser;

      await this.authService.validateWallet(challenge, walletAddress, signature);

      let existingUser = null;

      // check if user exists by social id (and not soft-deleted)
      if (sub) {
        existingUser = await transactionalEntityManager.findOne(User, {
          where: {
            // @TODO: Will fail if user has multiple linked accounts
            linkedAccounts: linkedAccount,
            deletedAt: null, // Ensure we only find active users initially
          },
        });
      }

      if (existingUser) {
        // user exists by social id, proceed with login
        return {
          success: true,
          sessionToken: this.authService.generateSessionToken(existingUser),
          isNewUser: false,
          user: this.authService.transformUser(existingUser),
        };
      }

      existingUser = await transactionalEntityManager
        .createQueryBuilder(User, 'user')
        .where('user.email = :email', { email })
        .andWhere('user.deletedAt IS NULL') // Ensure we only find active users initially
        .getOne();

      if (existingUser) {
        existingUser.linkedAccounts = { ...existingUser.linkedAccounts, linkedAccount };
        await transactionalEntityManager.save(User, existingUser);
        return {
          success: true,
          isNewUser: false,
          sessionToken: this.authService.generateSessionToken(existingUser),
          user: this.authService.transformUser(existingUser),
        };
      }

      // Check for SOFT-DELETED user by social id
      if (sub) {
        existingUser = await transactionalEntityManager.findOne(User, {
          where: {
            linkedAccounts: linkedAccount,
            deletedAt: Not(IsNull()), // Find soft-deleted users by social ID
          },
          withDeleted: true, // Include soft-deleted entities in the query
        });
      }

      if (!existingUser && email) {
        // Only check by email if not found by social ID and email exists
        // Check for SOFT-DELETED user by email
        existingUser = await transactionalEntityManager
          .createQueryBuilder(User, 'user')
          .where('user.email = :email', { email })
          .andWhere('user.deletedAt IS NOT NULL') // Find soft-deleted users by email
          .withDeleted() // Include soft-deleted entities in the query
          .getOne();
      }

      if (existingUser) {
        // User is soft-deleted, restore the account
        existingUser.deletedAt = null;
        existingUser.emailVerified = true; // Set emailVerified to true on restore
        existingUser.linkedAccounts = { ...existingUser.linkedAccounts, linkedAccount }; // Ensure linked account is updated in case it wasn't before soft-delete
        await transactionalEntityManager.save(User, existingUser);

        return {
          success: true,
          isNewUser: false, // It's technically not a new user, it's a restored user
          sessionToken: this.authService.generateSessionToken(existingUser),
          user: this.authService.transformUser(existingUser),
        };
      }

      // User doesn't exist (active or soft-deleted), create a new user
      try {
        const user = transactionalEntityManager.create(User, {
          addresses: [walletAddress],
          email: email,
          displayName: sanitizeInput(username),
          emailVerified: true, // Set emailVerified to true for new users
          role: Role.COMMUNITY_MEMBER,
          linkedAccounts: linkedAccount,
        });

        await transactionalEntityManager.save(User, user);

        return {
          success: true,
          sessionToken: this.authService.generateSessionToken(user),
          isNewUser: true,
          user: this.authService.transformUser(user),
        };
      } catch (err) {
        if (err.code === DatabaseErrorCode.UNIQUE_CONSTRAINT_VIOLATION) {
          throw new HttpException('User already exists.', HttpStatus.CONFLICT);
        }
        throw err;
      }
    });
  }
}
