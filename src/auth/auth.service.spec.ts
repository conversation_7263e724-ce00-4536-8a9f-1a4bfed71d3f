jest.mock('octokit', () => ({
  Octokit: jest.fn().mockImplementation(() => ({
    rest: {
      users: {
        getAuthenticated: jest.fn(),
      },
    },
  })),
}));

import { AuthService, JwtScope } from './auth.service';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DataSource, QueryRunner, Repository, SelectQueryBuilder } from 'typeorm';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { RequestChallenge, UserExists, VerifyOtp, VerifySignature } from './dto';
import { Test, TestingModule } from '@nestjs/testing';
import { User } from './entities/user.entity';
import { Role } from './role.enum';

import { MailService } from '../notifications/mail/mail.service';
import { PublicKey } from '@hashgraph/sdk';
import { SocialLoginService } from './social-login/social-login.service';
import { UpdateDisplayNameDto } from './dto/update-display-name.dto';
import { getRepositoryToken } from '@nestjs/typeorm';

const mockFetchPublicKeyForAddress = jest.fn();
const mockCheckSig = jest.fn();

describe('AuthService', () => {
  let service: AuthService;
  let jwtService: JwtService;
  let configService: ConfigService;
  let userRepository: Repository<User>;
  let mailService: MailService;
  let dataSource: DataSource;
  let queryRunner: QueryRunner;
  const mockLoggedInUser = {
    id: 1,
    email: '<EMAIL>',
    emailVerified: true,
    phoneNumber: null,
    isPhoneVerified: null,
    otp: null,
    otpExpiresAt: null,
    role: Role.COMMUNITY_MEMBER,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        // Importing the JwtModule to be able to sign and verify JWT tokens without mocking
        JwtModule.register({ secret: 'test_secret', signOptions: { expiresIn: '1d' } }),
        // Importing the ConfigModule to be able to set some config values
        ConfigModule.forFeature(async () => ({
          CHALLENGE_TOKEN_EXPIRATION_TIME: '5m',
          FRONTEND_BASE_URL: 'https://localhost:3000',
          RECOVERY_TOKEN_EXPIRATION_TIME: '5m',
        })),
      ],
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            createQueryBuilder: jest.fn(() => {
              const mockQueryBuilder = {
                withDeleted: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                getOne: jest.fn(),
              };
              return mockQueryBuilder as unknown as SelectQueryBuilder<User>;
            }),
            update: jest.fn(),
            getOne: jest.fn(),
          },
        },
        {
          provide: MailService,
          useValue: {
            sendEmailVerificationEmail: jest.fn(),
            sendAccountRecoveryEmail: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: {
            createQueryRunner: jest.fn().mockReturnValue({
              connect: jest.fn(),
              startTransaction: jest.fn(),
              commitTransaction: jest.fn(),
              rollbackTransaction: jest.fn(),
              release: jest.fn(),
              manager: {
                update: jest.fn(),
                softRemove: jest.fn(),
              },
            }),
          },
        },
        {
          provide: SocialLoginService,
          useValue: {},
        },
        {
          provide: 'FetchPublicKeyForAddress',
          useValue: mockFetchPublicKeyForAddress,
        }, // Mock external functions
        { provide: 'CheckSig', useValue: mockCheckSig },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    jwtService = module.get<JwtService>(JwtService);
    configService = module.get<ConfigService>(ConfigService);
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    mailService = module.get<MailService>(MailService);
    dataSource = module.get<DataSource>(DataSource);
    queryRunner = dataSource.createQueryRunner();
  });

  describe('request challenge', () => {
    it('should throw BadRequestException for invalid Hedera address', async () => {
      const invalidRequest: RequestChallenge = { address: '0.0.invalid' };
      await expect(service.requestChallenge(invalidRequest, 'https://referer.com')).rejects.toThrow(
        new HttpException('Invalid wallet address.', HttpStatus.BAD_REQUEST),
      );
    });

    it('should throw an error if JWT signing fails', async () => {
      const requestChallengeDto: RequestChallenge = { address: '0.0.12345' };
      jest.spyOn(jwtService, 'signAsync').mockImplementation(() => {
        throw new Error('JWT signing error');
      });

      await expect(service.requestChallenge(requestChallengeDto, 'https://referer.com')).rejects.toThrow(
        new HttpException('JWT signing error', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });

    it('should return a challenge and token if the address is valid', async () => {
      const validAddress = '0.0.3780959';
      const refererURI = 'https://test.com/sign-in';
      const result = await service.requestChallenge({ address: validAddress }, refererURI);

      expect(result.success).toBeTruthy();
      expect(result.challenge).toContain(validAddress);
      expect(result.challenge).toContain(refererURI);
      const payload = jwtService.decode(result.challengeToken);
      expect(payload.challenge).toBeDefined();
      expect(payload.challenge).toContain(validAddress);
      expect(payload.challenge).toContain(refererURI);
    });
  });

  describe('recover wallet', () => {
    it('should throw an error if the user is not found', async () => {
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      await expect(service.recoverAccount({ email: '<EMAIL>' })).rejects.toThrow(
        new HttpException('User not found.', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw an error if email could not be sent', async () => {
      const user = {
        email: '<EMAIL>',
        displayName: 'Test User',
      };
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user as User);
      jest.spyOn(mailService, 'sendAccountRecoveryEmail').mockResolvedValue(false);

      await expect(service.recoverAccount({ email: '<EMAIL>' })).rejects.toThrow(
        new HttpException('Failed to send recovery email to the user.', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });

    it('should throw an error if RECOVERY_TOKEN_EXPIRATION_TIME is null', async () => {
      const requestChallengeDto: RequestChallenge = { address: '0.0.12345' };
      jest.spyOn(configService, 'get').mockImplementation((key: string) => {
        if (key === 'RECOVERY_TOKEN_EXPIRATION_TIME') {
          return null;
        }
        return 'some-value';
      });

      await expect(service.requestChallenge(requestChallengeDto, 'https://referer.com')).rejects.toThrow(
        new HttpException(
          '"expiresIn" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60',
          HttpStatus.INTERNAL_SERVER_ERROR,
        ),
      );
    });

    it('should return success and token when user is found', async () => {
      const user = {
        id: '1',
        addresses: ['0.0.12345'],
        email: '<EMAIL>',
        emailVerified: true,
        displayName: 'Test User',
        createdAt: new Date(),
        otp: '123456',
        otpExpiresAt: new Date(),
        role: Role.COMMUNITY_MEMBER,
      };
      const mockRecoveryToken = 'new token';

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user as any);
      jest.spyOn(jwtService, 'signAsync').mockResolvedValue(mockRecoveryToken);
      jest.spyOn(mailService, 'sendAccountRecoveryEmail').mockResolvedValue(true);

      const result = await service.recoverAccount({ email: '<EMAIL>' });

      // Check if the recovery email was sent with correct parameters
      expect(mailService.sendAccountRecoveryEmail).toHaveBeenCalledWith(user, mockRecoveryToken);

      // Check if the JwtService was called with the correct payload
      expect(jwtService.signAsync).toHaveBeenCalledWith(
        { email: user.email, scope: JwtScope.RECOVERY_TOKEN },
        expect.anything(),
      );

      expect(result).toEqual({
        success: true,
        message: 'Recovery email sent to the user.',
      });
    });
  });

  describe('update wallet address', () => {
    it('should throw an error if the new address is invalid', async () => {
      const invalidRequest: RequestChallenge = { address: '0.0.invalid' };
      await expect(service.requestChallenge(invalidRequest, 'https://referer.com')).rejects.toThrow(
        new HttpException('Invalid wallet address.', HttpStatus.BAD_REQUEST),
      );
    });

    it('should throw an error if the recovery token is invalid', async () => {
      // sign a token with a different secret
      const invalidRecoveryToken = jwtService.sign(
        { email: '<EMAIL>', scope: JwtScope.RECOVERY_TOKEN },
        { expiresIn: '1d', secret: 'another_secret' },
      );

      await expect(
        service.updateWalletAddress({
          recoveryToken: invalidRecoveryToken,
          signature: '0x123',
          challengeToken: 'do not care about this token',
          address: '0xabc',
        }),
      ).rejects.toThrow(new HttpException('Invalid recovery token.', HttpStatus.UNAUTHORIZED));
    });

    it('should throw an error if the recovery token has expired', async () => {
      const expiredRecoveryToken = jwtService.sign(
        { email: '<EMAIL>', scope: JwtScope.RECOVERY_TOKEN },
        { expiresIn: -1000 },
      );
      jest.spyOn(jwtService, 'verifyAsync').mockRejectedValue(new Error('Invalid token'));

      await expect(
        service.updateWalletAddress({
          recoveryToken: expiredRecoveryToken,
          signature: '0x123',
          challengeToken: 'do not care about this token',
          address: '0xabc',
        }),
      ).rejects.toThrow(new HttpException('Invalid recovery token.', HttpStatus.UNAUTHORIZED));
    });

    it('should throw an error if the email is not found in the token', async () => {
      // Sign a valid token that does not contain an 'email' attribute
      const recoveryTokenWithoutEmail = jwtService.sign(
        { someOtherAttribute: 'some_value ', scope: JwtScope.RECOVERY_TOKEN },
        { expiresIn: '1d' },
      );

      await expect(
        service.updateWalletAddress({
          recoveryToken: recoveryTokenWithoutEmail,
          signature: '0x123',
          challengeToken: 'don not care about this token',
          address: '0xabc',
        }),
      ).rejects.toThrow(new HttpException('Email not found in the token provided.', HttpStatus.NOT_FOUND));
    });

    it('should throw an error if the user is not found', async () => {
      const validRecoveryToken = jwtService.sign(
        { email: 'example @example.com', scope: JwtScope.RECOVERY_TOKEN },
        { expiresIn: '1d' },
      );
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      await expect(
        service.updateWalletAddress({
          recoveryToken: validRecoveryToken,
          signature: '0x123',
          challengeToken: 'don not care about this token',
          address: '0xabc',
        }),
      ).rejects.toThrow(new HttpException('User not found.', HttpStatus.NOT_FOUND));
    });

    it('should throw an error if the challenge token is invalid', async () => {
      const validRecoveryToken = jwtService.sign(
        { email: 'example @example.com', scope: JwtScope.RECOVERY_TOKEN },
        { expiresIn: '1d' },
      );
      // sign a challenge token with a different secret
      const invalidChallengeToken = jwtService.sign(
        { challenge: 'some-challenge' },
        { expiresIn: '1d', secret: 'another_secret' },
      );

      jest.spyOn(userRepository, 'findOne').mockResolvedValue({} as User);

      await expect(
        service.updateWalletAddress({
          recoveryToken: validRecoveryToken,
          signature: '0x123',
          challengeToken: invalidChallengeToken,
          address: '0xabc',
        }),
      ).rejects.toThrow(new HttpException('Unauthorized challenge.', HttpStatus.UNAUTHORIZED));
    });

    it('should throw an error if the challenge token has expired', async () => {
      const validRecoveryToken = jwtService.sign(
        { email: 'example @example.com', scope: JwtScope.RECOVERY_TOKEN },
        { expiresIn: '1d' },
      );
      // sign a challenge token with a different secret
      const invalidChallengeToken = jwtService.sign({ challenge: 'some-challenge' }, { expiresIn: -1000 });

      jest.spyOn(userRepository, 'findOne').mockResolvedValue({} as User);

      await expect(
        service.updateWalletAddress({
          recoveryToken: validRecoveryToken,
          signature: '0x123',
          challengeToken: invalidChallengeToken,
          address: '0xabc',
        }),
      ).rejects.toThrow(new HttpException('Unauthorized challenge.', HttpStatus.UNAUTHORIZED));
    });

    it('should throw an error if signature is invalid', async () => {
      const validRecoveryToken = jwtService.sign(
        { email: 'example @example.com', scope: JwtScope.RECOVERY_TOKEN },
        { expiresIn: '1d' },
      );
      const validChallengeToken = jwtService.sign({ challenge: 'test-challenge' }, { expiresIn: '5m' });

      // Mock mirror node's response
      const mirrorNodeAccountResponse = {
        key: { _type: 'ED25519', key: '149d946bec32def2e951988d69b976f6ade2747b8e7446dd7ee5f99e6cd753ac' },
      };
      jest.spyOn(global, 'fetch').mockResolvedValue(new Response(JSON.stringify(mirrorNodeAccountResponse)));
      jest.spyOn(userRepository, 'findOne').mockResolvedValue({} as User);

      expect(
        service.updateWalletAddress({
          recoveryToken: validRecoveryToken,
          challengeToken: validChallengeToken,
          // Signature generated for another challenge than 'test-challenge'
          signature:
            '0x0e050b8b973fb52905575f39e94d1fdee440df9629e5915ddacf79475655be0ccb0d4319779a0ae65a91b47ac1b4eaff5254eb8ee0234377ff77760973e8500d',
          address: '0.0.4234511',
        }),
      ).rejects.toThrow(new HttpException('Invalid signature.', HttpStatus.UNAUTHORIZED));
    });

    it('should return success and update', async () => {
      const email = '<EMAIL>';
      const validRecoveryToken = jwtService.sign({ email: email, scope: JwtScope.RECOVERY_TOKEN }, { expiresIn: '1d' });
      const validChallengeToken = jwtService.sign({ challenge: 'test-challenge' }, { expiresIn: '5m' });

      // Mocking existing user
      const mockUser = new User();
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(mockUser);

      // Mock the methods required for signature verification
      jest.spyOn(service as any, 'fetchPublicKeyForAddress').mockResolvedValue({});
      jest.spyOn(service as any, 'checkSig').mockReturnValue(true);
      jest.spyOn(userRepository, 'save').mockResolvedValue(mockUser);

      const newAddress = '0.0.4234511';
      const result = await service.updateWalletAddress({
        recoveryToken: validRecoveryToken,
        challengeToken: validChallengeToken,
        signature: '0xbc64154d', // doesn't matter because signature verification is mocked
        address: newAddress,
      });

      expect(result).toEqual({
        success: true,
        message: 'Wallet address updated successfully.',
      });

      // Check if the correct email address was used to find the user
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { email: email } });
      // Check if the save method was called with the new Hedera address.
      mockUser.addresses = [newAddress];
      expect(userRepository.save).toHaveBeenCalledWith(mockUser);
    });
  });

  describe('send otp', () => {
    it('should send OTP to the user', async () => {
      const user = new User();
      user.id = 1;
      user.email = '<EMAIL>';
      user.displayName = 'Test User';
      user.otp = '123456';
      user.otpExpiresAt = new Date();
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);
      jest.spyOn(userRepository, 'save').mockResolvedValue(user);
      jest.spyOn(mailService, 'sendEmailVerificationEmail').mockResolvedValue(true);

      const result = await service.sendOTP(mockLoggedInUser);

      expect(result).toEqual({
        success: true,
        message: 'OTP sent to the user.',
      });
    });

    it('should throw an error if sending OTP fails', async () => {
      const user = {
        id: 1,
        addresses: ['0.0.12345'],
        email: '<EMAIL>',
        emailVerified: false,
        displayName: 'Test User',
        createdAt: new Date(),
        otp: null,
        otpExpiresAt: null,
        role: Role.COMMUNITY_MEMBER,
      } as Partial<User>;

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user as any);
      jest.spyOn(userRepository, 'save').mockResolvedValue(user as any);
      jest.spyOn(mailService, 'sendEmailVerificationEmail').mockResolvedValue(false);

      await expect(service.sendOTP(mockLoggedInUser)).rejects.toThrow(
        new InternalServerErrorException('Failed to send OTP to the user.'),
      );
    });

    it('should throw an error if user email is already verified', async () => {
      const user = {
        id: 1,
        addresses: ['0.0.12345'],
        email: '<EMAIL>',
        emailVerified: true,
        displayName: 'Test User',
        createdAt: new Date(),
        otp: null,
        otpExpiresAt: null,
        role: Role.COMMUNITY_MEMBER,
      } as Partial<User>;

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user as any);

      await expect(service.sendOTP(mockLoggedInUser)).rejects.toThrow(
        new BadRequestException('User email already verified.'),
      );
    });

    it('should throw an error if the user is not found', async () => {
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      await expect(service.sendOTP(mockLoggedInUser)).rejects.toThrow(new NotFoundException('User not found.'));
    });
  });

  describe('verify otp', () => {
    it('should verify the OTP', async () => {
      const user = new User();
      user.id = 1;
      user.otpExpiresAt = new Date(Date.now() + 5 * 60 * 1000);
      user.otp = '123456';
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);
      jest.spyOn(userRepository, 'save').mockResolvedValue(user);

      expect(service.verifyOTP({ otp: '123456' } satisfies VerifyOtp, mockLoggedInUser)).resolves.toBeUndefined();
    });

    it('should throw an error if the user is not found', async () => {
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      await expect(service.verifyOTP({ otp: '123456' } as VerifyOtp, mockLoggedInUser)).rejects.toThrow(
        new NotFoundException('User not found.'),
      );
    });

    it('should throw an error if the OTP is expired', async () => {
      const user = new User();
      user.id = 1;
      user.otpExpiresAt = new Date(Date.now() - 5 * 60 * 1000);
      user.otp = '123456';
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);

      await expect(service.verifyOTP({ otp: '123456' } as VerifyOtp, mockLoggedInUser)).rejects.toThrow(
        new HttpException('OTP expired.', HttpStatus.UNAUTHORIZED),
      );
    });

    it('should throw an error if the OTP is invalid', async () => {
      const user = new User();
      user.id = 1;
      user.otpExpiresAt = new Date(Date.now() + 5 * 60 * 1000);
      user.otp = '123456';
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);

      await expect(service.verifyOTP({ otp: '654321' } as VerifyOtp, mockLoggedInUser)).rejects.toThrow(
        new HttpException('Invalid OTP.', HttpStatus.UNAUTHORIZED),
      );
    });
  });

  describe('user details', () => {
    it('should return user details', async () => {
      const user = new User();
      user.id = 1;
      user.addresses = ['0xabc'];
      user.email = '<EMAIL>';
      user.emailVerified = true;
      user.displayName = 'Test User';
      user.createdAt = new Date();
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);

      const result = await service.userDetails(mockLoggedInUser);

      expect(result).toEqual({
        success: true,
        user: {
          id: user.id,
          addresses: user.addresses,
          email: user.email,
          emailVerified: user.emailVerified,
          displayName: user.displayName,
          createdAt: user.createdAt,
        },
      });
    });

    it('should throw an error if the user is not found', async () => {
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      await expect(service.userDetails(mockLoggedInUser)).rejects.toThrow(
        new HttpException('User not found.', HttpStatus.NOT_FOUND),
      );
    });
  });

  describe('signUp', () => {
    it('should throw an error if the challenge token is invalid', async () => {
      await expect(
        service.signUp({
          address: '0.0.12345',
          challengeToken: 'invalid_token',
          signature: '0x123',
          email: '<EMAIL>',
          displayName: 'Test User',
        }),
      ).rejects.toThrow(new HttpException('Unauthorized challenge.', HttpStatus.UNAUTHORIZED));
    });

    it('should throw an error if the address in the token does not match the request', async () => {
      const validToken = jwtService.sign(
        { address: '0.0.12345', scope: JwtScope.CHALLENGE_TOKEN },
        { expiresIn: '1d' },
      );

      await expect(
        service.signUp({
          address: '0.0.54321',
          challengeToken: validToken,
          signature: '0x123',
          email: '<EMAIL>',
          displayName: 'Test User',
        }),
      ).rejects.toThrow(
        new HttpException('Address in the token does not match the address in the request.', HttpStatus.BAD_REQUEST),
      );
    });

    it('should throw an error if the user already exists with the given wallet address', async () => {
      const validToken = jwtService.sign(
        { address: '0.0.12345', scope: JwtScope.CHALLENGE_TOKEN },
        { expiresIn: '1d' },
      );
      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue({} as User);

      await expect(
        service.signUp({
          address: '0.0.12345',
          challengeToken: validToken,
          signature: '0x123',
          email: '<EMAIL>',
          displayName: 'Test User',
        }),
      ).rejects.toThrow(new HttpException('User already exists with given wallet address.', HttpStatus.CONFLICT));
    });

    it('should throw an error if the user already exists with the given email', async () => {
      const validToken = jwtService.sign(
        { address: '0.0.12345', scope: JwtScope.CHALLENGE_TOKEN },
        { expiresIn: '1d' },
      );

      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(null);

      // Mock userRepository.createQueryBuilder for email check to simulate user exists by email (active user)
      (userRepository.createQueryBuilder as jest.Mock).mockReturnValue({
        withDeleted: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue({ id: 'some-user-id', deletedAt: null }),
      });

      await expect(
        service.signUp({
          address: '0.0.12345',
          challengeToken: validToken,
          signature: '0x123',
          email: '<EMAIL>',
          displayName: 'Test User',
        }),
      ).rejects.toThrow(new HttpException('User already exists with given email.', HttpStatus.CONFLICT));
    });

    it('should throw an error if the signature is invalid', async () => {
      const validToken = jwtService.sign(
        { address: '0.0.12345', scope: JwtScope.CHALLENGE_TOKEN },
        { expiresIn: '1d' },
      );
      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(null);
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);
      jest.spyOn(service as any, 'fetchPublicKeyForAddress').mockResolvedValue({} as PublicKey);
      jest.spyOn(service as any, 'checkSig').mockReturnValue(false);

      await expect(
        service.signUp({
          address: '0.0.12345',
          challengeToken: validToken,
          signature: '0x123',
          email: '<EMAIL>',
          displayName: 'Test User',
        }),
      ).rejects.toThrow(new HttpException('Invalid signature.', HttpStatus.UNAUTHORIZED));
    });

    it('should create a new user successfully', async () => {
      const validToken = jwtService.sign(
        { address: '0.0.12345', scope: JwtScope.CHALLENGE_TOKEN },
        { expiresIn: '1d' },
      );
      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(null);
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);
      jest.spyOn(service as any, 'fetchPublicKeyForAddress').mockResolvedValue({} as PublicKey);
      jest.spyOn(service as any, 'checkSig').mockReturnValue(true);
      jest.spyOn(userRepository, 'create').mockReturnValue({} as User);
      jest.spyOn(userRepository, 'save').mockResolvedValue({} as User);

      const result = await service.signUp({
        address: '0.0.12345',
        challengeToken: validToken,
        signature: '0x123',
        email: '<EMAIL>',
        displayName: 'Test User',
      });

      expect(result).toEqual({
        success: true,
        message: 'User created successfully.',
      });
    });
  });

  describe('signIn', () => {
    it('should throw an error if the challenge token is invalid', async () => {
      await expect(
        service.signIn({
          address: '0.0.12345',
          challengeToken: 'invalid_token',
          signature: '0x123',
        }),
      ).rejects.toThrow(new HttpException('Unauthorized challenge.', HttpStatus.UNAUTHORIZED));
    });

    it('should throw an error if the token scope is invalid', async () => {
      const invalidScopeToken = jwtService.sign({ address: '0.0.12345', scope: 'INVALID_SCOPE' }, { expiresIn: '1d' });

      await expect(
        service.signIn({
          address: '0.0.12345',
          challengeToken: invalidScopeToken,
          signature: '0x123',
        }),
      ).rejects.toThrow(new HttpException('Invalid token scope.', HttpStatus.BAD_REQUEST));
    });

    it('should throw an error if the address in the token does not match the request', async () => {
      const validToken = jwtService.sign(
        { address: '0.0.12345', scope: JwtScope.CHALLENGE_TOKEN },
        { expiresIn: '1d' },
      );

      await expect(
        service.signIn({
          address: '0.0.54321',
          challengeToken: validToken,
          signature: '0x123',
        }),
      ).rejects.toThrow(
        new HttpException('Address in the token does not match the address in the request.', HttpStatus.BAD_REQUEST),
      );
    });

    it('should throw an error if the user is not found', async () => {
      const validToken = jwtService.sign(
        { address: '0.0.12345', scope: JwtScope.CHALLENGE_TOKEN },
        { expiresIn: '1d' },
      );
      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(null);

      await expect(
        service.signIn({
          address: '0.0.12345',
          challengeToken: validToken,
          signature: '0x123',
        }),
      ).rejects.toThrow(new HttpException('User not found.', HttpStatus.NOT_FOUND));
    });

    it('should throw an error if the signature is invalid', async () => {
      const validToken = jwtService.sign(
        { address: '0.0.12345', scope: JwtScope.CHALLENGE_TOKEN },
        { expiresIn: '1d' },
      );
      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue({} as User);
      jest.spyOn(service as any, 'fetchPublicKeyForAddress').mockResolvedValue({} as PublicKey);
      jest.spyOn(service as any, 'checkSig').mockReturnValue(false);

      await expect(
        service.signIn({
          address: '0.0.12345',
          challengeToken: validToken,
          signature: '0x123',
        }),
      ).rejects.toThrow(new HttpException('Invalid signature.', HttpStatus.UNAUTHORIZED));
    });

    it('should sign in the user successfully', async () => {
      const validToken = jwtService.sign(
        { address: '0.0.12345', scope: JwtScope.CHALLENGE_TOKEN },
        { expiresIn: '1d' },
      );
      const mockUser = {
        id: 1,
        addresses: ['0.0.12345'],
        email: '<EMAIL>',
        emailVerified: true,
        displayName: 'Test User',
        role: Role.COMMUNITY_MEMBER,
        createdAt: new Date(),
      } as User;
      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(mockUser);
      jest.spyOn(service as any, 'fetchPublicKeyForAddress').mockResolvedValue({} as PublicKey);
      jest.spyOn(service as any, 'checkSig').mockReturnValue(true);
      jest.spyOn(service as any, 'generateSessionToken').mockReturnValue('session_token');

      const result = await service.signIn({
        address: '0.0.12345',
        challengeToken: validToken,
        signature: '0x123',
      });

      expect(result).toEqual({
        success: true,
        sessionToken: 'session_token',
        user: {
          id: mockUser.id,
          addresses: mockUser.addresses,
          email: mockUser.email,
          emailVerified: mockUser.emailVerified,
          displayName: mockUser.displayName,
          role: mockUser.role,
          createdAt: mockUser.createdAt,
        },
      });
    });
  });

  describe('addWalletToAccount', () => {
    it('should throw an error if the user is not found', async () => {
      const verifySignatureDto: VerifySignature = {
        address: '0.0.12345',
        challengeToken: 'valid-token',
        signature: 'valid-signature',
      };

      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(null);
      jest
        .spyOn(jwtService, 'verifyAsync')
        .mockResolvedValue({ challenge: 'challenge', scope: JwtScope.CHALLENGE_TOKEN });
      jest
        .spyOn(service as any, 'fetchPublicKeyForAddress')
        .mockResolvedValue(
          PublicKey.fromString(
            '302a300506032b6570032100a3b2e5c3e5b2e5c3e5b2e5c3e5b2e5c3e5b2e5c3e5b2e5c3e5b2e5c3e5b2e5c3',
          ),
        );
      jest.spyOn(service as any, 'checkSig').mockReturnValue(true);
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      await expect(service.addWalletToAccount(verifySignatureDto, mockLoggedInUser)).rejects.toThrow(
        new HttpException('User not found.', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw an error if the wallet address already exists', async () => {
      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue({} as User);

      await expect(
        service.addWalletToAccount(
          {
            address: '0.0.12345',
            challengeToken: 'valid_token',
            signature: '0x123',
          },
          mockLoggedInUser,
        ),
      ).rejects.toThrow(new HttpException('Wallet address already exists.', HttpStatus.CONFLICT));
    });

    it('should throw an error if the challenge token is invalid', async () => {
      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(null);

      await expect(
        service.addWalletToAccount(
          {
            address: '0.0.12345',
            challengeToken: 'invalid_token',
            signature: '0x123',
          },
          mockLoggedInUser,
        ),
      ).rejects.toThrow(new HttpException('Unauthorized challenge.', HttpStatus.UNAUTHORIZED));
    });

    it('should throw an error if the challenge is not found in the token', async () => {
      const validToken = jwtService.sign(
        { address: '0.0.12345', scope: JwtScope.CHALLENGE_TOKEN },
        { expiresIn: '1d' },
      );
      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(null);

      await expect(
        service.addWalletToAccount(
          {
            address: '0.0.12345',
            challengeToken: validToken,
            signature: '0x123',
          },
          mockLoggedInUser,
        ),
      ).rejects.toThrow(new HttpException('Challenge not found in the token provided.', HttpStatus.NOT_FOUND));
    });

    it('should throw an error if the signature is invalid', async () => {
      const validToken = jwtService.sign(
        { address: '0.0.12345', scope: JwtScope.CHALLENGE_TOKEN, challenge: 'test-challenge' },
        { expiresIn: '1d' },
      );

      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(null);
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);
      jest.spyOn(service as any, 'fetchPublicKeyForAddress').mockResolvedValue({} as PublicKey);
      jest.spyOn(service as any, 'checkSig').mockReturnValue(false);

      await expect(
        service.addWalletToAccount(
          {
            address: '0.0.12345',
            challengeToken: validToken,
            signature: '0x123',
          },
          mockLoggedInUser,
        ),
      ).rejects.toThrow(new HttpException('Invalid signature.', HttpStatus.UNAUTHORIZED));
    });

    it('should throw an error if the challenge token is expired', async () => {
      const verifySignatureDto: VerifySignature = {
        address: '0.0.12345',
        challengeToken: 'expired-token',
        signature: 'valid-signature',
      };

      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(null);
      jest.spyOn(jwtService, 'verifyAsync').mockRejectedValue(new Error('Token expired'));

      await expect(service.addWalletToAccount(verifySignatureDto, mockLoggedInUser)).rejects.toThrow(
        new HttpException('Unauthorized challenge.', HttpStatus.UNAUTHORIZED),
      );
    });

    it('should throw ForbiddenException if trying to add wallet to soft-deleted account', async () => {
      const verifySignatureDto: VerifySignature = {
        address: '0.0.12345',
        challengeToken: 'valid-token',
        signature: 'valid-signature',
      };

      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(null);
      jest
        .spyOn(jwtService, 'verifyAsync')
        .mockResolvedValue({ challenge: 'challenge', scope: JwtScope.CHALLENGE_TOKEN });
      jest
        .spyOn(service as any, 'fetchPublicKeyForAddress')
        .mockResolvedValue(
          PublicKey.fromString(
            '302a300506032b6570032100a3b2e5c3e5b2e5c3e5b2e5c3e5b2e5c3e5b2e5c3e5b2e5c3e5b2e5c3e5b2e5c3',
          ),
        );
      jest.spyOn(service as any, 'checkSig').mockReturnValue(true);

      const softDeletedUser = new User();
      softDeletedUser.deletedAt = new Date();

      jest.spyOn(userRepository, 'findOne').mockImplementationOnce(async () => {
        return softDeletedUser;
      });

      await expect(service.addWalletToAccount(verifySignatureDto, mockLoggedInUser)).rejects.toThrow(
        new HttpException(
          'Account is deleted and cannot be modified. Please recover your account first.',
          HttpStatus.FORBIDDEN,
        ),
      );
    });

    it('should add the wallet to the user account successfully', async () => {
      const validToken = jwtService.sign(
        { address: '0.0.12345', scope: JwtScope.CHALLENGE_TOKEN, challenge: 'test-challenge' },
        { expiresIn: '1d' },
      );
      const mockUser = {
        id: 1,
        addresses: ['0.0.54321'],
      } as User;
      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(null);
      jest.spyOn(service as any, 'fetchPublicKeyForAddress').mockResolvedValue({} as PublicKey);
      jest.spyOn(service as any, 'checkSig').mockReturnValue(true);
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(mockUser);
      jest.spyOn(userRepository, 'save').mockResolvedValue(mockUser);

      const result = await service.addWalletToAccount(
        {
          address: '0.0.12345',
          challengeToken: validToken,
          signature: '0x123',
        },
        mockLoggedInUser,
      );

      expect(result).toEqual({
        success: true,
        message: 'Wallet successfully added to the account.',
      });
      expect(userRepository.save).toHaveBeenCalledWith({
        ...mockUser,
        addresses: ['0.0.12345', '0.0.54321'],
      });
    });
  });

  describe('userExists', () => {
    it('should return true if user exists with the given email', async () => {
      const userExistsDto: UserExists = { email: '<EMAIL>' };
      jest.spyOn(userRepository, 'findOne').mockResolvedValue({} as User);

      const result = await service.userExists(userExistsDto);

      expect(result).toEqual({ exists: true, success: true });
    });

    it('should return false if the user does not exist', async () => {
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      const result = await service.userExists({ email: Buffer.from('<EMAIL>').toString('base64') });

      expect(result).toEqual({
        success: true,
        exists: false,
      });
    });
  });

  describe('updateDisplayName', () => {
    it('should update the display name successfully', async () => {
      const updateDisplayNameDto: UpdateDisplayNameDto = { displayName: 'John Doe' };
      const request = { headers: { authorization: 'Bearer valid-token' }, user: { id: 1 } } as any;
      const user = new User();
      user.id = 1;
      user.displayName = 'Old Name';

      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue({ id: 1 });
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);
      jest.spyOn(userRepository, 'save').mockResolvedValue(user);

      const result = await service.updateDisplayName(updateDisplayNameDto, request);

      expect(result).toEqual({
        success: true,
        message: 'Display name updated successfully.',
      });
      expect(userRepository.save).toHaveBeenCalledWith(expect.objectContaining({ displayName: 'John Doe' }));
    });

    it('should throw an error if the user is not found', async () => {
      const updateDisplayNameDto: UpdateDisplayNameDto = { displayName: 'John Doe' };
      const request = { headers: { authorization: 'Bearer valid-token' } } as any;

      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue({ id: 1 });
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      await expect(service.updateDisplayName(updateDisplayNameDto, request)).rejects.toThrow(
        new HttpException('User not found.', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw ForbiddenException if trying to update display name of soft-deleted account', async () => {
      const updateDisplayNameDto: UpdateDisplayNameDto = { displayName: 'John Doe' };
      const request = { headers: { authorization: 'Bearer valid-token' }, user: { id: 1 } } as any;
      const softDeletedUser = new User();
      softDeletedUser.id = 1;
      softDeletedUser.displayName = 'Old Name';
      softDeletedUser.deletedAt = new Date(); // Simulate soft-deleted user

      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue({ id: 1 });
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(softDeletedUser);

      await expect(service.updateDisplayName(updateDisplayNameDto, request)).rejects.toThrow(
        new HttpException('Account is deleted and cannot be updated.', HttpStatus.FORBIDDEN),
      );
    });
  });

  describe('deleteAccount', () => {
    it('should throw an exception if user is not found', async () => {
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(null);

      await expect(service.deleteAccount(mockLoggedInUser)).rejects.toThrow(
        new HttpException('User not found.', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw an error if the user is a GRANT_PROGRAM_COORDINATOR', async () => {
      const user = { id: 123, role: Role.COORDINATOR } as User;

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);

      await expect(service.deleteAccount({ ...mockLoggedInUser, role: Role.COORDINATOR })).rejects.toThrow(
        new HttpException('Grant program coordinators cannot delete their accounts.', HttpStatus.FORBIDDEN),
      );
    });

    it('should successfully delete the user account', async () => {
      const user = { id: 123, role: Role.COMMUNITY_MEMBER } as User;

      jest.spyOn(userRepository, 'findOne').mockResolvedValue(user);
      jest.spyOn(service as any, 'handleSoftDelete').mockResolvedValue(undefined); // Spy on the private method

      const result = await service.deleteAccount(mockLoggedInUser);

      expect(result).toEqual({ success: true, message: 'Account deleted successfully.' });
      expect(service['handleSoftDelete']).toHaveBeenCalledWith(user);
    });
  });

  describe('handleSoftDelete', () => {
    it('should perform a soft delete and nullify related entities', async () => {
      const user = { id: 123 } as User;
      await (service as any).handleSoftDelete(user);

      expect(queryRunner.connect).toHaveBeenCalled();
      expect(queryRunner.startTransaction).toHaveBeenCalled();
      expect(queryRunner.manager.softRemove).toHaveBeenCalledWith(User, { id: user.id });
      expect(queryRunner.commitTransaction).toHaveBeenCalled();
      expect(queryRunner.release).toHaveBeenCalled();
    });
  });

  describe('recoverDeletedAccount', () => {
    const mockRecoverAccountDto = {
      address: 'testAddress',
      challengeToken: 'testChallengeToken',
      signature: 'testSignature',
    };
    const mockDecodedToken = {
      scope: JwtScope.CHALLENGE_TOKEN,
      address: 'testAddress',
      challenge: 'testChallenge',
    };
    const mockExistingUser = {
      id: 'some-user-id',
      addresses: ['testAddress'],
      deletedAt: new Date(), // Simulate soft-deleted user
      save: jest.fn().mockResolvedValue(undefined), // Mock save method
    } as any as User;

    it('should throw UnauthorizedException if challenge token verification fails', async () => {
      jest.spyOn(jwtService, 'verifyAsync').mockRejectedValue(new Error('Unauthorized'));

      await expect(service.recoverDeletedAccount(mockRecoverAccountDto)).rejects.toThrow(
        new HttpException('Unauthorized challenge.', HttpStatus.UNAUTHORIZED),
      );
      expect(jwtService.verifyAsync).toHaveBeenCalledWith(mockRecoverAccountDto.challengeToken);
    });

    it('should throw BadRequestException if token scope is invalid', async () => {
      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue({ ...mockDecodedToken, scope: 'invalidScope' });

      await expect(service.recoverDeletedAccount(mockRecoverAccountDto)).rejects.toThrow(
        new HttpException('Invalid token scope.', HttpStatus.BAD_REQUEST),
      );
      expect(jwtService.verifyAsync).toHaveBeenCalledWith(mockRecoverAccountDto.challengeToken);
    });

    it('should throw BadRequestException if address in token does not match request', async () => {
      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue({ ...mockDecodedToken, address: 'differentAddress' });

      await expect(service.recoverDeletedAccount(mockRecoverAccountDto)).rejects.toThrow(
        new HttpException('Address in the token does not match the address in the request.', HttpStatus.BAD_REQUEST),
      );
      expect(jwtService.verifyAsync).toHaveBeenCalledWith(mockRecoverAccountDto.challengeToken);
    });

    it('should throw NotFoundException if no deleted account found', async () => {
      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue(mockDecodedToken);
      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(null); // Simulate user not found

      await expect(service.recoverDeletedAccount(mockRecoverAccountDto)).rejects.toThrow(
        new HttpException('No deleted account found for given wallet address.', HttpStatus.NOT_FOUND),
      );
      expect(jwtService.verifyAsync).toHaveBeenCalledWith(mockRecoverAccountDto.challengeToken);
      expect(service['getExistingUserByAddress']).toHaveBeenCalledWith(mockRecoverAccountDto.address);
    });

    it('should throw NotFoundException if account is not soft-deleted', async () => {
      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue(mockDecodedToken);
      jest
        .spyOn(service as any, 'getExistingUserByAddress')
        .mockResolvedValue({ ...mockExistingUser, deletedAt: null }); // Simulate not soft-deleted

      await expect(service.recoverDeletedAccount(mockRecoverAccountDto)).rejects.toThrow(
        new HttpException('No deleted account found for given wallet address.', HttpStatus.NOT_FOUND),
      );
      expect(jwtService.verifyAsync).toHaveBeenCalledWith(mockRecoverAccountDto.challengeToken);
      expect(service['getExistingUserByAddress']).toHaveBeenCalledWith(mockRecoverAccountDto.address);
    });

    it('should throw UnauthorizedException if signature is invalid', async () => {
      jest.spyOn(service as any, 'fetchPublicKeyForAddress').mockImplementation(mockFetchPublicKeyForAddress);
      jest.spyOn(service as any, 'checkSig').mockReturnValue(false);
      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue(mockDecodedToken);
      jest
        .spyOn(service as any, 'getExistingUserByAddress')
        .mockResolvedValue({ ...mockExistingUser, deletedAt: new Date() });
      mockFetchPublicKeyForAddress.mockResolvedValue('mockPublicKey');

      await expect(service.recoverDeletedAccount(mockRecoverAccountDto)).rejects.toThrow(
        new HttpException('Invalid signature.', HttpStatus.UNAUTHORIZED),
      );

      expect(jwtService.verifyAsync).toHaveBeenCalledWith(mockRecoverAccountDto.challengeToken);
      expect(service['getExistingUserByAddress']).toHaveBeenCalledWith(mockRecoverAccountDto.address);
      expect(service['fetchPublicKeyForAddress']).toHaveBeenCalledWith(mockRecoverAccountDto.address);
      expect(service['checkSig']).toHaveBeenCalledWith(
        'mockPublicKey',
        mockRecoverAccountDto.signature,
        mockDecodedToken.challenge,
      );
    });

    it('should recover deleted account successfully', async () => {
      jest.spyOn(service as any, 'fetchPublicKeyForAddress').mockImplementation(mockFetchPublicKeyForAddress);
      jest.spyOn(service as any, 'checkSig').mockImplementation(mockCheckSig);
      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue(mockDecodedToken);
      jest.spyOn(service as any, 'getExistingUserByAddress').mockResolvedValue(mockExistingUser);
      mockFetchPublicKeyForAddress.mockResolvedValue('mockPublicKey');
      mockCheckSig.mockResolvedValue(true);
      jest.spyOn(userRepository, 'save').mockResolvedValue(mockExistingUser);

      const result = await service.recoverDeletedAccount(mockRecoverAccountDto);

      expect(result).toEqual({
        success: true,
        message: 'Account recovered successfully.',
      });
      expect(jwtService.verifyAsync).toHaveBeenCalledWith(mockRecoverAccountDto.challengeToken);
      expect(service['getExistingUserByAddress']).toHaveBeenCalledWith(mockRecoverAccountDto.address);
      expect(service['fetchPublicKeyForAddress']).toHaveBeenCalledWith(mockRecoverAccountDto.address);
      expect(service['checkSig']).toHaveBeenCalledWith(
        'mockPublicKey',
        mockRecoverAccountDto.signature,
        mockDecodedToken.challenge,
      );
      expect(userRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          deletedAt: null,
          reactivatedAt: expect.any(Date),
        }),
      );
    });

    it('should throw InternalServerErrorException if save operation fails', async () => {
      jest.spyOn(service as any, 'fetchPublicKeyForAddress').mockImplementation(mockFetchPublicKeyForAddress);
      jest.spyOn(service as any, 'checkSig').mockImplementation(mockCheckSig);
      jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue(mockDecodedToken);
      jest
        .spyOn(service as any, 'getExistingUserByAddress')
        .mockResolvedValue({ ...mockExistingUser, deletedAt: new Date() });
      mockFetchPublicKeyForAddress.mockResolvedValue('mockPublicKey');
      mockCheckSig.mockResolvedValue(true);
      jest.spyOn(userRepository, 'save').mockRejectedValue(new Error('Save failed'));

      await expect(service.recoverDeletedAccount(mockRecoverAccountDto)).rejects.toThrow(
        new HttpException('Save failed', HttpStatus.INTERNAL_SERVER_ERROR),
      );

      expect(jwtService.verifyAsync).toHaveBeenCalledWith(mockRecoverAccountDto.challengeToken);
      expect(service['getExistingUserByAddress']).toHaveBeenCalledWith(mockRecoverAccountDto.address);
      expect(service['fetchPublicKeyForAddress']).toHaveBeenCalledWith(mockRecoverAccountDto.address);
      expect(service['checkSig']).toHaveBeenCalledWith(
        'mockPublicKey',
        mockRecoverAccountDto.signature,
        mockDecodedToken.challenge,
      );
      expect(userRepository.save).toHaveBeenCalled();
    });
  });
});
