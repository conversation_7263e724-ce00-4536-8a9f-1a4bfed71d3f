import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtScope } from '../auth.service';
import { LoggedInUser } from '../decorators/logged-in-user.decorator';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    readonly configService: ConfigService,
    @InjectRepository(User) private userRepository: Repository<User>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  async validate(decodedData: any): Promise<LoggedInUser> {
    if (decodedData.payload.scope !== JwtScope.SESSION_TOKEN) {
      throw new UnauthorizedException('Invalid token scope');
    }

    const user = await this.userRepository.findOne({
      where: {
        id: decodedData.payload.id,
      },
    });

    if (!user || user.deletedAt) {
      throw new UnauthorizedException();
    }

    return {
      id: user.id,
      email: user.email,
      emailVerified: user.emailVerified,
      phoneNumber: user.phoneNumber,
      isPhoneVerified: user.isPhoneVerified,
      role: user.role,
      otp: user.otp,
      otpExpiresAt: user.otpExpiresAt,
    };
  }
}
