import { Controller, Get, HttpStatus, INestApplication } from '@nestjs/common';
import { Public } from '../decorators/public.decorator';
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { Role } from '../role.enum';
import { JwtStrategy } from './jwt.strategy';
import { APP_GUARD, Reflector } from '@nestjs/core';
import { JwtAuthGuard } from './jwt-auth.guard';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../entities/user.entity';
import request from 'supertest';
import { JwtService } from '@nestjs/jwt';
import { JwtScope } from '../auth.service';
import { AllowUnverified } from '../decorators/allow-unverified.decorator';

@Controller('test')
class TestController {
  @Get('public')
  @Public()
  getPublic() {
    return 'public';
  }

  @Get('private')
  getPrivate() {
    return 'private';
  }

  @Get('allow-unverified')
  @AllowUnverified()
  getAllowUnverified() {
    return 'allow-unverified';
  }
}

const mockUser = {
  id: 1,
  email: '<EMAIL>',
  displayName: 'Test User',
  emailVerified: true,
  role: Role.COMMUNITY_MEMBER,
  addresses: [],
};

const mockUserRepository = {
  findOne: jest.fn().mockResolvedValue(mockUser),
};

describe('JwtAuthGuard', () => {
  let app: INestApplication;
  let sessionToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [() => ({ JWT_SECRET: 'test-secret' })],
        }),
      ],
      controllers: [TestController],
      providers: [
        JwtService,
        JwtStrategy,
        Reflector,
        {
          provide: APP_GUARD,
          useExisting: JwtAuthGuard,
        },
        JwtAuthGuard,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    const jwtService = moduleFixture.get<JwtService>(JwtService);
    sessionToken = jwtService.sign(
      {
        payload: {
          id: mockUser.id,
          scope: JwtScope.SESSION_TOKEN,
        },
      },
      { secret: 'test-secret' },
    );

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('GET /test/public should work without session token', () => {
    return request(app.getHttpServer()).get('/test/public').expect(HttpStatus.OK).expect('public');
  });

  it('GET /test/private should return 401 if no session token', () => {
    return request(app.getHttpServer()).get('/test/private').expect(HttpStatus.UNAUTHORIZED);
  });

  it('GET /test/private should succeed with valid session token', () => {
    return request(app.getHttpServer())
      .get('/test/private')
      .set('Authorization', `Bearer ${sessionToken}`)
      .expect(HttpStatus.OK)
      .expect('private');
  });

  it('GET /test/private should fail if email not verified', async () => {
    mockUserRepository.findOne.mockResolvedValueOnce({
      ...mockUser,
      emailVerified: false,
    });

    const res = await request(app.getHttpServer()).get('/test/private').set('Authorization', `Bearer ${sessionToken}`);

    expect(res.status).toBe(HttpStatus.UNAUTHORIZED);
    expect(res.body.message).toBe('Email not verified');
  });

  it('GET /test/allow-unverified should allow access even if email is not verified', async () => {
    mockUserRepository.findOne.mockResolvedValueOnce({
      ...mockUser,
      emailVerified: false,
    });

    const res = await request(app.getHttpServer())
      .get('/test/allow-unverified')
      .set('Authorization', `Bearer ${sessionToken}`);

    expect(res.status).toBe(HttpStatus.OK);
    expect(res.text).toBe('allow-unverified');
  });
});
