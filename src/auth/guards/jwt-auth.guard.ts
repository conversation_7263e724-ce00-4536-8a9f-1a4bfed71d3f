import { ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { PUBLIC_KEY } from '../decorators/public.decorator';
import { ALLOW_UNVERIFIED_KEY } from '../decorators/allow-unverified.decorator';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(context: ExecutionContext) {
    const isPublic = this.reflector.getAllAndOverride<boolean>(PUBLIC_KEY, [context.getHandler(), context.getClass()]);

    const request = context.switchToHttp().getRequest();
    const sessionToken = request.headers['authorization'];

    if (isPublic && !sessionToken) {
      return true;
    }

    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, _: any, context: ExecutionContext): any {
    if (err || !user) {
      throw err || new UnauthorizedException();
    }

    const allowUnverified = this.reflector.getAllAndOverride<boolean>(ALLOW_UNVERIFIED_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!user.emailVerified && !allowUnverified) {
      throw new UnauthorizedException('Email not verified');
    }

    return user;
  }
}
