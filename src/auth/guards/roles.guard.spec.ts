import { APP_GUARD } from '@nestjs/core';
import { RolesGuard } from './roles.guard';
import { Controller, ExecutionContext, Get, HttpStatus, Injectable, UseGuards } from '@nestjs/common';
import { CanActivate, INestApplication } from '@nestjs/common/interfaces';
import { Role } from '../role.enum';
import { Roles } from '../decorators/roles.decorator';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';

// Mock guard to inject user role from header
@Injectable()
class MockAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const req = context.switchToHttp().getRequest();
    req.user = {
      role: req.headers['x-user-role'],
    };
    return true;
  }
}

@Controller('test')
@UseGuards(RolesGuard)
export class TestController {
  @Get('open')
  open() {
    return 'open';
  }

  @Get('community-member')
  @Roles(Role.COMMUNITY_MEMBER)
  forCommunityMember() {
    return 'community_member';
  }

  @Get('grant-program-coordinator')
  @Roles(Role.COORDINATOR)
  forGrantProgramCoordinator() {
    return 'grant_program_coordinator';
  }
}

describe('RolesGuard', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TestController],
      providers: [
        { provide: APP_GUARD, useClass: MockAuthGuard },
        { provide: APP_GUARD, useClass: RolesGuard },
      ],
    }).compile();

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('GET /test/open should bypass RolesGuard when no role header is provided', () => {
    return request(app.getHttpServer()).get('/test/open').expect(HttpStatus.OK).expect('open');
  });

  it('GET /test/community-member should allow access when role=community_member (passes RolesGuard)', () => {
    return request(app.getHttpServer())
      .get('/test/community-member')
      .set('x-user-role', Role.COMMUNITY_MEMBER)
      .expect(HttpStatus.OK)
      .expect('community_member');
  });

  it('GET /test/community-member should forbid access when role=grant_program_coordinator (blocked by RolesGuard)', () => {
    return request(app.getHttpServer())
      .get('/test/community-member')
      .set('x-user-role', Role.COORDINATOR)
      .expect(HttpStatus.FORBIDDEN);
  });

  it('GET /test/grant-program-coordinator should allow access when role=grant_program_coordinator (passes RolesGuard)', () => {
    return request(app.getHttpServer())
      .get('/test/grant-program-coordinator')
      .set('x-user-role', Role.COORDINATOR)
      .expect(HttpStatus.OK)
      .expect('grant_program_coordinator');
  });

  it('GET /test/grant-program-coordinator should forbid access when role=community_member (blocked by RolesGuard)', () => {
    return request(app.getHttpServer())
      .get('/test/grant-program-coordinator')
      .set('x-user-role', Role.COMMUNITY_MEMBER)
      .expect(HttpStatus.FORBIDDEN);
  });
});
