import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import {
  VerifySignature,
  RequestChallenge,
  VerifyOtp,
  SignIn,
  SignUp,
  RecoverDeletedAccount,
  RecoverAccount,
  UpdateWallet,
  SignInResponse,
  RequestChallengeResponse,
  UserExists,
  AuthenticatedUserDto,
} from './dto';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { Role } from './role.enum';
import { Repository, DataSource, IsNull, Not } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { MailService } from '../notifications/mail/mail.service';
import { PublicKey } from '@hashgraph/sdk';
import { sanitizeInput } from '../utils/sanitize.util';
import { prefixMessageToSign } from '../utils/hedera.util';
import { UpdateDisplayNameDto } from './dto/update-display-name.dto';

import { DatabaseErrorCode } from '../database/database-error-code.enum';
import { SuccessResponse } from '../common/dto/success-response.dto';
import { LoggedInUser } from './decorators/logged-in-user.decorator';

export enum JwtScope {
  SESSION_TOKEN = 'SESSION_TOKEN',
  CHALLENGE_TOKEN = 'CHALLENGE_TOKEN',
  RECOVERY_TOKEN = 'RECOVERY_TOKEN',
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly hederaAccountsAPI: string;

  constructor(
    @InjectRepository(User)
    private readonly authRepository: Repository<User>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly mailService: MailService,
    private readonly dataSource: DataSource,
  ) {
    this.hederaAccountsAPI = `${this.configService.get<string>('MIRROR_NODE_URL')}/api/v1/accounts/`;
  }

  private generateChallenge(origin: string, uri: string, address: string) {
    return `${origin} wants you to sign in with your Hedera account:\n${address}\n\n URI: ${uri}\n\n Version: 1\n Nonce: ${this.generateSiweNonce()}\nIssued At: ${new Date().toISOString()}`;
  }

  private async getExistingUserByAddress(address: string): Promise<User> {
    const user = await this.authRepository
      .createQueryBuilder('user')
      .where(':address = ANY(user.addresses)', { address })
      .withDeleted()
      .getOne();

    return user;
  }

  /**
   * Generates random EIP-4361 nonce.
   * @see https://eips.ethereum.org/EIPS/eip-4361
   */
  private generateSiweNonce() {
    const length = 96;
    const size = 256;
    let index = size;
    let buffer: string;
    if (!buffer || index + length > size * 2) {
      buffer = '';
      index = 0;
      for (let i = 0; i < size; i++) {
        buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1);
      }
    }
    return buffer.substring(index, index++ + length);
  }

  private isValidHederaAddress(address: string): boolean {
    return /^\d+\.\d+\.\d+$/.test(address);
  }

  async requestChallenge(requestChallenge: RequestChallenge, refererURI: string): Promise<RequestChallengeResponse> {
    if (!this.isValidHederaAddress(requestChallenge.address)) {
      throw new BadRequestException('Invalid wallet address.');
    }

    const frontendBaseUrl = this.configService.getOrThrow<string>('FRONTEND_BASE_URL');
    const domain = frontendBaseUrl.split('://')[1];
    const challenge = this.generateChallenge(domain, refererURI, requestChallenge.address);
    const existingUser = await this.getExistingUserByAddress(requestChallenge.address);
    const token = await this.jwtService.signAsync(
      { challenge, address: requestChallenge.address, scope: JwtScope.CHALLENGE_TOKEN },
      { expiresIn: this.configService.getOrThrow<string>('CHALLENGE_TOKEN_EXPIRATION_TIME') },
    );

    return {
      success: true,
      challenge,
      challengeToken: token,
      accountExists: !!(existingUser && !existingUser.deletedAt),
      accountDeleted: !!(existingUser && existingUser.deletedAt),
    };
  }

  public async validateWallet(challengeToken: string, address: string, signature: string) {
    let decodedToken;
    try {
      decodedToken = await this.jwtService.verifyAsync(challengeToken);
    } catch (err) {
      this.logger.warn(
        `called verify-signature with unauthorized challenge token and address ${address}\n${err.message}`,
      );
      throw new UnauthorizedException('Unauthorized challenge.');
    }

    if (decodedToken.scope !== JwtScope.CHALLENGE_TOKEN) {
      throw new BadRequestException('Invalid token scope.');
    }

    if (address !== decodedToken.address) {
      throw new BadRequestException('Address in the token does not match the address in the request.');
    }

    const pubKey = await this.fetchPublicKeyForAddress(address);
    if (!this.checkSig(pubKey, signature, decodedToken.challenge)) {
      throw new UnauthorizedException('Invalid signature.');
    }
  }

  public transformUser(user: User): AuthenticatedUserDto {
    return {
      id: user.id,
      addresses: user.addresses,
      email: user.email,
      phoneNumber: user.phoneNumber,
      emailVerified: user.emailVerified,
      isPhoneVerified: user.isPhoneVerified,
      displayName: user.displayName,
      role: user.role,
      createdAt: user.createdAt,
    };
  }

  async signUp(signUp: SignUp): Promise<SuccessResponse> {
    let decodedToken;

    try {
      decodedToken = await this.jwtService.verifyAsync(signUp.challengeToken);
    } catch (err) {
      this.logger.warn(
        `called verify-signature with unauthorized challenge token and address ${signUp.address}\n${err.message}`,
      );
      throw new UnauthorizedException('Unauthorized challenge.');
    }

    if (decodedToken.scope !== JwtScope.CHALLENGE_TOKEN) {
      throw new BadRequestException('Invalid token scope.');
    }

    if (signUp.address !== decodedToken.address) {
      throw new BadRequestException('Address in the token does not match the address in the request.');
    }

    // Check for existing user (including soft-deleted) by address
    const existingUserByAddress = await this.getExistingUserByAddress(signUp.address);
    if (existingUserByAddress) {
      if (existingUserByAddress.deletedAt) {
        return {
          success: false,
          message: 'Account is deleted and can be recovered.',
        };
      } else {
        throw new ConflictException('User already exists with given wallet address.');
      }
    }

    // Check for existing user (including soft-deleted) by email
    const existingUserByEmail = await this.authRepository
      .createQueryBuilder('user')
      .where('user.email = :email', { email: signUp.email })
      .withDeleted() // Include soft-deleted users
      .getOne();

    if (existingUserByEmail) {
      if (existingUserByEmail.deletedAt) {
        return {
          success: false,
          message: 'Account is deleted and can be recovered.',
        };
      } else {
        throw new HttpException('User already exists with given email.', HttpStatus.CONFLICT);
      }
    }

    const pubKey = await this.fetchPublicKeyForAddress(signUp.address);
    if (!this.checkSig(pubKey, signUp.signature, decodedToken.challenge)) {
      throw new HttpException('Invalid signature.', HttpStatus.UNAUTHORIZED);
    }

    try {
      const user = this.authRepository.create({
        addresses: [signUp.address],
        email: signUp.email,
        displayName: sanitizeInput(signUp.displayName),
      });

      await this.authRepository.save(user);

      return {
        success: true,
        message: 'User created successfully.',
      };
    } catch (err) {
      if (err.code === DatabaseErrorCode.UNIQUE_CONSTRAINT_VIOLATION) {
        throw new HttpException('User already exists.', HttpStatus.CONFLICT);
      }
      throw err;
    }
  }

  async recoverDeletedAccount(account: RecoverDeletedAccount): Promise<SuccessResponse> {
    let decodedToken;
    try {
      decodedToken = await this.jwtService.verifyAsync(account.challengeToken);
    } catch (err) {
      this.logger.warn(
        `called verify-signature for recoverAccount with unauthorized challenge token and address ${account.address}\n${err.message}`,
      );
      throw new UnauthorizedException('Unauthorized challenge.');
    }

    if (decodedToken.scope !== JwtScope.CHALLENGE_TOKEN) {
      throw new BadRequestException('Invalid token scope.');
    }

    if (account.address !== decodedToken.address) {
      throw new BadRequestException('Address in the token does not match the address in the request.');
    }

    // Find soft-deleted user by address
    const existingUser = await this.getExistingUserByAddress(account.address);
    if (!existingUser || !existingUser.deletedAt) {
      throw new NotFoundException('No deleted account found for given wallet address.');
    }

    const pubKey = await this.fetchPublicKeyForAddress(account.address);

    if (!this.checkSig(pubKey, account.signature, decodedToken.challenge)) {
      throw new UnauthorizedException('Invalid signature.');
    }

    existingUser.deletedAt = null;
    existingUser.reactivatedAt = new Date();
    existingUser.emailVerified = false;

    await this.authRepository.save(existingUser);

    return {
      success: true,
      message: 'Account recovered successfully.',
    };
  }

  async signIn(signIn: SignIn): Promise<SignInResponse> {
    let decodedToken;
    try {
      decodedToken = await this.jwtService.verifyAsync(signIn.challengeToken);
    } catch (err) {
      this.logger.warn(`decoding token failed: ${err.message}`);
      throw new UnauthorizedException('Unauthorized challenge.');
    }

    if (decodedToken.scope !== JwtScope.CHALLENGE_TOKEN) {
      throw new BadRequestException('Invalid token scope.');
    }

    if (signIn.address !== decodedToken.address) {
      throw new BadRequestException('Address in the token does not match the address in the request.');
    }

    const existingUser = await this.getExistingUserByAddress(signIn.address);
    if (!existingUser) {
      throw new NotFoundException('User not found.');
    }

    if (existingUser.deletedAt) {
      throw new ForbiddenException('Account is deleted and can be recovered.');
    }

    const pubKey = await this.fetchPublicKeyForAddress(signIn.address);
    if (!this.checkSig(pubKey, signIn.signature, decodedToken.challenge)) {
      throw new UnauthorizedException('Invalid signature.');
    }

    return {
      success: true,
      sessionToken: this.generateSessionToken(existingUser),
      user: {
        id: existingUser.id,
        addresses: existingUser.addresses,
        email: existingUser.email,
        phoneNumber: existingUser.phoneNumber,
        emailVerified: existingUser.emailVerified,
        isPhoneVerified: existingUser.isPhoneVerified,
        displayName: existingUser.displayName,
        role: existingUser.role,
        createdAt: existingUser.createdAt,
      },
    };
  }

  generateSessionToken(user: User): string {
    return this.jwtService.sign({
      payload: {
        id: user.id,
        role: user.role,
        scope: JwtScope.SESSION_TOKEN,
        emailVerified: user.emailVerified,
      },
    });
  }

  // Expects signature in hexadecimal format with '0x' prefix
  private checkSig(pubKey: PublicKey, signature: string, message: string) {
    const signatureBytes = Buffer.from(signature.substring(2), 'hex');
    const prefixedMessageBytes = Buffer.from(prefixMessageToSign(message));
    try {
      return pubKey.verify(prefixedMessageBytes, signatureBytes);
    } catch (error) {
      this.logger.error(error.message);
      throw new HttpException('Signature verification failed.', HttpStatus.BAD_REQUEST);
    }
  }

  public async fetchPublicKeyForAddress(address: string): Promise<PublicKey> {
    // Fetch account information without transactions
    const response = await fetch(`${this.hederaAccountsAPI}${address}?transactions=false`);
    const data = await response.json();
    if (data.error) {
      throw new HttpException('Failed fetching public key from mirror node.', HttpStatus.INTERNAL_SERVER_ERROR);
    }
    const keyWithType = data.key;
    return keyWithType._type == 'ED25519'
      ? PublicKey.fromStringED25519(keyWithType.key)
      : PublicKey.fromStringECDSA(keyWithType.key); // ECDSA_SECP256K1
  }

  async recoverAccount(recoverWallet: RecoverAccount) {
    const user = await this.authRepository.findOne({
      where: { email: recoverWallet.email },
    });

    if (!user) {
      throw new NotFoundException('User not found.');
    }

    const token = await this.jwtService.signAsync(
      { email: recoverWallet.email, scope: JwtScope.RECOVERY_TOKEN },
      { expiresIn: this.configService.get<string>('RECOVERY_TOKEN_EXPIRATION_TIME') },
    );
    const isEmailSent = await this.mailService.sendAccountRecoveryEmail(user, token);

    if (!isEmailSent) {
      throw new InternalServerErrorException('Failed to send recovery email to the user.');
    }

    return {
      success: true,
      message: 'Recovery email sent to the user.',
    };
  }

  // Checks for a valid recovery token, checks for a valid challenge token, verifies the signature, and updates the
  // wallet addresses of the corresponding user, by removing all the previous wallet addresses and adding the new one.
  async updateWalletAddress(updateWallet: UpdateWallet) {
    const existingUser = await this.getExistingUserByAddress(updateWallet.address);
    if (existingUser) {
      if (existingUser.deletedAt) {
        throw new HttpException('Account is deleted and can be recovered.', HttpStatus.FORBIDDEN);
      } else {
        throw new HttpException('Wallet address already exists.', HttpStatus.CONFLICT);
      }
    }

    let decodedToken;
    try {
      decodedToken = await this.jwtService.verifyAsync(updateWallet.recoveryToken);
    } catch (err) {
      this.logger.warn(`decoding token failed: ${err.message}`);
      throw new HttpException('Invalid recovery token.', HttpStatus.UNAUTHORIZED);
    }

    if (decodedToken.scope !== JwtScope.RECOVERY_TOKEN) {
      throw new HttpException('Invalid token scope.', HttpStatus.BAD_REQUEST);
    }

    if (!decodedToken.email) {
      throw new HttpException('Email not found in the token provided.', HttpStatus.NOT_FOUND);
    }

    const user = await this.authRepository.findOne({
      where: { email: decodedToken.email },
    });

    if (!user) {
      throw new HttpException('User not found.', HttpStatus.NOT_FOUND);
    }

    try {
      decodedToken = await this.jwtService.verifyAsync(updateWallet.challengeToken);
    } catch (err) {
      this.logger.warn(`verifying challenge token failed: ${err.message}`);
      throw new HttpException('Unauthorized challenge.', HttpStatus.UNAUTHORIZED);
    }

    if (!decodedToken.challenge) {
      throw new HttpException('Challenge not found in the token provided.', HttpStatus.NOT_FOUND);
    }

    const pubKey = await this.fetchPublicKeyForAddress(updateWallet.address);

    if (!this.checkSig(pubKey, updateWallet.signature, decodedToken.challenge)) {
      throw new HttpException('Invalid signature.', HttpStatus.UNAUTHORIZED);
    }

    // Update the wallet address of the user
    user.addresses = [updateWallet.address];
    await this.authRepository.save(user);

    return {
      success: true,
      message: 'Wallet address updated successfully.',
    };
  }

  async sendOTP(loggedInUser: LoggedInUser): Promise<SuccessResponse> {
    const user = await this.authRepository.findOne({
      where: { id: loggedInUser.id },
    });

    if (!user) {
      throw new NotFoundException('User not found.');
    }

    if (user.emailVerified) {
      throw new BadRequestException('User email already verified.');
    }

    const otp = Math.floor(100000 + Math.random() * 900000);
    user.otp = otp.toString();
    user.otpExpiresAt = new Date(Date.now() + 5 * 60 * 1000);

    await this.authRepository.save(user);

    const isEmailSent = await this.mailService.sendEmailVerificationEmail(user, otp.toString());

    if (!isEmailSent) {
      throw new InternalServerErrorException('Failed to send OTP to the user.');
    }

    return {
      success: true,
      message: 'OTP sent to the user.',
    };
  }

  async verifyOTP(verifyOtp: VerifyOtp, loggedInUser: LoggedInUser) {
    const user = await this.authRepository.findOne({
      where: { id: loggedInUser.id },
    });

    if (!user) {
      throw new HttpException('User not found.', HttpStatus.NOT_FOUND);
    }

    if (user.otpExpiresAt < new Date()) {
      throw new HttpException('OTP expired.', HttpStatus.UNAUTHORIZED);
    }

    if (user.otp !== verifyOtp.otp) {
      throw new HttpException('Invalid OTP.', HttpStatus.UNAUTHORIZED);
    }

    user.emailVerified = true;
    user.otp = null;
    user.otpExpiresAt = null;

    await this.authRepository.save(user);
  }

  async userDetails(loggedInUser: LoggedInUser) {
    const user = await this.authRepository.findOne({
      where: { id: loggedInUser.id },
      select: {
        id: true,
        addresses: true,
        email: true,
        emailVerified: true,
        displayName: true,
        createdAt: true,
        role: true,
        phoneNumber: true,
        isPhoneVerified: true,
      },
    });

    if (!user) {
      throw new HttpException('User not found.', HttpStatus.NOT_FOUND);
    }

    return {
      success: true,
      user,
    };
  }

  // Services to add another wallet to the user's account.
  async addWalletToAccount(verifySignature: VerifySignature, loggedInUser: LoggedInUser) {
    const existingUser = await this.getExistingUserByAddress(verifySignature.address);
    if (existingUser) {
      if (existingUser.deletedAt) {
        throw new HttpException(
          'Account is deleted and cannot be modified. Please recover your account first.',
          HttpStatus.FORBIDDEN,
        );
      } else {
        throw new HttpException('Wallet address already exists.', HttpStatus.CONFLICT);
      }
    }

    let decodedToken;
    try {
      decodedToken = await this.jwtService.verifyAsync(verifySignature.challengeToken);
    } catch (err) {
      this.logger.warn(`verifying token failed: ${err.message}`);
      throw new HttpException('Unauthorized challenge.', HttpStatus.UNAUTHORIZED);
    }

    if (!decodedToken.challenge) {
      throw new HttpException('Challenge not found in the token provided.', HttpStatus.NOT_FOUND);
    }

    if (decodedToken.scope !== JwtScope.CHALLENGE_TOKEN) {
      throw new HttpException('Invalid token scope.', HttpStatus.BAD_REQUEST);
    }

    const pubKey = await this.fetchPublicKeyForAddress(verifySignature.address);
    if (!this.checkSig(pubKey, verifySignature.signature, decodedToken.challenge)) {
      throw new HttpException('Invalid signature.', HttpStatus.UNAUTHORIZED);
    }

    const user = await this.authRepository.findOne({
      where: {
        id: loggedInUser.id,
      },
      withDeleted: true,
    });

    if (!user) {
      throw new NotFoundException('User not found.');
    }

    if (user.deletedAt) {
      throw new ForbiddenException('Account is deleted and cannot be modified. Please recover your account first.');
    }

    user.addresses = [verifySignature.address, ...user.addresses];

    await this.authRepository.save(user);

    return {
      success: true,
      message: 'Wallet successfully added to the account.',
    };
  }

  async userExists(userExists: UserExists) {
    const email = Buffer.from(userExists.email, 'base64').toString('utf-8');
    const user = await this.authRepository.findOne({
      select: { id: true },
      where: { email },
    });

    return {
      success: true,
      exists: user == null ? false : true,
    };
  }

  getTotalKycVerifiedUsers(): Promise<number> {
    return this.authRepository.count({
      where: {
        phoneNumber: Not(IsNull()),
        isPhoneVerified: true,
      },
    });
  }

  /**
   * Updates the display name of the user.
   * @param updateDisplayNameDto - DTO containing the new display name.
   * @param requestUser - The HTTP request object containing the authorization header.
   * @returns An object indicating the success of the operation.
   */
  async updateDisplayName(updateDisplayNameDto: UpdateDisplayNameDto, loggedInUser: LoggedInUser) {
    // Find the user by the ID from the decoded token
    const user = await this.authRepository.findOne({
      where: { id: loggedInUser.id },
      withDeleted: true,
    });

    if (!user) {
      throw new NotFoundException('User not found.');
    }

    if (user.deletedAt) {
      throw new ForbiddenException('Account is deleted and cannot be updated.');
    }

    user.displayName = updateDisplayNameDto.displayName;
    await this.authRepository.save(user);

    return {
      success: true,
      message: 'Display name updated successfully.',
    };
  }

  async deleteAccount(loggedInUser: LoggedInUser): Promise<{ success: boolean; message: string }> {
    const user = await this.authRepository.findOne({
      where: { id: loggedInUser.id },
    });

    if (!user) {
      throw new NotFoundException('User not found.');
    }

    if (user.role === Role.COORDINATOR) {
      throw new ForbiddenException('Grant program coordinators cannot delete their accounts.');
    }

    await this.handleSoftDelete(user);

    return {
      success: true,
      message: 'Account deleted successfully.',
    };
  }

  async verifyAndExtractSignedInUser(authToken: string) {
    try {
      const decodedData = await this.jwtService.verifyAsync(authToken);

      if (decodedData.payload.scope !== JwtScope.SESSION_TOKEN) {
        return null;
      }

      return decodedData.payload;
    } catch (err) {
      this.logger.warn(`verifying token failed: ${err.message}`);
      return null;
    }
  }

  private async handleSoftDelete(user: User): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Perform soft delete on user entity
      await queryRunner.manager.softRemove(User, { id: user.id });

      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }
}
