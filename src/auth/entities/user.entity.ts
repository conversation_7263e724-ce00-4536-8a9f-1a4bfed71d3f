import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { BadRequestException } from '@nestjs/common';
import { UserNotificationPreferences } from '../../user/notification-preference/entities/user-notification-preferences.entity';
import { Role } from '../role.enum';

@Index('UQ_verified_phone_number', ['phoneNumber'], {
  unique: true,
  where: '"isPhoneVerified" IS TRUE',
})
@Entity()
export class User {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ unique: true })
  email: string;

  @Column({
    array: true,
    nullable: false,
    type: 'text',
  })
  addresses: string[];

  @Column()
  displayName: string;

  @Column({ default: false })
  emailVerified: boolean;

  @Column({ nullable: true })
  phoneNumber: string;

  @Column({ default: false })
  isPhoneVerified: boolean;

  @Column({ type: 'bigint', nullable: true })
  otp: string;

  @Column({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP', nullable: true })
  otpExpiresAt: Date;

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @Column({ type: 'enum', enum: Role, default: Role.COMMUNITY_MEMBER })
  role: Role;

  @DeleteDateColumn({ type: 'timestamptz', nullable: true })
  deletedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  reactivatedAt?: Date;

  @Column({ type: 'jsonb', nullable: true, default: {} })
  linkedAccounts?: Record<string, string>;

  @OneToMany(() => UserNotificationPreferences, (preference) => preference.user, { cascade: ['insert', 'update'] })
  notificationPreferences: UserNotificationPreferences[];

  @BeforeUpdate()
  @BeforeInsert()
  ensureUniqueAddresses() {
    const uniqueAddresses = Array.from(new Set(this.addresses));
    if (uniqueAddresses.length !== this.addresses.length) {
      throw new BadRequestException('Duplicate addresses are not allowed');
    }
    this.addresses = uniqueAddresses;
  }
}
