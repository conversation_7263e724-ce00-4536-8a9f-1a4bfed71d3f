import { ApiProperty } from '@nestjs/swagger';
import { Role } from '../role.enum';
import { UserDto } from './user.dto';
import { IsNotEmpty } from 'class-validator';

/**
 * The DTO used for returning information about an authenticated user.
 */
export class AuthenticatedUserDto extends UserDto {
  @ApiProperty({
    example: ['0.0.4501344', '0.0.4501345'],
    description: "The array of wallet addresses linked to the user's account.",
  })
  @IsNotEmpty()
  addresses: string[];

  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email address of the user.',
  })
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    example: true,
    description: 'The value set true if the user verified their email address',
  })
  @IsNotEmpty()
  emailVerified: boolean;

  @ApiProperty({
    example: '2021-07-01T00:00:00.000Z',
    description: 'The timestamp when the user registered',
  })
  @IsNotEmpty()
  createdAt: Date;

  @ApiProperty({
    example: Role.COMMUNITY_MEMBER,
    description: 'The role of the current user',
    enum: Role,
    enumName: 'Role',
  })
  @IsNotEmpty()
  role: Role;

  @ApiProperty({ example: false, description: 'Indicates if the phone number is verified' })
  @IsNotEmpty()
  isPhoneVerified: boolean;

  @ApiProperty({
    description: 'The phone number that was added.',
    example: '+***********',
    nullable: false, // Assuming phoneNumber is always returned in the response
  })
  @IsNotEmpty()
  phoneNumber: string;
}

export class FetchUserResponse {
  @ApiProperty({
    example: true,
    description: 'Indicates if the request was successful',
  })
  @IsNotEmpty()
  success: boolean;

  @ApiProperty({
    type: AuthenticatedUserDto,
    description: 'The user information',
  })
  @IsNotEmpty()
  user: AuthenticatedUserDto;
}
