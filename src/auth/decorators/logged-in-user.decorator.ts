import { User } from '../entities/user.entity';

import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export type LoggedInUser = Pick<
  User,
  'id' | 'email' | 'emailVerified' | 'phoneNumber' | 'isPhoneVerified' | 'role' | 'otp' | 'otpExpiresAt'
>;

export const LoggedInUser = createParamDecorator((_: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  return request.user;
});
