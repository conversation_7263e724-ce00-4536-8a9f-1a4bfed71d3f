import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { plainToInstance } from 'class-transformer';
import { RESPONSE_BY_ROLE_KEY } from '../decorators/response-by-role.decorator';

@Injectable()
export class ResponseByRoleInterceptor implements NestInterceptor {
  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler<any>): Observable<any> {
    // Find which DTO was set via @ResponseByRole()
    const dto = this.reflector.get<new (...args: any[]) => any>(RESPONSE_BY_ROLE_KEY, context.getHandler());
    if (!dto) {
      return next.handle();
    }

    const req = context.switchToHttp().getRequest();
    const role: string | undefined = req.user?.role;

    return next.handle().pipe(
      map((data) =>
        plainToInstance(dto, data, {
          groups: role ? [role] : [],
        }),
      ),
    );
  }
}
