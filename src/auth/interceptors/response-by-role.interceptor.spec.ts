import {
  CanActivate,
  Controller,
  ExecutionContext,
  Get,
  INestApplication,
  Injectable,
  UseGuards,
} from '@nestjs/common';
import { ExposeForRoles, ResponseByRole } from '../decorators/response-by-role.decorator';
import { Role } from '../role.enum';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { ResponseByRoleInterceptor } from './response-by-role.interceptor';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';

// Mock guard to inject user role from header
@Injectable()
class MockAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const req = context.switchToHttp().getRequest();
    req.user = {
      role: req.headers['x-user-role'],
    };
    return true;
  }
}

class TestDto {
  fieldForPublic: string;

  @ExposeForRoles(Role.COMMUNITY_MEMBER)
  fieldForCommunityMember: string;

  @ExposeForRoles(Role.COORDINATOR)
  fieldForCoordinator: string;

  @ExposeForRoles(Role.COORDINATOR, Role.COMMUNITY_MEMBER)
  fieldForCoordinatorAndCommunityMember: string;
}

@Controller('test')
class TestController {
  @Get()
  @UseGuards(MockAuthGuard)
  @ResponseByRole(TestDto)
  find(): TestDto {
    return {
      fieldForPublic: 'dummy-value',
      fieldForCommunityMember: 'dummy-value',
      fieldForCoordinator: 'dummy-value',
      fieldForCoordinatorAndCommunityMember: 'dummy-value',
    };
  }
}

describe('ResponseByRoleInterceptor', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TestController],
      providers: [
        { provide: APP_GUARD, useClass: MockAuthGuard },
        { provide: APP_INTERCEPTOR, useClass: ResponseByRoleInterceptor },
      ],
    }).compile();

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('GRANT_PROGRAM_COORDINATOR only sees coordinator + public fields', async () => {
    const res = await request(app.getHttpServer()).get('/test').set('x-user-role', Role.COORDINATOR).expect(200);

    expect(Object.keys(res.body)).toEqual([
      'fieldForPublic',
      'fieldForCoordinator',
      'fieldForCoordinatorAndCommunityMember',
    ]);
    expect(res.body.fieldForCommunityMember).toBeUndefined();
  });

  it('COMMUNITY_MEMBER only sees community + public fields', async () => {
    const res = await request(app.getHttpServer()).get('/test').set('x-user-role', Role.COMMUNITY_MEMBER).expect(200);

    expect(Object.keys(res.body)).toEqual([
      'fieldForPublic',
      'fieldForCommunityMember',
      'fieldForCoordinatorAndCommunityMember',
    ]);
    expect(res.body.fieldForGrantCoordinator).toBeUndefined();
  });

  it('no role (unauthenticated) returns everything unfiltered', async () => {
    const res = await request(app.getHttpServer()).get('/test').expect(200);

    expect(Object.keys(res.body)).toEqual(['fieldForPublic']);
    expect(res.body.fieldForCoordinator).toBeUndefined();
  });
});
