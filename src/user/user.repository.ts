import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from '../auth/entities/user.entity';
import { Repository, UpdateResult } from 'typeorm';

@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>,
  ) {}

  findOneById(id: number): Promise<User> {
    return this.repository.findOne({
      where: {
        id,
      },
    });
  }

  findOneByVerifiedPhoneNumber(phoneNumber: string): Promise<User> {
    return this.repository.findOne({ where: { phoneNumber, isPhoneVerified: true } });
  }

  updateById(id: number, fieldsToUpdate: Partial<User>): Promise<UpdateResult> {
    return this.repository.update(id, fieldsToUpdate);
  }
}
