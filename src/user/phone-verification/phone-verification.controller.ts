import { Controller, Post, Body, Get, Delete, HttpCode, HttpStatus } from '@nestjs/common';

import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiNoContentResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { PhoneVerificationService } from './phone-verification.service';
import { LoggedInUser } from '../../auth/decorators/logged-in-user.decorator';
import { SendCodeRequestDto } from './dto/send-code-request.dto';
import { VerifyCodeRequestDto } from './dto/verify-code-request.dto';

@ApiTags('User')
@Controller('/user/phone-verification')
export class PhoneVerificationController {
  constructor(private readonly phoneVerificationService: PhoneVerificationService) {}

  @Post('send-code')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Associate a new phone number and initiate OTP delivery' })
  @ApiNoContentResponse({ description: 'Successfully sent an OTP to the provided phone number' })
  @ApiBadRequestResponse({
    description: 'Invalid phone number format or user input, Account is deleted and cannot be modified',
  })
  @ApiConflictResponse({ description: 'This phone number is already associated with another account' })
  async sendVerificationCode(@LoggedInUser() loggedInUser: LoggedInUser, @Body() dto: SendCodeRequestDto) {
    await this.phoneVerificationService.sendVerificationCode(loggedInUser, dto);
  }

  @Get('resend-code')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Reissue an OTP to the currently registered phone number' })
  @ApiNoContentResponse({ description: 'A fresh OTP has been successfully sent to the existing phone number' })
  @ApiBadRequestResponse({
    description: 'Cannot resend OTP: no phone number on record, already verified, or resend attempted too soon.',
  })
  async resendVerificationCode(@LoggedInUser() loggedInUser: LoggedInUser) {
    await this.phoneVerificationService.resendVerificationCode(loggedInUser);
  }

  @Post('verify-code')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Validate the OTP and confirm phone number ownership' })
  @ApiNoContentResponse({
    description: 'Phone number verified successfully, marking it as confirmed on the user account',
  })
  @ApiBadRequestResponse({ description: 'OTP is invalid, expired, or does not match; please retry with a valid code' })
  async verifyPhoneNumber(
    @LoggedInUser() loggedInUser: LoggedInUser,
    @Body() verifyPhoneNumberDto: VerifyCodeRequestDto,
  ) {
    await this.phoneVerificationService.verifyPhoneNumber(loggedInUser, verifyPhoneNumberDto.otp);
  }

  @Delete()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Remove the phone number' })
  @ApiNoContentResponse({ description: 'Phone number removed successfully' })
  async deletePhoneNumber(@LoggedInUser() loggedInUser: LoggedInUser) {
    await this.phoneVerificationService.deletePhoneNumber(loggedInUser.id);
  }
}
