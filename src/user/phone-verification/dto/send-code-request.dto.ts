import { IsString, Matches, IsEnum, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CountryCode, getCountries } from 'libphonenumber-js';
import { IsPhoneNumber } from '../validators/is-phone-number.validator';

export class SendCodeRequestDto {
  @ApiProperty({ description: 'Phone number (digits only)', example: '2512630796' })
  @IsNotEmpty()
  @IsString()
  @Matches(/^\d{1,15}$/, { message: 'phoneNumber must contain only digits (1 to 15 digits)' })
  @IsPhoneNumber('countryCode')
  phoneNumber: string;

  @ApiProperty({
    description: 'Country code (ISO 3166-1 alpha-2)',
    example: 'US',
  })
  @IsString()
  @IsEnum(Object.values(getCountries()).filter((v) => typeof v === 'string'), {
    message: `countryCode must be one of the following values: ${getCountries().join(', ')}`,
  })
  countryCode: CountryCode;
}
