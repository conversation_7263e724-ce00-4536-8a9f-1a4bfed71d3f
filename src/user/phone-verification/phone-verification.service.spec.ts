import { Test, TestingModule } from '@nestjs/testing';
import { PhoneVerificationService } from './phone-verification.service';
import { UserRepository } from '../user.repository';
import { LoggedInUser } from '../../auth/decorators/logged-in-user.decorator';
import { Role } from '../../auth/role.enum';
import { ConflictException, InternalServerErrorException } from '@nestjs/common';
import { SnsService } from '../../notifications/channels/sns/sns.service';
import { ConfigService } from '@nestjs/config';

describe('PhoneVerificationService', () => {
  let service: PhoneVerificationService;
  const mockUserRepository = {
    findOneByVerifiedPhoneNumber: jest.fn(),
    updateById: jest.fn().mockResolvedValue({ affected: 1 }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PhoneVerificationService,
        ConfigService,
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
        {
          provide: SnsService,
          useValue: {
            publishSMS: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<PhoneVerificationService>(PhoneVerificationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendVerificationCode', () => {
    const mockLoggedInUser: LoggedInUser = {
      id: 1,
      email: '<EMAIL>',
      emailVerified: true,
      role: Role.COMMUNITY_MEMBER,
      isPhoneVerified: false,
      phoneNumber: null,
      otp: null,
      otpExpiresAt: null,
    };
    const dto = { phoneNumber: '**********', countryCode: 'US' } as any;

    it('throws ConflictException if another user already has this number', async () => {
      mockUserRepository.findOneByVerifiedPhoneNumber.mockResolvedValueOnce({ id: 2 });

      await expect(service.sendVerificationCode(mockLoggedInUser, dto)).rejects.toBeInstanceOf(ConflictException);
    });

    it('enforces wait period when OTP still valid on same number', async () => {
      const loggedInUser = {
        ...mockLoggedInUser,
        phoneNumber: '+1**********',
        otp: '000000',
        otpExpiresAt: new Date(Date.now() + 5 * 60 * 1000),
      };

      await expect(service.sendVerificationCode(loggedInUser, dto)).rejects.toThrow(/still valid/);
    });

    it('rejects when same number already verified', async () => {
      const loggedInUser = {
        ...mockLoggedInUser,
        phoneNumber: '+1**********',
        isPhoneVerified: true,
      };
      mockUserRepository.findOneByVerifiedPhoneNumber.mockResolvedValue(null);

      await expect(service.sendVerificationCode(loggedInUser, dto)).rejects.toThrow('already added and verified');
    });

    it('throws InternalServerErrorException if update fails', async () => {
      mockUserRepository.findOneByVerifiedPhoneNumber.mockResolvedValue(null);
      mockUserRepository.updateById.mockResolvedValue({ affected: 0 } as any);

      await expect(service.sendVerificationCode(mockLoggedInUser, dto)).rejects.toBeInstanceOf(
        InternalServerErrorException,
      );
    });
  });

  describe('resendVerificationCode', () => {
    const mockLoggedInUser: LoggedInUser = {
      id: 1,
      email: '<EMAIL>',
      emailVerified: true,
      role: Role.COMMUNITY_MEMBER,
      isPhoneVerified: false,
      phoneNumber: '1234567890',
      otp: null,
      otpExpiresAt: null,
    };

    it('throws if no phone number', async () => {
      await expect(service.resendVerificationCode({ ...mockLoggedInUser, phoneNumber: null })).rejects.toThrow(
        'Phone number not found',
      );
    });

    it('throws if already verified', async () => {
      await expect(service.resendVerificationCode({ ...mockLoggedInUser, isPhoneVerified: true })).rejects.toThrow(
        'Phone number already verified',
      );
    });

    it('enforces wait period when OTP still valid', async () => {
      const loggedInUser = { ...mockLoggedInUser, otp: '000', otpExpiresAt: new Date(Date.now() + 5 * 60 * 1000) };
      await expect(service.resendVerificationCode(loggedInUser)).rejects.toThrow(/still valid/);
    });

    it('throws InternalServerErrorException if update fails', async () => {
      mockUserRepository.updateById.mockResolvedValue({ affected: 0 } as any);
      await expect(service.resendVerificationCode(mockLoggedInUser)).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('verifyPhoneNumber', () => {
    const mockLoggedInUser: LoggedInUser = {
      id: 1,
      email: '<EMAIL>',
      emailVerified: true,
      role: Role.COMMUNITY_MEMBER,
      isPhoneVerified: false,
      phoneNumber: '1234567890',
      otp: '123456',
      otpExpiresAt: null,
    };

    it('throws if no phone number', async () => {
      await expect(
        service.verifyPhoneNumber({ ...mockLoggedInUser, phoneNumber: null }, mockLoggedInUser.otp),
      ).rejects.toThrow('Phone number not found');
    });

    it('throws if already verified', async () => {
      await expect(
        service.verifyPhoneNumber({ ...mockLoggedInUser, isPhoneVerified: true }, mockLoggedInUser.otp),
      ).rejects.toThrow('already verified');
    });

    it('throws if OTP expired', async () => {
      await expect(
        service.verifyPhoneNumber(
          { ...mockLoggedInUser, otpExpiresAt: new Date(Date.now() - 1000) },
          mockLoggedInUser.otp,
        ),
      ).rejects.toThrow('Invalid OTP');
    });

    it('throws if OTP does not match', async () => {
      await expect(service.verifyPhoneNumber(mockLoggedInUser, '000000')).rejects.toThrow('Invalid OTP');
    });

    it('throws if update fails', async () => {
      mockUserRepository.updateById.mockResolvedValue({ affected: 0 } as any);
      await expect(
        service.verifyPhoneNumber(
          { ...mockLoggedInUser, otpExpiresAt: new Date(Date.now() + 1000) },
          mockLoggedInUser.otp,
        ),
      ).rejects.toBeInstanceOf(InternalServerErrorException);
    });
  });

  describe('deletePhoneNumber', () => {
    it('throws if update fails', async () => {
      mockUserRepository.updateById.mockResolvedValue({ affected: 0 } as any);
      await expect(service.deletePhoneNumber(1)).rejects.toBeInstanceOf(InternalServerErrorException);
    });
  });
});
