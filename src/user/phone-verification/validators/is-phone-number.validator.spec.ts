import { validate } from 'class-validator';
import { IsPhoneNumber } from './is-phone-number.validator';
import { CountryCode } from 'libphonenumber-js';

class TestDto {
  @IsPhoneNumber('countryCode')
  phoneNumber: any;

  countryCode: CountryCode;
}

class TestDtoWithCustomMessage {
  @IsPhoneNumber('countryCode', { message: 'Custom error: invalid phone' })
  phoneNumber: string;

  countryCode: CountryCode;
}

describe('@IsPhoneNumber', () => {
  it('passes when given a valid E.164 string for the specified country', async () => {
    const dto = new TestDto();
    dto.countryCode = 'US';
    dto.phoneNumber = '2025550125'; // valid US number
    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });

  it('fails when the value is not a string', async () => {
    const dto = new TestDto();
    dto.countryCode = 'US';
    dto.phoneNumber = 2025550125; // non-string
    const errors = await validate(dto);
    expect(errors).toHaveLength(1);

    const constraints = errors[0].constraints!;
    const msg = Object.values(constraints)[0];
    expect(msg).toMatch(/phoneNumber must be a valid phone number for the country code in "countryCode"/);
  });

  it('fails when the string is not a valid number for that country', async () => {
    const dto = new TestDto();
    dto.countryCode = 'US';
    dto.phoneNumber = '7911123456'; // valid UK but countryCode is US
    const errors = await validate(dto);
    expect(errors).toHaveLength(1);

    const constraints = errors[0].constraints!;
    const msg = Object.values(constraints)[0];
    expect(msg).toMatch(/phoneNumber must be a valid phone number for the country code in "countryCode"/);
  });

  it('uses the custom message when provided via ValidationOptions', async () => {
    const dto = new TestDtoWithCustomMessage();
    dto.countryCode = 'US';
    dto.phoneNumber = 'abcdef';
    const errors = await validate(dto);
    expect(errors).toHaveLength(1);
    const msg = Object.values(errors[0].constraints!)[0];
    expect(msg).toBe('Custom error: invalid phone');
  });
});
