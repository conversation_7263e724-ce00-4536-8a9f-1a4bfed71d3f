import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { parsePhoneNumberWithError, isValidNumber, CountryCode } from 'libphonenumber-js';

@ValidatorConstraint({ async: false })
export class IsPhoneNumberConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments): boolean {
    if (typeof value !== 'string') return false;

    // we *guaranteed* at decorator time that args.constraints[0] is a field name
    const countryCodeField = args.constraints[0] as string;
    const countryCode = (args.object as any)[countryCodeField] as CountryCode;

    try {
      if (!isValidNumber(value, countryCode)) return false;
      parsePhoneNumberWithError(value, countryCode);
      return true;
    } catch {
      return false;
    }
  }

  defaultMessage(args: ValidationArguments): string {
    const countryCodeField = args.constraints[0] as string;
    return `$property must be a valid phone number for the country code in "${countryCodeField}"`;
  }
}

/**
 * Decorator: @IsPhoneNumber(countryCodeField, validationOptions)
 *
 * @param countryCodeField  — name of the DTO property that holds the CountryCode
 * @param validationOptions — standard class‑validator options
 *
 * @throws if you don’t pass a countryCodeField
 */
export function IsPhoneNumber(countryCodeField: string, validationOptions?: ValidationOptions) {
  if (!countryCodeField || typeof countryCodeField !== 'string') {
    throw new Error(
      `IsPhoneNumber decorator requires the name of the countryCode field, e.g. @IsPhoneNumber('countryCode')`,
    );
  }

  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [countryCodeField],
      validator: IsPhoneNumberConstraint,
    });
  };
}
