import { Modu<PERSON> } from '@nestjs/common';
import { PhoneVerificationService } from './phone-verification.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../../auth/entities/user.entity';
import { SnsModule } from '../../notifications/channels/sns/sns.module';
import { UserRepository } from '../user.repository';
import { PhoneVerificationController } from './phone-verification.controller';
import { ConfigService } from '@nestjs/config';

@Module({
  imports: [TypeOrmModule.forFeature([User]), SnsModule],
  controllers: [PhoneVerificationController],
  providers: [PhoneVerificationService, UserRepository, ConfigService],
})
export class PhoneVerificationModule {}
