import { Injectable, BadRequestException, InternalServerErrorException, ConflictException } from '@nestjs/common';
import { parsePhoneNumberWithError } from 'libphonenumber-js';
import { SnsService } from '../../notifications/channels/sns/sns.service';
import { UserRepository } from '../user.repository';
import { LoggedInUser } from '../../auth/decorators/logged-in-user.decorator';
import { generateOtp } from './utils/phone-verification.utils';
import { SendCodeRequestDto } from './dto/send-code-request.dto';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class PhoneVerificationService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly snsService: SnsService,
    private readonly configService: ConfigService,
  ) {}

  async sendVerificationCode(loggedInUser: LoggedInUser, dto: SendCodeRequestDto) {
    // normalize phone number
    const newPhoneNumber = parsePhoneNumberWithError(dto.phoneNumber, dto.countryCode).number;

    // ensure no other account has this number
    const existing = await this.userRepository.findOneByVerifiedPhoneNumber(newPhoneNumber);
    if (existing && existing.id !== loggedInUser.id) {
      throw new ConflictException('This phone number is already associated with another account');
    }

    // enforce wait period & prevent re-verification
    if (loggedInUser.phoneNumber === newPhoneNumber) {
      const now = new Date();
      if (loggedInUser.otp && loggedInUser.otpExpiresAt && loggedInUser.otpExpiresAt > now) {
        const minutesLeft = Math.ceil((loggedInUser.otpExpiresAt.getTime() - now.getTime()) / 60000);
        throw new BadRequestException(
          `An OTP for this number is still valid. Please use the existing code or wait ${minutesLeft} more minute(s) before requesting a new one`,
        );
      }

      if (loggedInUser.isPhoneVerified) {
        throw new BadRequestException('This phone number is already added and verified');
      }
    }

    const otp = generateOtp();

    const updateResult = await this.userRepository.updateById(loggedInUser.id, {
      phoneNumber: newPhoneNumber,
      isPhoneVerified: false,
      otp,
      otpExpiresAt: new Date(Date.now() + this.configService.get<number>('OTP_EXPIRY_TIME_IN_MINUTES') * 60 * 1000),
    });

    if (updateResult.affected !== 1) {
      throw new InternalServerErrorException('Failed to update');
    }

    await this.snsService.publishSMS(newPhoneNumber, `THA \nUse the code ${otp} to verify your phone number`);
  }

  async resendVerificationCode(loggedInUser: LoggedInUser) {
    if (!loggedInUser.phoneNumber) {
      throw new BadRequestException('Phone number not found. Please add phone number first');
    }

    if (loggedInUser.isPhoneVerified) {
      throw new BadRequestException('Phone number already verified');
    }

    // enforce wait period & prevent re-verification
    const now = new Date();
    if (loggedInUser.otp && loggedInUser.otpExpiresAt && loggedInUser.otpExpiresAt > now) {
      const minutesLeft = Math.ceil((loggedInUser.otpExpiresAt.getTime() - now.getTime()) / 60000);
      throw new BadRequestException(
        `An OTP for this number is still valid. Please use the existing code or wait ${minutesLeft} more minute(s) before requesting a new one`,
      );
    }

    const otp = generateOtp();

    const updateResult = await this.userRepository.updateById(loggedInUser.id, {
      otp,
      otpExpiresAt: new Date(Date.now() + this.configService.get<number>('OTP_EXPIRY_TIME_IN_MINUTES') * 60 * 1000),
    });

    if (updateResult.affected !== 1) {
      throw new InternalServerErrorException('Failed to update');
    }

    await this.snsService.publishSMS(loggedInUser.phoneNumber, `THA \nUse the code ${otp} to verify your phone number`);
  }

  async verifyPhoneNumber(loggedInUser: LoggedInUser, otp: string) {
    if (!loggedInUser.phoneNumber) {
      throw new BadRequestException('Phone number not found. Please add phone number first');
    }

    if (loggedInUser.isPhoneVerified) {
      throw new BadRequestException('Phone number already verified');
    }

    // ensure no other account has this number
    const existing = await this.userRepository.findOneByVerifiedPhoneNumber(loggedInUser.phoneNumber);
    if (existing && existing.id !== loggedInUser.id) {
      throw new ConflictException('This phone number has already been verified by another account');
    }

    if (
      !loggedInUser.otp ||
      !loggedInUser.otpExpiresAt ||
      loggedInUser.otpExpiresAt < new Date() ||
      loggedInUser.otp !== otp
    ) {
      throw new BadRequestException('Invalid OTP');
    }

    const updateResult = await this.userRepository.updateById(loggedInUser.id, {
      isPhoneVerified: true,
      otp: null,
      otpExpiresAt: null,
    });

    if (updateResult.affected !== 1) {
      throw new InternalServerErrorException('Failed to update');
    }
  }

  async deletePhoneNumber(userId: number) {
    const updateResult = await this.userRepository.updateById(userId, {
      phoneNumber: null,
      isPhoneVerified: false,
      otp: null,
      otpExpiresAt: null,
    });

    if (updateResult.affected !== 1) {
      throw new InternalServerErrorException('Failed to update');
    }
  }
}
