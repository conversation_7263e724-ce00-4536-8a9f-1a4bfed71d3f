import * as crypto from 'crypto';

/**
 * Generates a 6-digit OTP code using cryptographically secure random bytes.
 * - Creates 3 random bytes
 * - Converts them to a hexadecimal string
 * - Parses that hex as an integer
 * - Takes the first 6 digits and pads with leading zeros if necessary
 *
 * @returns {string} A 6-digit OTP string
 */
export function generateOtp(): string {
  const bytes = crypto.randomBytes(3);
  const hex = bytes.toString('hex');
  const num = parseInt(hex, 16).toString().slice(0, 6);
  return num.padStart(6, '0');
}
