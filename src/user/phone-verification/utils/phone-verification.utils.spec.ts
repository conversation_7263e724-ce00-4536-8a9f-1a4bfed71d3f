// Override crypto.randomBytes for predictable OTP values in tests
const mockRandomBytes = jest.fn().mockReturnValue(Buffer.from([0x00, 0x00, 0x01]));
jest.mock('crypto', () => {
  const actual = jest.requireActual('crypto');
  return {
    ...actual,
    randomBytes: mockRandomBytes,
  };
});

import { generateOtp } from './phone-verification.utils';

describe('phone-verification.utils', () => {
  describe('generateOtp', () => {
    it('should return a 6-digit numeric string padded with zeros', () => {
      const otp = generateOtp();
      expect(otp).toBe('000001');
      expect(otp).toHaveLength(6);
      expect(/^[0-9]{6}$/.test(otp)).toBe(true);
    });

    it('should handle maximum value without throwing', () => {
      // hex ffffff → decimal 16777215 → toString().slice(0,6) = '167772'
      mockRandomBytes.mockReturnValue(Buffer.from([0xff, 0xff, 0xff]));
      const otp = generateOtp();
      expect(otp).toHaveLength(6);
      expect(/^[0-9]{6}$/.test(otp)).toBe(true);
      expect(otp).toBe('167772');
    });
  });
});
