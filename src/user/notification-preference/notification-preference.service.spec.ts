import { Test, TestingModule } from '@nestjs/testing';
import { NotificationPreferenceService } from './notification-preference.service';
import { NotificationPreferenceRepository } from './notification-preference.repository';
import { NotificationType } from './entities/user-notification-preferences.entity';
import { InternalServerErrorException } from '@nestjs/common';

describe('NotificationPreferenceService', () => {
  let service: NotificationPreferenceService;
  const mockNotificationPreferencesRepository = {
    findAllByUser: jest.fn(),
    updateOrInsert: jest.fn(),
  };
  const mockNotificationPreferences = [
    {
      notificationType: NotificationType.GRANT_CALL_UPDATE,
      isEnabled: true,
    },
    {
      notificationType: NotificationType.GRANT_APPLICATION_UPDATE,
      isEnabled: false,
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationPreferenceService,
        {
          provide: NotificationPreferenceRepository,
          useValue: mockNotificationPreferencesRepository,
        },
      ],
    }).compile();

    service = module.get<NotificationPreferenceService>(NotificationPreferenceService);

    mockNotificationPreferencesRepository.findAllByUser.mockResolvedValue(mockNotificationPreferences);
    mockNotificationPreferencesRepository.updateOrInsert.mockResolvedValue({ identifiers: 1 });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findAll', () => {
    it('should findAll notification preference of user', async () => {
      const notificationPreferences = await service.find(1);
      expect(notificationPreferences).toEqual({
        [mockNotificationPreferences[0].notificationType]: mockNotificationPreferences[0].isEnabled,
        [mockNotificationPreferences[1].notificationType]: mockNotificationPreferences[1].isEnabled,
      });
    });
  });

  describe('update', () => {
    it('should update notification preference', async () => {
      const updateResult = await service.update(1, {
        notificationType: NotificationType.GRANT_CALL_UPDATE,
        isEnabled: true,
      });
      expect(updateResult.identifiers).toEqual(1);
    });

    it('should throw InternalServerErrorException when notification preference is not updated', async () => {
      mockNotificationPreferencesRepository.updateOrInsert.mockRejectedValueOnce(new Error());

      await expect(
        service.update(1, {
          notificationType: NotificationType.GRANT_CALL_UPDATE,
          isEnabled: true,
        }),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });
});
