import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationPreferenceController } from './notification-preference.controller';
import { NotificationPreferenceService } from './notification-preference.service';
import { NotificationPreferenceRepository } from './notification-preference.repository';
import { UserNotificationPreferences } from './entities/user-notification-preferences.entity';

@Module({
  imports: [TypeOrmModule.forFeature([UserNotificationPreferences])],
  controllers: [NotificationPreferenceController],
  providers: [NotificationPreferenceService, NotificationPreferenceRepository],
})
export class NotificationPreferenceModule {}
