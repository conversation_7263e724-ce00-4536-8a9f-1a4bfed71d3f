import { ApiProperty } from '@nestjs/swagger';
import { NotificationType } from '../entities/user-notification-preferences.entity';
import { IsBoolean, IsEnum } from 'class-validator';

export class UpdateNotificationPreferenceRequestDto {
  @ApiProperty({
    description: 'The notification type to update for the current user',
    enum: NotificationType,
    enumName: 'NotificationType',
    example: NotificationType.GRANT_CALL_UPDATE,
  })
  @IsEnum(NotificationType)
  notificationType: NotificationType;

  @ApiProperty({ description: 'Flag indicating whether this notification type should be enabled', example: true })
  @IsBoolean()
  isEnabled: boolean;
}
