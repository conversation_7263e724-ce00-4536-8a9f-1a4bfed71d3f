import { ApiProperty } from '@nestjs/swagger';
import { NotificationType } from '../entities/user-notification-preferences.entity';

export class FindNotificationPreferenceResponseDto {
  @ApiProperty({
    description: 'Whether the user receives grant call update emails',
    example: true,
  })
  [NotificationType.GRANT_CALL_UPDATE]: boolean;

  @ApiProperty({
    description: 'Whether the user receives grant application update emails',
    example: true,
  })
  [NotificationType.GRANT_APPLICATION_UPDATE]: boolean;
}
