import { Controller, Get, Body, HttpStatus, HttpCode, Patch } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { NotificationPreferenceService } from './notification-preference.service';
import { UpdateNotificationPreferenceRequestDto } from './dto/update-notification-preference-request.dto';
import { FindNotificationPreferenceResponseDto } from './dto/find-notification-preference-response.dto';
import { LoggedInUser } from '../../auth/decorators/logged-in-user.decorator';

@ApiTags('User')
@Controller('user/notification-preferences')
@ApiBearerAuth()
export class NotificationPreferenceController {
  constructor(private readonly notificationPreferenceService: NotificationPreferenceService) {}

  @Get()
  @ApiOperation({ summary: 'Get current user notification preferences' })
  @ApiOkResponse({
    description: 'Successfully retrieved notification preferences',
    type: FindNotificationPreferenceResponseDto,
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  async findPreferences(@LoggedInUser() loggedInUser: LoggedInUser): Promise<FindNotificationPreferenceResponseDto> {
    return await this.notificationPreferenceService.find(loggedInUser.id);
  }

  @Patch()
  @ApiOperation({ summary: 'Update notification preference of the user' })
  @ApiNoContentResponse({
    description: 'Notification preference updated',
  })
  @ApiBadRequestResponse({ description: 'Validation failed' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiBody({
    type: UpdateNotificationPreferenceRequestDto,
    description: 'Payload containing the notification preference',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  async updatePreference(
    @LoggedInUser() loggedInUser: LoggedInUser,
    @Body() updateNotificationPreferenceDto: UpdateNotificationPreferenceRequestDto,
  ) {
    await this.notificationPreferenceService.update(loggedInUser.id, updateNotificationPreferenceDto);
  }
}
