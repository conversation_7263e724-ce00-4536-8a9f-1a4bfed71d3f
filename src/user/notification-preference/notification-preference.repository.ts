import { Injectable } from '@nestjs/common';
import { InsertResult, Repository } from 'typeorm';
import { NotificationType, UserNotificationPreferences } from './entities/user-notification-preferences.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { UpdateNotificationPreferenceRequestDto } from './dto/update-notification-preference-request.dto';

@Injectable()
export class NotificationPreferenceRepository {
  constructor(
    @InjectRepository(UserNotificationPreferences)
    private notificationPreferenceRepository: Repository<UserNotificationPreferences>,
  ) {}

  findAllByUser(userId: number): Promise<UserNotificationPreferences[]> {
    return this.notificationPreferenceRepository.find({ where: { userId } });
  }

  updateOrInsert(userId: number, dto: UpdateNotificationPreferenceRequestDto): Promise<InsertResult> {
    return this.notificationPreferenceRepository.upsert(
      { userId, notificationType: dto.notificationType, isEnabled: dto.isEnabled },
      ['userId', 'notificationType'],
    );
  }

  findAllUsersWithEnabledPreference(notificationType: NotificationType) {
    return this.notificationPreferenceRepository
      .find({
        relations: {
          user: true,
        },
        where: {
          notificationType,
          isEnabled: true,
        },
      })
      .then((n) => n.map((n) => n.user));
  }
}
