import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { InsertResult } from 'typeorm';
import { NotificationPreferenceRepository } from './notification-preference.repository';
import { FindNotificationPreferenceResponseDto } from './dto/find-notification-preference-response.dto';
import { UpdateNotificationPreferenceRequestDto } from './dto/update-notification-preference-request.dto';
import { NOTIFICATION_PREFERENCE_DEFAULTS } from './notification-preference.constants';
import { User } from '../../auth/entities/user.entity';
import { NotificationType } from './entities/user-notification-preferences.entity';

@Injectable()
export class NotificationPreferenceService {
  constructor(private notificationPreferenceRepository: NotificationPreferenceRepository) {}
  private readonly logger = new Logger(NotificationPreferenceService.name);

  async find(userId: number): Promise<FindNotificationPreferenceResponseDto> {
    const preferences = await this.notificationPreferenceRepository.findAllByUser(userId);

    const settings = { ...NOTIFICATION_PREFERENCE_DEFAULTS };
    for (const { notificationType, isEnabled } of preferences) {
      settings[notificationType] = isEnabled;
    }

    return settings;
  }

  async update(userId: number, dto: UpdateNotificationPreferenceRequestDto): Promise<InsertResult> {
    try {
      return await this.notificationPreferenceRepository.updateOrInsert(userId, dto);
    } catch (error) {
      this.logger.error(`Failed to upsert notification preference for user ${userId}`, error.stack);

      throw new InternalServerErrorException(`Failed to update notification preference: ${error.message}`);
    }
  }

  async findUsersWithEnabledNotification(notificationType: NotificationType): Promise<User[]> {
    return this.notificationPreferenceRepository.findAllUsersWithEnabledPreference(notificationType);
  }
}
