import { Body, Controller, Get, HttpStatus, Param, Patch, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Roles } from '../auth/decorators/roles.decorator';
import {
  CreateGrantCallDto,
  GrantCallDetailResponseDto,
  FindOneGrantCallResponseDto,
  UpdateGrantCallDto,
  ConditionalBulkTransitionResponseDto,
} from './dto';
import { GrantCallService } from './grant-call.service';
import { StageWithCountResponseDto } from '../workflow/dto/stage-with-count.response.dto';
import { StageDefinitionResponseDto } from '../workflow/dto/stage-definition.response.dto';
import { GrantCallApplicationStagesResponseDto } from './dto/grant-call-application-stage-response.dto';
import { BulkTransitionResponseDto } from '../workflow/dto/bulk-transition-response.dto';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { FindGrantApplicationResponseDto, GetGrantApplicationsQueryDto } from '../grant-application/dto';
import { GrantApplicationService } from '../grant-application/grant-application.service';
import { CommunityVotingAdvanceResponseDto } from './dto/community-voting-advance-response.dto';
import { GrantCallQueryService } from './services/grant-call-query.service';
import { Role } from '../auth/role.enum';
import { Public } from '../auth/decorators/public.decorator';
import { LoggedInUser } from '../auth/decorators/logged-in-user.decorator';
import { ResponseByRole } from '../auth/decorators/response-by-role.decorator';

@ApiTags('Grant Call')
@Controller('grant-call')
export class GrantCallController {
  constructor(
    private readonly grantCallService: GrantCallService,
    private readonly grantApplicationService: GrantApplicationService,
    private readonly grantCallQueryService: GrantCallQueryService,
  ) {}

  @ApiOperation({ summary: 'Get the defined workflow stage definitions for Grant Calls' })
  @ApiResponse({
    status: 200,
    description: 'List of stage definitions for Grant Calls.',
    type: [StageDefinitionResponseDto],
  })
  @Get('/stages')
  getGrantApplicationStageDefinitions(): Promise<StageDefinitionResponseDto[]> {
    return this.grantCallQueryService.getCallStageDefinitions();
  }

  @ApiOperation({ summary: 'Get Grant Call workflow stages with counts of calls in each stage.' })
  @ApiResponse({ status: 200, type: [StageWithCountResponseDto] })
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @Get('/stage-counts')
  geGrantCallStageCounts(): Promise<StageWithCountResponseDto[]> {
    return this.grantCallQueryService.getCallStagesWithCounts();
  }

  @ApiOperation({ summary: 'Get application stage summaries (with counts) for a specific grant call.' })
  @ApiResponse({
    status: 200,
    description: 'Application stage summaries retrieved successfully.',
    type: GrantCallApplicationStagesResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Grant Call not found.' })
  @Public()
  @Get(':slug/application-stage-summaries')
  async getApplicationStageSummaries(@Param('slug') slug: string): Promise<GrantCallApplicationStagesResponseDto> {
    return this.grantCallQueryService.getApplicationStageSummaries(slug);
  }

  @ApiResponse({
    status: 201,
    type: GrantCallDetailResponseDto,
    description: 'Grant Call created successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'When the given grant program, the coordinator or members are not found.' })
  @ApiOperation({ summary: 'Create a new grant call' })
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @Post()
  create(
    @Body() createGrantCallDto: CreateGrantCallDto,
    @LoggedInUser() loggedInUser: LoggedInUser,
  ): Promise<GrantCallDetailResponseDto> {
    return this.grantCallService.create(createGrantCallDto, loggedInUser.id);
  }

  @ApiResponse({
    status: 200,
    description: 'The grant call has been successfully retrieved.',
    type: FindOneGrantCallResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Not Found.' })
  @ApiParam({
    name: 'slug',
    description: 'The slug of the grant call.',
    example: 'grant-call-to-the-moon-hdoija',
  })
  @ApiOperation({ summary: "Get a grant call by it's slug" })
  @Public()
  @Get(':slug')
  @ResponseByRole(FindOneGrantCallResponseDto)
  findOne(@Param('slug') slug: string): Promise<FindOneGrantCallResponseDto> {
    return this.grantCallQueryService.findOne(slug);
  }

  @ApiResponse({
    status: 200,
    type: GrantCallDetailResponseDto,
    description: 'The grant call has been successfully updated.',
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Not Found.' })
  @ApiOperation({ summary: 'Update a grant call' })
  @ApiParam({
    name: 'slug',
    description: 'The slug of the grant call.',
    example: 'grant-call-to-the-moon-hdoija',
  })
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @Patch(':slug')
  update(
    @Param('slug') slug: string,
    @Body() updateGrantCall: UpdateGrantCallDto,
  ): Promise<GrantCallDetailResponseDto> {
    return this.grantCallService.update(slug, updateGrantCall);
  }

  @ApiResponse({
    status: 200,
    description: 'The applications were successfully retrieved.',
    type: [FindGrantApplicationResponseDto],
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'If the grant call is not found.' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'In case of invalid query params.' })
  @ApiParam({
    name: 'slug',
    description: 'The slug of the grant call.',
    example: 'grant-call-1',
  })
  @ApiBearerAuth()
  @Get(':slug/grant-applications')
  @Public()
  getGrantApplications(
    @Param('slug') slug: string,
    @Query()
    filterOptions: GetGrantApplicationsQueryDto,
    @LoggedInUser() loggedInUser: LoggedInUser | null,
  ): Promise<FindGrantApplicationResponseDto[]> {
    return this.grantApplicationService.findGrantApplications(
      { grantCallSlug: slug, ...filterOptions },
      loggedInUser?.id,
    );
  }

  @ApiOperation({ summary: 'Advance Grant Call to the next workflow stage' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Grant Call successfully transitioned.',
    type: SingleTransitionResponseDto,
  })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'User lacks permission.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Call or its workflow state not found.' })
  @ApiResponse({ status: HttpStatus.UNPROCESSABLE_ENTITY, description: 'terminal state' })
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @Post(':slug/transition')
  async transitionGrantCall(@Param('slug') slug: string): Promise<SingleTransitionResponseDto> {
    return this.grantCallService.transitionGrantCallToNextStep(slug);
  }

  @ApiOperation({ summary: "Bulk transition 'Ready' Applications FROM Due Diligence stage" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Bulk transition processed successfully.',
    type: BulkTransitionResponseDto,
  })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'User lacks permission.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Call or required Step Definition not found.' })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Grant Call not in expected stage or target App stage missing.',
  })
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @Post(':slug/applications/bulk-transition-ready-due-diligence')
  async bulkTransitionReadyDueDiligenceApps(@Param('slug') slug: string): Promise<BulkTransitionResponseDto> {
    return await this.grantCallService.bulkTransitionReadyApplicationsAtDueDiligence(slug);
  }

  @ApiOperation({
    summary: 'Advance Grant Call from Screening to Community Voting.',
    description:
      "Advances a Grant Call from the 'Screening' stage to 'Community Voting' if all its applications " +
      "currently in 'Screening' are 'Screening Ready'. " +
      "Upon successful advancement, these applications will move to the 'Qualification' stage.",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Grant call and applications successfully advanced from Screening.',
    type: ConditionalBulkTransitionResponseDto,
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Call or required workflow definitions not found.' })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Transition rule not met (e.g., not all applications ready, or call not in Screening stage).',
  })
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @Post(':slug/advance-from-screening')
  async advanceGrantCallFromScreening(@Param('slug') slug: string): Promise<ConditionalBulkTransitionResponseDto> {
    return this.grantCallService.advanceGrantCallFromScreening(slug);
  }

  @ApiOperation({
    summary: 'Advance Grant Call from Onboarding to Final Community Voting.',
    description:
      'Advances a Grant Call from the (GC_ONBOARDING) stage to (GC_FINAL_COMMUNITY_VOTING) ' +
      "if all its applications currently in (GA_TOWN_HALL) are 'Town Hall Ready'. " +
      'Upon successful advancement, these applications will move to the (GA_FINAL_QUALIFICATION) stage.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Grant call and applications successfully advanced from Onboarding.',
    type: ConditionalBulkTransitionResponseDto,
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Call or required workflow definitions not found.' })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Transition rule not met (e.g., not all applications ready, or call not in Onboarding stage).',
  })
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @Post(':slug/advance-from-onboarding')
  async advanceGrantCallFromOnboarding(@Param('slug') slug: string): Promise<ConditionalBulkTransitionResponseDto> {
    return this.grantCallService.advanceGrantCallFromOnboarding(slug);
  }

  @ApiOperation({ summary: 'Get the votes count for a user' })
  @ApiResponse({ status: HttpStatus.OK, description: 'The votes count for the user.', type: Number })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'The grant call is not found.' })
  @ApiBearerAuth()
  @Roles(Role.COMMUNITY_MEMBER)
  @Get(':slug/user-votes')
  getUserVotes(@Param('slug') slug: string, @LoggedInUser() loggedInUser: LoggedInUser): Promise<number> {
    return this.grantCallQueryService.getUserVotes(slug, loggedInUser.id);
  }

  @ApiOperation({ summary: 'Set Grant Call status to CLOSED and finalize its stage.' })
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @ApiParam({ name: 'slug', type: String, description: 'Grant Call Slug' })
  @ApiResponse({ status: 200, description: 'Grant Call finalized', type: SingleTransitionResponseDto })
  @Post(':slug/finalize')
  async finalizeGrantCall(@Param('slug') slug: string): Promise<SingleTransitionResponseDto> {
    return this.grantCallService.finalizeGrantCall(slug);
  }

  @ApiOperation({
    summary: 'Advance Grant Call from Community Voting to Onboarding.',
    description:
      "Advances a Grant Call from 'Community Voting' to 'Onboarding' if all its applications " +
      "in 'Qualification' are 'Ready'. Applications will move to 'Interview'.",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Grant call and applications successfully advanced from Community Voting.',
    type: CommunityVotingAdvanceResponseDto,
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Call or required workflow definitions not found.' })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description:
      "Transition rule not met (e.g., not all applications ready, or grant call not in 'Community Voting' stage).",
  })
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @Post(':slug/advance-from-community-voting')
  async advanceGrantCallFromCommunityVoting(@Param('slug') slug: string): Promise<CommunityVotingAdvanceResponseDto> {
    return this.grantCallService.advanceGrantCallFromCommunityVoting(slug);
  }

  @ApiOperation({
    summary: 'Finalize Grant Call after Final Community Voting.',
    description:
      "Processes outcomes from the 'Final Community Voting' round based on ranking (top 3 eligible). " +
      "Sets application statuses to 'Approved' (for top 3) or 'Rejected'. " +
      "The Grant Call itself is then transitioned to 'Finalized' stage",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description:
      'Grant call successfully finalized, application statuses set, and grant call moved to its final stage.',
    type: CommunityVotingAdvanceResponseDto,
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Grant Call or required workflow data not found.' })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Cannot finalize (e.g., grant call not in the correct pre-requisite stage for finalization).',
  })
  @ApiBearerAuth()
  @Roles(Role.COORDINATOR)
  @Post(':slug/finalize-after-final-voting')
  async processFinalApplicationsAndFinalizeCall(
    @Param('slug') slug: string,
  ): Promise<CommunityVotingAdvanceResponseDto> {
    return this.grantCallService.processFinalApplicationsAndFinalizeCall(slug);
  }
}
