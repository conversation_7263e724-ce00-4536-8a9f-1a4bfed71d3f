import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GrantCall } from '../entities/grant-call.entity';
import { User } from '../../auth/entities/user.entity';
import { GrantCallRepository } from '../grant-call.repository';
import { GrantCallMapper } from '../grant-call.mapper';
import { WorkflowService } from '../../workflow/workflow.service';
import { VotesService } from '../../votes/votes.service';
import { WorkflowEntityType } from '../../workflow/enums/workflow-entity-type.enum';
import { FindOneGrantCallResponseDto, GrantCallApplicationStagesResponseDto } from '../dto';
import { StageDefinitionResponseDto } from '../../workflow/dto/stage-definition.response.dto';
import { StageWithCountResponseDto } from '../../workflow/dto/stage-with-count.response.dto';

@Injectable()
export class GrantCallQueryService {
  private readonly logger = new Logger(GrantCallQueryService.name);

  constructor(
    @InjectRepository(GrantCall)
    private readonly grantCallRepository: Repository<GrantCall>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly grantCallRepositoryCustom: GrantCallRepository,
    private readonly grantCallMapper: GrantCallMapper,
    private readonly workflowService: WorkflowService,
    private readonly votesService: VotesService,
  ) {}

  async findOne(slug: string): Promise<FindOneGrantCallResponseDto> {
    const grantCall = await this.grantCallRepositoryCustom.findOneBySlug(slug);

    if (!grantCall) {
      throw new NotFoundException(`Grant Call with slug "${slug}" not found`);
    }

    const mappedGrantCall = this.grantCallMapper.mapGrantCallToDetailDto(grantCall);

    return {
      ...mappedGrantCall,
      grantProgram: {
        name: grantCall.grantProgram.name,
        grantProgramSlug: grantCall.grantProgram.grantProgramSlug,
      },
      isAbleToChangeStageManually: this.grantCallMapper.calculateIsAbleToChangeStageManually(
        grantCall,
        mappedGrantCall.openForApplicationStart,
      ),
    };
  }

  async getApplicationStageSummaries(grantCallSlug: string): Promise<GrantCallApplicationStagesResponseDto> {
    const grantCall = await this.grantCallRepository.findOne({
      where: { grantCallSlug },
      select: ['id', 'grantCallSlug'],
    });

    if (!grantCall) {
      throw new NotFoundException(`Grant Call with slug "${grantCallSlug}" not found.`);
    }

    const stageSummaries = await this.workflowService.getStageSummariesWithCounts(
      WorkflowEntityType.APPLICATION,
      grantCall.id,
    );

    const totalApplications = await this.workflowService.countChildEntities(
      WorkflowEntityType.APPLICATION,
      grantCall.id,
    );

    return {
      grantCallSlug: grantCall.grantCallSlug,
      totalApplications,
      stages: stageSummaries,
    };
  }

  async getUserVotes(slug: string, userId: number): Promise<number> {
    const grantCall = await this.grantCallRepository.findOne({ where: { grantCallSlug: slug } });
    const user = await this.userRepository.findOne({ where: { id: userId } });

    if (!grantCall) {
      throw new NotFoundException(`Grant Call with slug "${slug}" not found.`);
    }

    return this.votesService.getUserVotesCount(grantCall.id, user);
  }

  async getCallStageDefinitions(): Promise<StageDefinitionResponseDto[]> {
    return this.workflowService.getStageDefinitionsForEntityType(WorkflowEntityType.CALL);
  }

  async getCallStagesWithCounts(): Promise<StageWithCountResponseDto[]> {
    return this.workflowService.getStageSummariesWithCounts(WorkflowEntityType.CALL);
  }
}
