import { Injectable, NotFoundException, UnprocessableEntityException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { GrantCall } from '../entities/grant-call.entity';
import { WorkflowStepDefinition } from '../../workflow/entities/workflow-step-definition.entity';
import { WorkflowTransitionService } from '../../workflow/workflow-transition.service';
import { VotingOutcomeService } from '../../voting-outcome/voting-outcome.service';
import { GrantApplicationService } from '../../grant-application/grant-application.service';
import { WorkflowEntityType } from '../../workflow/enums/workflow-entity-type.enum';
import { GrantCallStageCode, StageCode } from '../../workflow/enums/stage-code.enum';
import { WorkflowStatus } from '../../workflow/enums/workflow-status.enum';
import { VotingType } from '../../votes/entities/vote.entity';
import { BULK_ACTION_APP_SOURCE_MAP } from '../grant-call.constants';
import { formatStageCode } from '../../utils/formatting.util';
import { BulkTransitionResponseDto } from '../../workflow/dto/bulk-transition-response.dto';
import { SingleTransitionResponseDto } from '../../workflow/dto/single-transition-response.dto';
import { ConditionalBulkTransitionResponseDto } from '../dto/conditional-bulk-transition-response.dto';
import { CommunityVotingAdvanceResponseDto } from '../dto/community-voting-advance-response.dto';
import { NotificationsService } from '../../notifications/notifications.service';

@Injectable()
export class GrantCallTransitionService {
  constructor(
    @InjectRepository(WorkflowStepDefinition)
    private readonly stepDefinitionRepository: Repository<WorkflowStepDefinition>,
    @InjectRepository(GrantCall)
    private readonly grantCallRepository: Repository<GrantCall>,
    private readonly workflowTransitionService: WorkflowTransitionService,
    private readonly votingOutcomeService: VotingOutcomeService,
    private readonly grantApplicationService: GrantApplicationService,
    private readonly dataSource: DataSource,
    private readonly notificationsService: NotificationsService,
  ) {}

  async transitionGrantCallToNextStep(grantCallSlug: string): Promise<SingleTransitionResponseDto> {
    const grantCall = await this.grantCallRepository.findOne({
      relations: { grantProgram: true },
      where: { grantCallSlug },
    });

    if (!grantCall) {
      throw new NotFoundException('Grant Call not found');
    }

    return this.grantCallRepository.manager.transaction(async (txEntityManager) => {
      const transitionResult = await this.workflowTransitionService.transitionSingleStateToNextStep(
        WorkflowEntityType.CALL,
        grantCall.id,
        txEntityManager,
      );

      if (transitionResult.stepDefinitionCode === StageCode.GC_OPEN_FOR_APPLICATIONS) {
        await this.notificationsService.grantCallOpenForApplications(grantCall);
      }

      return transitionResult;
    });
  }

  async bulkTransitionReadyApplicationsAtDueDiligence(grantCallSlug: string): Promise<BulkTransitionResponseDto> {
    const grantCallId = await this.getGrantCallIdBySlug(grantCallSlug);
    const applicationSourceStepId = await this.deriveApplicationSourceStepId(grantCallId, StageCode.GA_DUE_DILIGENCE);

    const result = await this.workflowTransitionService.bulkTransitionReadyApplicationsForCall(
      grantCallId,
      applicationSourceStepId,
    );

    await this.grantApplicationService.notifyDueDiligenceCompleted(result.transitionedApplicationIds);

    return result;
  }

  advanceGrantCallFromScreening(grantCallSlug: string): Promise<ConditionalBulkTransitionResponseDto> {
    return this.conditionallyAdvanceGrantCallStage(grantCallSlug, StageCode.GA_SCREENING);
  }

  advanceGrantCallFromOnboarding(grantCallSlug: string): Promise<ConditionalBulkTransitionResponseDto> {
    return this.conditionallyAdvanceGrantCallStage(grantCallSlug, StageCode.GA_TOWN_HALL);
  }

  async advanceGrantCallFromCommunityVoting(grantCallSlug: string): Promise<CommunityVotingAdvanceResponseDto> {
    return this.processVotingAndAdvanceGrantCall(
      grantCallSlug,
      VotingType.COMMUNITY_VOTING,
      StageCode.GA_QUALIFICATION,
    );
  }

  processFinalApplicationsAndFinalizeCall(grantCallSlug: string): Promise<CommunityVotingAdvanceResponseDto> {
    return this.processVotingAndAdvanceGrantCall(grantCallSlug, VotingType.FINAL_COMMUNITY_VOTING);
  }

  async finalizeGrantCall(grantCallSlug: string): Promise<SingleTransitionResponseDto> {
    return this.grantCallRepository.manager.transaction(async (manager: EntityManager) => {
      const grantCall = await this.grantCallRepository.findOne({
        where: { grantCallSlug },
        relations: { grantProgram: true, workflowState: { currentStepDefinition: true } },
      });

      if (!grantCall) {
        throw new NotFoundException(`Grant Call with slug "${grantCallSlug}" not found.`);
      }

      if (grantCall.workflowState.currentStepDefinition.isTerminal) {
        throw new UnprocessableEntityException(
          `Cannot change Grant Call status from terminal step '${grantCall.workflowState.currentStepDefinition.name}'.`,
        );
      }

      if (
        grantCall.workflowState.status === WorkflowStatus.REJECTED ||
        grantCall.workflowState.status === WorkflowStatus.WITHDRAWN
      ) {
        throw new UnprocessableEntityException(
          `Cannot change Grant Call status from ${grantCall.workflowState.status} status.`,
        );
      }

      await this.workflowTransitionService.updateStateStatus(
        WorkflowEntityType.CALL,
        grantCall.id,
        WorkflowStatus.CLOSED,
        manager,
      );

      return await this.workflowTransitionService.setSpecificStage(
        WorkflowEntityType.CALL,
        grantCall.id,
        StageCode.GC_FINALIZED,
        manager,
      );
    });
  }

  private async processVotingAndAdvanceGrantCall(
    grantCallSlug: string,
    voteTypeForProcessing: VotingType,
    sourceAppStageForTransition?: StageCode,
  ): Promise<CommunityVotingAdvanceResponseDto> {
    return this.dataSource.manager.transaction(async (transactionalEntityManager) => {
      const grantCall = await this.getGrantCallWithGrantProgramBySlug(grantCallSlug);

      const { categorizationResult, allAppScoresMap } =
        await this.votingOutcomeService.processVotingResultsForGrantCall(grantCall.id, voteTypeForProcessing);

      const rejectedWorkflowStateIds = [
        ...new Set(categorizationResult.rejectedApplications.map((application) => application.workflowStateId)),
      ];

      let rejectedApplicationsCount = 0;
      let approvedApplicationsCount = 0;

      if (rejectedWorkflowStateIds.length > 0) {
        rejectedApplicationsCount = await this.workflowTransitionService.bulkSetStatusForWorkflowStateIds(
          rejectedWorkflowStateIds,
          WorkflowStatus.REJECTED,
          transactionalEntityManager,
        );
      }

      const qualifiedWorkflowStateIds = [
        ...new Set(categorizationResult.qualifiedApplications.map((application) => application.workflowStateId)),
      ];

      if (qualifiedWorkflowStateIds.length > 0) {
        approvedApplicationsCount = await this.workflowTransitionService.bulkSetStatusForWorkflowStateIds(
          qualifiedWorkflowStateIds,
          voteTypeForProcessing === VotingType.FINAL_COMMUNITY_VOTING
            ? WorkflowStatus.APPROVED
            : WorkflowStatus.READY_FOR_NEXT_STEP,
          transactionalEntityManager,
        );
      }

      if (sourceAppStageForTransition) {
        const applicationSourceStepId = await this.deriveApplicationSourceStepId(
          grantCall.id,
          sourceAppStageForTransition,
        );
        await this.workflowTransitionService.bulkTransitionReadyApplicationsForCall(
          grantCall.id,
          applicationSourceStepId,
          transactionalEntityManager,
        );
      }

      const grantCallTransitionResult = await this.workflowTransitionService.transitionSingleStateToNextStep(
        WorkflowEntityType.CALL,
        grantCall.id,
        transactionalEntityManager,
      );

      await this.grantApplicationService.anchorVotingEndedEvent(
        grantCall,
        categorizationResult,
        allAppScoresMap,
        voteTypeForProcessing,
      );

      if (grantCall.workflowState.currentStepDefinition.code === StageCode.GC_FINAL_COMMUNITY_VOTING) {
        await this.notificationsService.grantCallFinalized(grantCall);
      }

      return {
        approvedApplicationsCount,
        rejectedApplicationsCount,
        grantCallNewStage: grantCallTransitionResult.stepDefinitionCode,
      };
    });
  }

  private async conditionallyAdvanceGrantCallStage(
    grantCallSlug: string,
    stage: StageCode,
  ): Promise<ConditionalBulkTransitionResponseDto> {
    const grantCallId = await this.getGrantCallIdBySlug(grantCallSlug);
    const applicationSourceStepId = await this.deriveApplicationSourceStepId(grantCallId, stage);

    return this.grantCallRepository.manager.transaction(async (transactionalEntityManager) => {
      const appTransitionResult = await this.workflowTransitionService.bulkTransitionReadyApplicationsForCall(
        grantCallId,
        applicationSourceStepId,
        transactionalEntityManager,
      );

      const updatedGrantCallWorkflowState = await this.workflowTransitionService.transitionSingleStateToNextStep(
        WorkflowEntityType.CALL,
        grantCallId,
        transactionalEntityManager,
      );

      const grantCall = await transactionalEntityManager.getRepository(GrantCall).findOne({
        relations: {
          grantProgram: true,
          workflowState: true,
        },
        where: {
          id: grantCallId,
        },
      });

      if (stage === StageCode.GA_SCREENING) {
        await this.grantApplicationService.anchorVotingStartedEvent(
          appTransitionResult.transitionedApplicationIds,
          VotingType.COMMUNITY_VOTING,
          transactionalEntityManager,
        );

        await this.notificationsService.grantCallStartedCommunityVoting(grantCall);
      } else if (stage === StageCode.GA_TOWN_HALL) {
        await this.grantApplicationService.anchorVotingStartedEvent(
          appTransitionResult.transitionedApplicationIds,
          VotingType.FINAL_COMMUNITY_VOTING,
          transactionalEntityManager,
        );

        await this.notificationsService.grantCallStartedFinalCommunityVoting(grantCall);
      }

      return {
        transitionedCount: appTransitionResult.transitionedCount,
        transitionedApplicationIds: appTransitionResult.transitionedApplicationIds,
        grantCallNewStage: updatedGrantCallWorkflowState.stepDefinitionCode as unknown as GrantCallStageCode,
      };
    });
  }

  private async deriveApplicationSourceStepId(grantCallId: number, applicationSourceStage: StageCode): Promise<number> {
    const { currentStepDefinition } = await this.workflowTransitionService.findWorkflowStateByEntity(
      WorkflowEntityType.CALL,
      grantCallId,
    );

    const { code } = currentStepDefinition;
    const targetApplicationCodes = BULK_ACTION_APP_SOURCE_MAP[code];

    if (!targetApplicationCodes?.length || !targetApplicationCodes.includes(applicationSourceStage)) {
      throw new UnprocessableEntityException(
        `Bulk transition action targeting applications in stage '${formatStageCode(applicationSourceStage)}' is not permitted while the grant call is in stage '${formatStageCode(code)}'.`,
      );
    }

    const applicationStepDef = await this.stepDefinitionRepository.findOne({
      where: { code: applicationSourceStage },
      select: ['id', 'code'],
    });

    return applicationStepDef.id;
  }

  private async getGrantCallIdBySlug(grantCallSlug: string): Promise<number> {
    const grantCall = await this.grantCallRepository.findOne({
      where: { grantCallSlug },
      select: ['id'],
    });

    if (!grantCall) {
      throw new NotFoundException(`Grant Call with slug '${grantCallSlug}' not found.`);
    }
    return grantCall.id;
  }

  private async getGrantCallWithGrantProgramBySlug(grantCallSlug: string) {
    const grantCall = await this.grantCallRepository.findOne({
      relations: {
        grantProgram: true,
        workflowState: {
          currentStepDefinition: true,
        },
      },
      where: { grantCallSlug },
    });

    if (!grantCall) {
      throw new NotFoundException(`Grant Call with slug '${grantCallSlug}' not found.`);
    }

    return grantCall;
  }
}
