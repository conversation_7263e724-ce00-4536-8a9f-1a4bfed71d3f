import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DataSource, EntityManager, In } from 'typeorm';
import { GrantCall } from '../entities/grant-call.entity';
import { GrantProgram } from '../../grant-program/entities/grant-program.entity';
import { WorkflowState } from '../../workflow/entities/workflow-state.entity';
import { WorkflowStepDefinition } from '../../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../../workflow/entities/workflow-template.entity';
import { WorkflowEntityType } from '../../workflow/enums/workflow-entity-type.enum';
import { StageCode } from '../../workflow/enums/stage-code.enum';
import { CreateGrantCallDto } from '../dto';
import {
  ALL_POSSIBLE_SETTING_STAGES,
  REQUIRED_TIMING_STAGES,
  STAGE_TIMING_EXPECTATION,
  STAGE_URL_DTO_KEYS,
  StageTimingType,
  GrantCallTimingProperties,
} from '../grant-call.constants';
import { generateUniqueSlug } from '../../utils/slugify.util';
import { GrantCallStageSetting } from '../entities/grant-call-stage-setting.entity';
import { GrantDistributionRule } from '../entities/grant-distribution-rule.entity';
import { DistributionType } from '../enums/distribution-type.enum';

@Injectable()
export class GrantCallCreationService {
  private readonly logger = new Logger(GrantCallCreationService.name);

  constructor(private readonly dataSource: DataSource) {}

  async create(createGrantCallDto: CreateGrantCallDto, requestingUserId: number): Promise<{ grantCallSlug: string }> {
    return this.dataSource.manager.transaction(async (transactionalEntityManager) => {
      const { grantProgram, stepDefMap } = await this.getProgramAndStageDefinitions(
        createGrantCallDto.grantProgramSlug,
        ALL_POSSIBLE_SETTING_STAGES,
        transactionalEntityManager,
      );

      const grantCallRepo = transactionalEntityManager.getRepository(GrantCall);
      const wfStateRepo = transactionalEntityManager.getRepository(WorkflowState);
      const stepDefRepo = transactionalEntityManager.getRepository(WorkflowStepDefinition);
      const wfTemplateRepo = transactionalEntityManager.getRepository(WorkflowTemplate);

      const grantCallSlug: string = await generateUniqueSlug(
        createGrantCallDto.name,
        async (slugToCheck) => grantCallRepo.exists({ where: { grantCallSlug: slugToCheck } }),
        'grant-call',
      );

      const defaultClosedStepDef = await stepDefRepo.findOneBy({ code: StageCode.GC_CLOSED });
      const workflowTemplate = await wfTemplateRepo.findOneBy({
        entityType: WorkflowEntityType.CALL,
      });

      const initialWorkflowState = wfStateRepo.create({
        workflowTemplate: workflowTemplate,
        currentStepDefinitionId: defaultClosedStepDef.id,
        currentStepDefinition: defaultClosedStepDef,
        currentStepTransitionedAt: new Date(),
        currentStepEndsAt: null,
      });

      await wfStateRepo.save(initialWorkflowState);

      const grantCall = grantCallRepo.create({
        grantProgram,
        name: createGrantCallDto.name,
        grantCallSlug,
        description: createGrantCallDto.description,
        businessCategory: createGrantCallDto.businessCategory,
        categories: createGrantCallDto.categories,
        totalGrantAmount: createGrantCallDto.totalGrantAmount,
        createdById: requestingUserId,
        workflowState: initialWorkflowState,
        stageSettings: this.buildStageSettings(createGrantCallDto, stepDefMap),
        distributionRules: this.buildDistributionRules(createGrantCallDto),
      });

      await grantCallRepo.save(grantCall);

      return {
        grantCallSlug,
      };
    });
  }

  private async getProgramAndStageDefinitions(
    grantProgramSlug: string,
    stageCodesToFetch: StageCode[],
    manager: EntityManager,
  ): Promise<{ grantProgram: GrantProgram; stepDefMap: Map<StageCode, WorkflowStepDefinition> }> {
    const grantProgramRepo = manager.getRepository(GrantProgram);
    const stepDefRepo = manager.getRepository(WorkflowStepDefinition);

    const grantProgram = await grantProgramRepo.findOneBy({ grantProgramSlug });

    if (!grantProgram) {
      throw new NotFoundException(`Grant Program '${grantProgramSlug}' not found`);
    }

    const stepDefs = await stepDefRepo.find({ where: { code: In(stageCodesToFetch) } });
    const stepDefMap = new Map(stepDefs.map((def) => [def.code, def]));

    return { grantProgram, stepDefMap };
  }

  private buildDistributionRules(dto: CreateGrantCallDto): Partial<GrantDistributionRule>[] {
    return dto.grantDistribution.map((percentageValue, index) => ({
      rank: index + 1,
      type: DistributionType.PERCENTAGE,
      value: percentageValue,
    }));
  }

  private determineTimingProperties(stageCode: StageCode, dto: CreateGrantCallDto): GrantCallTimingProperties | null {
    if (!REQUIRED_TIMING_STAGES.includes(stageCode)) {
      return null;
    }

    const expectedTiming = STAGE_TIMING_EXPECTATION[stageCode];
    const timingResult: GrantCallTimingProperties = {};

    if (expectedTiming === StageTimingType.DATE_RANGE && stageCode === StageCode.GC_OPEN_FOR_APPLICATIONS) {
      timingResult.startDate = new Date(dto.openForApplicationStart);
      timingResult.endDate = new Date(dto.openForApplicationEnd);
    } else if (expectedTiming === StageTimingType.DURATION) {
      if (stageCode === StageCode.GC_COMMUNITY_VOTING) {
        timingResult.durationSeconds = dto.communityVotingTime1;
      } else if (stageCode === StageCode.GC_FINAL_COMMUNITY_VOTING) {
        timingResult.durationSeconds = dto.communityVotingTime2;
      }
    } else {
      this.logger.warn(`applyTimingSettings: Configuration mismatch for required timing stage ${stageCode}.`);
    }

    return timingResult;
  }

  private determineUrlProperty(
    stageCode: StageCode,
    dto: CreateGrantCallDto,
  ): Partial<Pick<GrantCallStageSetting, 'stageUrl'>> | null {
    const dtoKey = STAGE_URL_DTO_KEYS[stageCode];

    if (!dtoKey) {
      return null;
    }

    return { stageUrl: dto[dtoKey] ?? null };
  }

  private buildSingleStageSetting(
    stageCode: StageCode,
    dto: CreateGrantCallDto,
    stepDefMap: Map<StageCode, WorkflowStepDefinition>,
  ): GrantCallStageSetting | null {
    const stepDefinition = stepDefMap.get(stageCode);

    const timingProps = this.determineTimingProperties(stageCode, dto);
    const urlProps = this.determineUrlProperty(stageCode, dto);

    if (!timingProps && !urlProps) {
      return null;
    }

    const setting = new GrantCallStageSetting();
    setting.workflowStepDefinition = stepDefinition;

    Object.assign(setting, timingProps, urlProps);

    return setting;
  }

  private buildStageSettings(
    dto: CreateGrantCallDto,
    stepDefMap: Map<StageCode, WorkflowStepDefinition>,
  ): GrantCallStageSetting[] {
    return ALL_POSSIBLE_SETTING_STAGES.map((stageCode) =>
      this.buildSingleStageSetting(stageCode, dto, stepDefMap),
    ).filter((setting): setting is GrantCallStageSetting => setting !== null);
  }
}
