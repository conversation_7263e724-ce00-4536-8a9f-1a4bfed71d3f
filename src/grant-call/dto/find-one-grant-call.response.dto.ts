import { Grant<PERSON>allStageCode, StageCode } from '../../workflow/enums/stage-code.enum';

import { ApiProperty, PickType } from '@nestjs/swagger';
import { FindOneGrantCallBaseDto } from './find-one-grant-call-base.dto';
import { GrantProgramDto as GrantProgramBaseDto } from '../../grant-program/dto';
import { UserPublicResponseDto } from '../../auth/dto/user-public-response.dto';

class GrantProgramDto extends PickType(GrantProgramBaseDto, ['name', 'grantProgramSlug'] as const) {}

export class FindOneGrantCallResponseDto extends FindOneGrantCallBaseDto {
  @ApiProperty({
    description: 'Unique slug identifying the Grant Call.',
    example: 'grant-call-to-the-moon-hdoija',
  })
  grantCallSlug: string;

  @ApiProperty({
    type: UserPublicResponseDto,
    description: 'Details of the user who created the grant call.',
  })
  createdBy: UserPublicResponseDto;

  @ApiProperty({
    description: 'The unique code identifying the current workflow stage.',
    enum: GrantCallStageCode,
    enumName: 'GrantCallStageCode',
    example: GrantCallStageCode.OPEN_FOR_APPLICATIONS,
    nullable: false,
  })
  status: StageCode;

  @ApiProperty({
    example: '2021-09-01T00:00:00.000Z',
    description: 'The last updated date of the grant call.',
  })
  updatedAt: Date;

  @ApiProperty({
    type: GrantProgramDto,
    description: 'Details of the Grant Program.',
  })
  grantProgram: GrantProgramDto;

  @ApiProperty({
    example: true,
    description: 'Tells if user is able to change grant call stage manually',
  })
  isAbleToChangeStageManually: boolean;
}
