import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ArrayMaxSize,
  ArrayMinSize,
  ArrayNotEmpty,
  IsArray,
  IsDateString,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  IsUrl,
  <PERSON><PERSON>ength,
} from 'class-validator';

import { BusinessCategory } from '../enums/business-category.enum';
import { GrantCategory } from '../enums/grant-category.enum';
import { IsArraySumEqualTo100 } from '../../common/validators/array-sum-meets-target.validator';
import { IsDateAfter } from '../../common/validators/is-date-after.validator';

export class GrantCallBaseDto {
  @ApiProperty({
    description: 'The name of the grant call',
    example: 'Grant Call 1',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(120)
  name: string;

  @ApiProperty({
    description: 'The description of the grant call',
    example: 'This is a grant call',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'The business category of the grant call',
    example: BusinessCategory.STARTUP,
    enum: BusinessCategory,
    enumName: 'BusinessCategory',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(BusinessCategory)
  businessCategory: BusinessCategory;

  @ApiProperty({
    description: 'Target Grant Categories',
    enum: GrantCategory,
    enumName: 'GrantCategory',
    isArray: true,
    required: true,
    example: [GrantCategory.SUSTAINABILITY],
  })
  @IsArray()
  @IsEnum(GrantCategory, { each: true })
  @ArrayNotEmpty({ message: 'At least one category required.' })
  categories: GrantCategory[];

  @ApiProperty({ description: 'Total grant amount.', example: 100000.0, required: true })
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  totalGrantAmount: number;

  @ApiProperty({
    description: 'Application Start Date.',
    required: true,
    example: '2024-01-01T00:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  openForApplicationStart: string;

  @ApiProperty({
    description: 'Application End Date (after start date).',
    required: true,
    example: '2024-01-15T23:59:59Z',
  })
  @IsDateString()
  @IsNotEmpty()
  @IsDateAfter('openForApplicationStart')
  openForApplicationEnd: string;

  @ApiProperty({
    description: 'Duration for 1st community voting (SECONDS)',
    required: true,
    example: 604800,
  })
  @IsInt()
  @IsPositive()
  communityVotingTime1: number;

  @ApiProperty({
    description: 'Duration for final community voting (SECONDS)',
    required: true,
    example: 259200,
  })
  @IsInt()
  @IsPositive()
  communityVotingTime2: number;

  @ApiProperty({
    description: `Array of EXACTLY 3 POSITIVE percentage values for ranks 1-3. Sum must be 100%.`,
    type: [Number],
    required: true,
    example: [50, 30, 20],
  })
  @IsArray()
  @ArrayMinSize(3, { message: 'Distribution must provide exactly 3 percentage values.' })
  @ArrayMaxSize(3, { message: 'Distribution must provide exactly 3 percentage values.' })
  @IsNumber({ maxDecimalPlaces: 2 }, { each: true })
  @IsPositive({ each: true, message: 'Each percentage must be positive.' })
  @IsArraySumEqualTo100()
  grantDistribution: number[];

  @ApiProperty({
    description: 'Screening Form URL (only visible to coordinators)',
    example: 'https://forms.clickup.com/12345/f/abc-123',
    required: true,
  })
  @IsNotEmpty()
  @IsUrl({}, { message: 'Screening Form URL must be a valid URL.' })
  screeningFormUrl: string;

  @ApiProperty({
    description: 'Due Diligence Form URL (only visible to coordinators)',
    example: 'https://sharing.clickup.com/12345/f/def-456',
    required: true,
  })
  @IsNotEmpty()
  @IsUrl({}, { message: 'Due Diligence Form URL must be a valid URL.' })
  dueDiligenceFormUrl: string;

  @ApiPropertyOptional({
    description: 'Optional Interview Scheduling URL',
    example: 'https://calendly.com/your-org/30min',
    required: false,
  })
  @IsOptional()
  @IsUrl({}, { message: 'Interview Scheduling URL must be a valid URL.' })
  interviewSchedulingUrl?: string;

  @ApiPropertyOptional({
    description: 'Optional Town Hall Scheduling URL',
    example: 'https://calendly.com/your-org/town-hall-q1',
    required: false,
  })
  @IsOptional()
  @IsUrl({}, { message: 'Town Hall Scheduling URL must be a valid URL.' })
  townHallSchedulingUrl?: string;
}
