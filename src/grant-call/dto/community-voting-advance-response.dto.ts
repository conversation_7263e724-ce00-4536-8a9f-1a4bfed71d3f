import { Grant<PERSON>allStageCode, StageCode } from '../../workflow/enums/stage-code.enum';

import { ApiProperty } from '@nestjs/swagger';

export class CommunityVotingAdvanceResponseDto {
  @ApiProperty({
    description:
      'Number of applications whose statuses were successfully set to indicate approval/qualification by vote (e.g., READY_FOR_NEXT_STEP).',
    example: 15,
  })
  approvedApplicationsCount: number;

  @ApiProperty({
    description:
      'Number of applications whose statuses were successfully set to indicate rejection by vote (e.g., REJECTED).',
    example: 5,
  })
  rejectedApplicationsCount: number;

  @ApiProperty({
    description: 'The new stage the Grant Call itself has successfully moved to after processing.',
    enum: StageCode,
    enumName: 'GrantCallStageCode',
    example: GrantCallStageCode.ONBOARDING,
  })
  grantCallNewStage: StageCode;
}
