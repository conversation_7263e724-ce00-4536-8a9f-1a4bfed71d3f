import { PickType } from '@nestjs/mapped-types';
import { GrantCall as GrantCallEntity } from '../entities/grant-call.entity';
import { UserPublicResponseDto } from '../../auth/dto/user-public-response.dto';
import { GrantProgram as GrantProgramEntity } from '../../grant-program/entities/grant-program.entity';
import { WorkflowState } from '../../workflow/entities/workflow-state.entity';
import { GrantCallStageSetting } from '../entities/grant-call-stage-setting.entity';
import { GrantDistributionRule } from '../entities/grant-distribution-rule.entity';

class GrantCall extends PickType(GrantCallEntity, [
  'id',
  'name',
  'description',
  'grantCallSlug',
  'businessCategory',
  'totalGrantAmount',
  'categories',
  'updatedAt',
] as const) {}
class GrantProgram extends PickType(GrantProgramEntity, ['id', 'name', 'grantProgramSlug'] as const) {}

export class GrantCallWithApplicationsCountDto extends GrantCall {
  createdBy: UserPublicResponseDto;
  grantProgram: GrantProgram;
  workflowState: WorkflowState;
  stageSettings: GrantCallStageSetting[];
  distributionRules: GrantDistributionRule[];

  grantApplicationsCount: number;
  inProgressGrantApplicationsCount: number;
}
