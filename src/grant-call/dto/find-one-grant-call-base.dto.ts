import { ApiProperty } from '@nestjs/swagger';
import { Grant<PERSON>allBaseDto } from './grant-call-base.dto';
import { GrantCallStageCode, StageCode } from '../../workflow/enums/stage-code.enum';
import { WorkflowStateDto } from '../../workflow/dto/workflow-state.dto';
import { UserPublicResponseDto } from '../../auth/dto/user-public-response.dto';
import { ExposeForRoles } from '../../auth/decorators/response-by-role.decorator';
import { Role } from '../../auth/role.enum';

export class FindOneGrantCallBaseDto extends GrantCallBaseDto {
  @ApiProperty({
    description: 'Unique slug identifying the Grant Call.',
    example: 'grant-call-to-the-moon-hdoija',
  })
  grantCallSlug: string;

  @ApiProperty({
    type: UserPublicResponseDto,
    description: 'Details of the user who created the grant call.',
  })
  createdBy: UserPublicResponseDto;

  @ApiProperty({
    description: 'The unique code identifying the current workflow stage.',
    enum: GrantCallStageCode,
    enumName: 'GrantCallStageCode',
    example: GrantCallStageCode.OPEN_FOR_APPLICATIONS,
    nullable: false,
  })
  status: StageCode;

  @ApiProperty({
    description: 'The information about current workflow stage',
    type: WorkflowStateDto,
    example: {
      id: 12,
      currentStepTransitionedAt: '2025-05-29T18:02:02.531Z',
      currentStepEndsAt: '2025-05-28T18:30:00.000Z',
    },
  })
  workflowState: WorkflowStateDto;

  @ApiProperty({
    example: '2021-09-01T00:00:00.000Z',
    description: 'The last updated date of the grant call.',
  })
  updatedAt: Date;

  @ApiProperty({ description: 'Number of applications submitted to this grant call.', example: 12 })
  grantApplicationsCount?: number;

  @ExposeForRoles(Role.COORDINATOR)
  override screeningFormUrl: string;

  @ExposeForRoles(Role.COORDINATOR)
  override dueDiligenceFormUrl: string;

  @ExposeForRoles(Role.COORDINATOR)
  override interviewSchedulingUrl?: string;

  @ExposeForRoles(Role.COORDINATOR)
  override townHallSchedulingUrl?: string;
}
