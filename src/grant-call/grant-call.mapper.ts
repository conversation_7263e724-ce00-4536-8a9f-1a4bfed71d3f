import { Injectable, BadRequestException } from '@nestjs/common';
import { GrantCallStageSetting } from './entities/grant-call-stage-setting.entity';
import { GrantDistributionRule } from './entities/grant-distribution-rule.entity';
import { FindOneGrantCallResponseDto } from './dto';
import { StageCode } from '../workflow/enums/stage-code.enum';
import {
  GrantCallTimingSettings,
  GrantCallUrlDtoKey,
  REQUIRED_TIMING_STAGES,
  STAGE_URL_DTO_KEYS,
} from './grant-call.constants';
import { StageTransitionType } from '../workflow/enums/stage-transition-type.enum';
import { GrantCategory } from './enums/grant-category.enum';
import { GrantCallWithApplicationsCountDto } from './dto/grant-call-with-applications-count.dto';

@Injectable()
export class GrantCallMapper {
  mapGrantCallToDetailDto(grantCall: GrantCallWithApplicationsCountDto): FindOneGrantCallResponseDto {
    const {
      grantCallSlug,
      name,
      description,
      businessCategory,
      categories,
      totalGrantAmount,
      createdBy,
      stageSettings,
      grantApplicationsCount,
      distributionRules,
      workflowState,
      updatedAt,
      grantProgram,
    } = grantCall;

    const { id, displayName } = createdBy;
    const timingSettings = this.extractTimingsFromSettings(stageSettings);
    const urlSettings = this.extractUrlsFromSettings(stageSettings);
    const grantDistributionSettings = this.extractGrantDistributionSettings(distributionRules);

    return {
      grantCallSlug,
      name,
      description,
      businessCategory,
      totalGrantAmount,
      updatedAt,
      ...timingSettings,
      ...urlSettings,
      createdBy: { id, displayName },
      categories: categories as GrantCategory[],
      grantDistribution: grantDistributionSettings,
      status: workflowState?.currentStepDefinition.code,
      workflowState: {
        id: workflowState.id,
        currentStepTransitionedAt: workflowState.currentStepTransitionedAt,
        currentStepEndsAt: workflowState.currentStepEndsAt,
      },
      grantApplicationsCount,
      grantProgram: {
        name: grantProgram.name,
        grantProgramSlug: grantProgram.grantProgramSlug,
      },
      isAbleToChangeStageManually: this.calculateIsAbleToChangeStageManually(
        grantCall,
        timingSettings.openForApplicationStart,
      ),
    };
  }

  calculateIsAbleToChangeStageManually(
    grantCall: GrantCallWithApplicationsCountDto,
    openForApplicationStart: string,
  ): boolean {
    const wfState = grantCall.workflowState;
    const currentStepDef = wfState.currentStepDefinition;
    const currentStageCode = currentStepDef.code;

    const openForApplicationStartPhasePassed = new Date(openForApplicationStart) <= new Date();
    const deadlineHasPassed = !wfState.currentStepEndsAt || wfState.currentStepEndsAt <= new Date();

    const allApplicationsAreReady = grantCall.inProgressGrantApplicationsCount === 0;

    const isManualTransition = currentStepDef.transitionType === StageTransitionType.MANUAL;

    const isNotTerminal = !currentStepDef.isTerminal;

    const hasNextStepInTemplate = wfState.workflowTemplate.steps.some(
      (step) => step.sequenceNumber > currentStepDef.sequenceNumber,
    );

    if (
      !isManualTransition ||
      !isNotTerminal ||
      !hasNextStepInTemplate ||
      currentStageCode === StageCode.GC_FINALIZED
    ) {
      return false;
    }

    return (
      (currentStageCode === StageCode.GC_CLOSED && openForApplicationStartPhasePassed) ||
      (currentStageCode === StageCode.GC_OPEN_FOR_APPLICATIONS && deadlineHasPassed) ||
      (currentStageCode === StageCode.GC_SCREENING && allApplicationsAreReady) ||
      (currentStageCode === StageCode.GC_COMMUNITY_VOTING && deadlineHasPassed) ||
      (currentStageCode === StageCode.GC_ONBOARDING && allApplicationsAreReady) ||
      (currentStageCode === StageCode.GC_FINAL_COMMUNITY_VOTING && deadlineHasPassed)
    );
  }

  private findSettingByCode(settings: GrantCallStageSetting[], code: StageCode): GrantCallStageSetting {
    return settings.find(({ workflowStepDefinition }) => workflowStepDefinition.code === code);
  }

  extractTimingsFromSettings(settings: GrantCallStageSetting[]): GrantCallTimingSettings {
    const relevantSettings = REQUIRED_TIMING_STAGES.map((code) => this.findSettingByCode(settings, code));
    const [applicationSetting, voting1Setting, voting2Setting] = relevantSettings;

    if (!applicationSetting) {
      throw new BadRequestException('Application stage settings not found');
    }

    if (!applicationSetting.startDate || !applicationSetting.endDate) {
      throw new BadRequestException(
        `Application stage timing is incomplete. Start date: ${applicationSetting.startDate}, End date: ${applicationSetting.endDate}`,
      );
    }

    return {
      openForApplicationStart: applicationSetting.startDate.toISOString(),
      openForApplicationEnd: applicationSetting.endDate.toISOString(),
      communityVotingTime1: voting1Setting?.durationSeconds ?? null,
      communityVotingTime2: voting2Setting?.durationSeconds ?? null,
    };
  }

  private extractGrantDistributionSettings(rules: GrantDistributionRule[]): number[] {
    return rules?.map(({ value }) => value ?? 0);
  }

  private extractUrlsFromSettings(
    settings: GrantCallStageSetting[],
  ): Pick<FindOneGrantCallResponseDto, GrantCallUrlDtoKey> {
    return Object.fromEntries(
      (Object.entries(STAGE_URL_DTO_KEYS) as [StageCode, GrantCallUrlDtoKey][]).map(([stageCode, dtoKey]) => [
        dtoKey,
        this.findSettingByCode(settings, stageCode)?.stageUrl ?? null,
      ]),
    ) as Record<GrantCallUrlDtoKey, string | null>;
  }
}
