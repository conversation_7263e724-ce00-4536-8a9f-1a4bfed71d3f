import { ConditionalBulkTransitionResponseDto, CreateGrantCallDto, GrantCallBaseDto } from './dto';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { GrantCallStageCode, StageCode } from '../workflow/enums/stage-code.enum';
import { Test, TestingModule } from '@nestjs/testing';

import { BusinessCategory } from './enums/business-category.enum';
import { ConfigService } from '@nestjs/config';
import { DistributionType } from './enums/distribution-type.enum';
import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { GrantApplicationService } from '../grant-application/grant-application.service';
import { GrantCall } from './entities/grant-call.entity';
import { GrantCallMapper } from './grant-call.mapper';
import { GrantCallRepository } from './grant-call.repository';
import { GrantCallService } from './grant-call.service';
import { GrantCallStageSetting } from './entities/grant-call-stage-setting.entity';
import { GrantCategory } from './enums/grant-category.enum';
import { GrantDistributionRule } from './entities/grant-distribution-rule.entity';
import { GrantProgram } from '../grant-program/entities/grant-program.entity';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { StageTransitionType } from '../workflow/enums/stage-transition-type.enum';
import { User } from '../auth/entities/user.entity';
import { VotingOutcomeService } from '../voting-outcome/voting-outcome.service';
import { VotesService } from '../votes/votes.service';
import { VotingType } from '../votes/entities/vote.entity';
import { WorkflowEntityType } from '../workflow/enums/workflow-entity-type.enum';
import { WorkflowService } from '../workflow/workflow.service';
import { WorkflowState } from '../workflow/entities/workflow-state.entity';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';
import { generateUniqueSlug } from '../utils/slugify.util';
import { getRepositoryToken } from '@nestjs/typeorm';
import { GrantCallCreationService } from './services/grant-call-creation.service';
import { GrantCallQueryService } from './services/grant-call-query.service';
import { GrantCallTransitionService } from './services/grant-call-transition.service';
import { GrantCallVotingSummaryDto } from '../voting-outcome/dto/grant-call-voting-summary.dto';
import { ApplicationScoresMap } from '../voting-outcome/types/voting-service.types';
import { NotificationsService } from '../notifications/notifications.service';

jest.mock('../utils/slugify.util');

const mockedGenerateUniqueSlug = generateUniqueSlug as jest.MockedFunction<
  (name: string, checker: (s: string) => Promise<boolean>, defaultBase?: string) => Promise<string>
>;

const mockGrantProgram = (): GrantProgram =>
  ({
    id: 1,
    grantProgramSlug: 'test-program-slug',
    name: 'Test Program',
    grantCalls: [],
    workflowState: {
      currentStepDefinition: {
        code: StageCode.GP_OPEN,
      },
    },
    grantProgramCoordinator: {
      id: 1,
      displayName: 'Test Coordinator',
    },
  }) as GrantProgram;

const mockUser = (): User => ({ id: 101, email: '<EMAIL>' }) as User;

const mockWorkflowStepDef = (id: number, code: StageCode): WorkflowStepDefinition =>
  ({ id, code, name: `Step ${code}` }) as WorkflowStepDefinition;

const mockGrantCallStageSetting = (idSuffix: number, wfId: number): GrantCallStageSetting =>
  ({
    id: 500 + idSuffix,
    grantCallId: 5,
    grantCall: {} as GrantCall,
    workflowStepDefinitionId: wfId,
    workflowStepDefinition: {} as WorkflowStepDefinition,
    startDate: new Date('2024-01-10'),
    endDate: null,
    durationSeconds: null,
    stageUrl: null,
  }) as GrantCallStageSetting;

const mockGrantDistributionRule = (idSuffix: number, rank: number, value: number): GrantDistributionRule =>
  ({
    id: 600 + idSuffix,
    grantCallId: 5,
    grantCall: {} as GrantCall,
    rank,
    type: DistributionType.PERCENTAGE,
    value,
  }) as GrantDistributionRule;

const createMockGrantCall = (id: number, slug: string, existingRelations: boolean = false): GrantCall =>
  ({
    id,
    grantCallSlug: slug,
    name: 'Original Grant Call',
    description: 'Original Description',
    businessCategory: BusinessCategory.STARTUP,
    categories: [GrantCategory.CONSUMER_LOYALTY],
    totalGrantAmount: 50000,
    grantProgramId: 1,
    grantProgram: mockGrantProgram(),
    createdById: mockUser().id,
    createdBy: mockUser(),
    stageSettings: existingRelations ? [mockGrantCallStageSetting(1, 10), mockGrantCallStageSetting(2, 20)] : [],
    distributionRules: existingRelations ? [mockGrantDistributionRule(1, 1, 60)] : [],
    createdAt: new Date(),
    updatedAt: new Date(),
    workflowStateId: 1,
    workflowState: {
      id: 1,
      currentStepDefinition: {
        id: 1,
        name: 'Test Step',
        code: StageCode.GC_OPEN_FOR_APPLICATIONS,
        isTerminal: false,
        workflowTemplateId: 1,
        workflowTemplate: null,
        sequenceNumber: 1,
        transitionType: StageTransitionType.AUTOMATIC,
      },
    },
    applicationStages: [],
  }) as unknown as GrantCall;

const mockCreateDto = (): CreateGrantCallDto => ({
  grantProgramSlug: 'test-program-slug',
  name: 'New Grant Call',
  description: 'Test Description',
  businessCategory: BusinessCategory.GOVERNMENT,
  categories: [GrantCategory.SUSTAINABILITY, GrantCategory.OTHER],
  totalGrantAmount: 100000,
  openForApplicationStart: '2024-01-01T00:00:00Z',
  openForApplicationEnd: '2024-01-31T23:59:59Z',
  communityVotingTime1: 604800,
  communityVotingTime2: 259200,
  grantDistribution: [50, 30, 20],
  screeningFormUrl: 'https://forms.clickup.com/123',
  dueDiligenceFormUrl: 'https://forms.clickup.com/456',
  interviewSchedulingUrl: 'https://calendly.com/your-org/30min',
  townHallSchedulingUrl: 'https://calendly.com/your-org/town-hall-q1',
});

const mockUpdateDto = (): GrantCallBaseDto => ({
  name: 'Updated Grant Call Name',
  description: 'Updated Description',
  businessCategory: BusinessCategory.ENTERPRISE,
  categories: [GrantCategory.DIGITAL_IDENTITY],
  totalGrantAmount: 150000,
  openForApplicationStart: '2024-02-01T00:00:00Z',
  openForApplicationEnd: '2024-02-28T23:59:59Z',
  communityVotingTime1: 700000,
  communityVotingTime2: 300000,
  grantDistribution: [70, 20, 10],
  screeningFormUrl: 'https://forms.clickup.com/updated-123',
  dueDiligenceFormUrl: 'https://forms.clickup.com/updated-456',
  interviewSchedulingUrl: 'https://calendly.com/your-org/updated-30min',
  townHallSchedulingUrl: 'https://calendly.com/your-org/updated-town-hall',
});

const mockGrantCallRepository = { findOneBySlug: jest.fn() };
const mockMapper = { mapGrantCallToDetailDto: jest.fn(), calculateIsAbleToChangeStageManually: jest.fn() };
const mockGrantCallRepoTransactional = { findOne: jest.fn(), save: jest.fn(), create: jest.fn() };
const mockWorkflowStateRepoTransactional = { create: jest.fn(), save: jest.fn() };
const mockDistRuleRepoTransactional = { remove: jest.fn() };
const mockStageSettingRepoTransactional = { remove: jest.fn() };
const mockProgramRepoTransactional = { findOneBy: jest.fn() };
const mockWfStepDefRepoTransactional = { find: jest.fn(), findOneBy: jest.fn() };
const mockWfTemplateRepoTransactional = { find: jest.fn(), findOneBy: jest.fn() };
const mockUserRepoTransactional = {
  findOneBy: jest.fn(),
};

const transactionalRepositoryMap = new Map<any, any>([
  [GrantCall, mockGrantCallRepoTransactional],
  [GrantDistributionRule, mockDistRuleRepoTransactional],
  [GrantCallStageSetting, mockStageSettingRepoTransactional],
  [GrantProgram, mockProgramRepoTransactional],
  [WorkflowStepDefinition, mockWfStepDefRepoTransactional],
  [User, mockUserRepoTransactional],
  [WorkflowState, mockWorkflowStateRepoTransactional],
  [WorkflowTemplate, mockWfTemplateRepoTransactional],
]);

const mockTransactionalEntityManager = {
  getRepository: jest.fn((entity) => {
    const repo = transactionalRepositoryMap.get(entity);
    if (repo) return repo;
    const entityName = typeof entity === 'function' ? entity.name : entity;
    throw new Error(`Mock getRepository not configured for ${entityName}`);
  }),
};

const mockGrantCallRepositoryValue = {
  findOne: mockGrantCallRepoTransactional.findOne,
  save: jest.fn(),
  manager: {
    transaction: jest.fn().mockImplementation(async (isoLevel, callback) => {
      const actualCallback = typeof isoLevel === 'function' ? isoLevel : callback;
      return await actualCallback(mockTransactionalEntityManager);
    }),
  },
};

const mockWorkflowTransitionService = {
  transitionSingleStateToNextStep: jest.fn(),
  findWorkflowStateByEntity: jest.fn(),
  bulkTransitionReadyApplicationsForCall: jest.fn(),
  bulkSetStatusForWorkflowStateIds: jest.fn(),
  updateStateStatus: jest.fn(),
  setSpecificStage: jest.fn(),
};

const createMockRepository = () => ({
  findOne: jest.fn(),
  findOneBy: jest.fn(),
  find: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  createQueryBuilder: jest.fn(),
});

describe('GrantCallService', () => {
  let service: GrantCallService;
  let grantCallCreationService: GrantCallCreationService;
  let grantCallTransitionService: GrantCallTransitionService;

  let grantCallRepository: Repository<GrantCall>;
  let votingOutcomeService: jest.Mocked<VotingOutcomeService>;
  let mockEntityManager: jest.Mocked<EntityManager>;
  const mockDataSource = {
    manager: {
      transaction: jest.fn().mockImplementation(async (callback) => callback(mockEntityManager)),
    },
  };
  let workflowTransitionService: WorkflowTransitionService;

  beforeEach(async () => {
    jest.clearAllMocks();

    mockEntityManager = {
      findOne: jest.fn(),
      getRepository: jest.fn().mockImplementation((entityType) => {
        if (entityType === WorkflowState) {
          return { update: jest.fn().mockResolvedValue({ affected: 0 }) };
        }
        return {
          findOne: jest.fn(),
          find: jest.fn(),
          update: jest.fn(),
          save: jest.fn(),
        };
      }),
    } as unknown as jest.Mocked<EntityManager>;

    const mockVotingOutcomeService = {
      processVotingResultsForGrantCall: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GrantCallService,
        GrantCallCreationService,
        GrantCallTransitionService,
        GrantCallQueryService,
        WorkflowService,
        ConfigService,
        { provide: DataSource, useValue: mockDataSource },

        { provide: VotingOutcomeService, useValue: mockVotingOutcomeService },
        {
          provide: VotesService,
          useValue: {
            getUserVotesCount: jest.fn(),
          },
        },
        {
          provide: GrantApplicationService,
          useValue: {
            onGrantCallStageChange: jest.fn(),
            anchorVotingStartedEvent: jest.fn(),
            anchorVotingEndedEvent: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(WorkflowTemplate),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(GrantProgram),
          useValue: {
            find: jest.fn(),
          },
        },
        { provide: GrantCallMapper, useValue: mockMapper },
        {
          provide: getRepositoryToken(GrantProgram),
          useClass: Repository,
        },
        {
          provide: WorkflowTransitionService,
          useValue: mockWorkflowTransitionService,
        },
        {
          provide: getRepositoryToken(WorkflowStepDefinition),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(WorkflowState),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(User),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(GrantCall),
          useValue: mockGrantCallRepositoryValue,
        },
        {
          provide: getRepositoryToken(GrantApplication),
          useClass: Repository,
        },
        {
          provide: NotificationsService,
          useValue: {
            grantCallOpenForApplications: jest.fn(),
            grantCallStartedCommunityVoting: jest.fn(),
            grantCallStartedFinalCommunityVoting: jest.fn(),
            grantCallFinalized: jest.fn(),
          },
        },
        {
          provide: GrantCallRepository,
          useValue: mockGrantCallRepository,
        },
      ],
    }).compile();

    service = module.get<GrantCallService>(GrantCallService);
    grantCallCreationService = module.get<GrantCallCreationService>(GrantCallCreationService);
    grantCallTransitionService = module.get<GrantCallTransitionService>(GrantCallTransitionService);
    grantCallRepository = module.get<Repository<GrantCall>>(getRepositoryToken(GrantCall));
    votingOutcomeService = module.get(VotingOutcomeService);
    workflowTransitionService = module.get<WorkflowTransitionService>(WorkflowTransitionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    let createDto: CreateGrantCallDto;
    let coordinatorUserMock: User;
    let mockProg: Partial<GrantProgram>;
    let mockStepDefs: Partial<WorkflowStepDefinition>[];
    let stepDefMap: Map<StageCode, WorkflowStepDefinition>;
    let mockSettings: Partial<GrantCallStageSetting>[];
    let mockRules: Partial<GrantDistributionRule>[];
    let mockClosedStepDef: Partial<WorkflowStepDefinition>;

    beforeEach(() => {
      createDto = mockCreateDto();
      coordinatorUserMock = mockUser();
      mockProg = mockGrantProgram();
      mockStepDefs = [mockWorkflowStepDef(10, StageCode.GC_OPEN_FOR_APPLICATIONS)];
      stepDefMap = new Map(mockStepDefs.map((def) => [def.code, def as WorkflowStepDefinition]));
      mockClosedStepDef = mockWorkflowStepDef(99, StageCode.GC_CLOSED);
      mockSettings = [mockGrantCallStageSetting(1, 10)];
      mockRules = [
        mockGrantDistributionRule(9, 1, 50),
        mockGrantDistributionRule(8, 2, 30),
        mockGrantDistributionRule(6, 3, 20),
      ];

      jest
        .spyOn(grantCallCreationService as any, 'getProgramAndStageDefinitions')
        .mockResolvedValue({ grantProgram: mockProg, stepDefMap: stepDefMap });
      mockedGenerateUniqueSlug.mockResolvedValue('new-grant-call-slug');

      jest.spyOn(grantCallCreationService as any, 'buildStageSettings').mockReturnValue(mockSettings);
      jest.spyOn(grantCallCreationService as any, 'buildDistributionRules').mockReturnValue(mockRules);
      mockWfStepDefRepoTransactional.findOneBy.mockResolvedValue(mockClosedStepDef);
      mockUserRepoTransactional.findOneBy.mockResolvedValue(coordinatorUserMock);
      mockGrantCallRepoTransactional.save.mockImplementation((gc) => Promise.resolve({ ...gc, id: 99 }));
    });

    it('should map URL fields to stage settings via stageUrl property', async () => {
      const urlDto = {
        screeningFormUrl: 'https://forms.clickup.com/screening',
        dueDiligenceFormUrl: 'https://forms.clickup.com/due-diligence',
        interviewSchedulingUrl: 'https://calendly.com/interview',
        townHallSchedulingUrl: 'https://calendly.com/town-hall',
      };

      const testDto = { ...createDto, ...urlDto };

      // Test each URL mapping from DTO to stage settings
      const screeningResult = grantCallCreationService['determineUrlProperty'](StageCode.GA_SCREENING, testDto);
      expect(screeningResult).toEqual({ stageUrl: urlDto.screeningFormUrl });

      const dueDiligenceResult = grantCallCreationService['determineUrlProperty'](StageCode.GA_DUE_DILIGENCE, testDto);
      expect(dueDiligenceResult).toEqual({ stageUrl: urlDto.dueDiligenceFormUrl });

      const interviewResult = grantCallCreationService['determineUrlProperty'](StageCode.GA_INTERVIEW, testDto);
      expect(interviewResult).toEqual({ stageUrl: urlDto.interviewSchedulingUrl });

      const townHallResult = grantCallCreationService['determineUrlProperty'](StageCode.GA_TOWN_HALL, testDto);
      expect(townHallResult).toEqual({ stageUrl: urlDto.townHallSchedulingUrl });
    });
  });

  describe('update', () => {
    let updateDto: GrantCallBaseDto;
    let existingSlug: string;
    let foundGrantCall: GrantCall;
    let mockStepDefs: WorkflowStepDefinition[];
    let stepDefMap: Map<StageCode, WorkflowStepDefinition>;
    let mockNewSettings: GrantCallStageSetting[];

    beforeEach(() => {
      updateDto = mockUpdateDto();
      existingSlug = 'existing-slug-456';
      foundGrantCall = createMockGrantCall(5, existingSlug, true);

      mockStepDefs = [
        mockWorkflowStepDef(10, StageCode.GC_OPEN_FOR_APPLICATIONS),
        mockWorkflowStepDef(20, StageCode.GA_SCREENING),
      ];
      stepDefMap = new Map(mockStepDefs.map((def) => [def.code, def]));
      mockNewSettings = [mockGrantCallStageSetting(3, 10), mockGrantCallStageSetting(4, 20)];

      mockGrantCallRepoTransactional.findOne.mockResolvedValue(foundGrantCall);
      mockGrantCallRepoTransactional.save.mockImplementation((gc) => Promise.resolve(gc));
      mockStageSettingRepoTransactional.remove.mockResolvedValue(undefined);
      mockProgramRepoTransactional.findOneBy.mockResolvedValue(foundGrantCall.grantProgram);

      jest
        .spyOn(grantCallCreationService as any, 'getProgramAndStageDefinitions')
        .mockResolvedValue({ grantProgram: foundGrantCall.grantProgram, stepDefMap: stepDefMap });
      jest.spyOn(grantCallCreationService as any, 'buildStageSettings').mockReturnValue(mockNewSettings);
    });

    it('should successfully update a grant call', async () => {
      const result = await service.update(existingSlug, updateDto);

      expect(result).toEqual({ grantCallSlug: existingSlug });

      expect(grantCallRepository.manager.transaction).toHaveBeenCalledTimes(1);

      expect(mockGrantCallRepoTransactional.findOne).toHaveBeenCalledTimes(1);
      expect(mockGrantCallRepoTransactional.findOne).toHaveBeenCalledWith({
        where: { grantCallSlug: existingSlug },
        relations: ['grantProgram', 'stageSettings', 'stageSettings.workflowStepDefinition'],
      });

      expect(mockGrantCallRepoTransactional.save).toHaveBeenCalledTimes(1);
      expect(mockGrantCallRepoTransactional.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: foundGrantCall.id,
          grantCallSlug: existingSlug,
          createdById: foundGrantCall.createdById,
          grantProgram: foundGrantCall.grantProgram,
          name: updateDto.name,
          description: updateDto.description,
          stageSettings: expect.any(Array),
        }),
      );
    });
  });

  describe('conditionallyAdvanceGrantCallStage', () => {
    const mockGrantCallSlug = 'a-slug';
    const mockGrantCallId = 123;
    const mockAppSourceStage = StageCode.GA_SCREENING;
    const mockAppStepId = 456;
    const mockAppTransitionResult = { transitionedCount: 3 };
    const mockUpdatedGcState = { stepDefinitionCode: StageCode.GC_COMMUNITY_VOTING } as any;

    let getGrantCallIdBySlugSpy: jest.SpyInstance;
    let deriveApplicationSourceStepIdSpy: jest.SpyInstance;

    beforeEach(() => {
      getGrantCallIdBySlugSpy = jest
        .spyOn(grantCallTransitionService as any, 'getGrantCallIdBySlug')
        .mockResolvedValue(mockGrantCallId);
      deriveApplicationSourceStepIdSpy = jest
        .spyOn(grantCallTransitionService as any, 'deriveApplicationSourceStepId')
        .mockResolvedValue(mockAppStepId);

      mockWorkflowTransitionService.bulkTransitionReadyApplicationsForCall.mockResolvedValue(mockAppTransitionResult);
      mockWorkflowTransitionService.transitionSingleStateToNextStep.mockResolvedValue(mockUpdatedGcState);
    });

    it('should call its dependencies in order and return the correct structure', async () => {
      const result = await (grantCallTransitionService as any).conditionallyAdvanceGrantCallStage(
        mockGrantCallSlug,
        mockAppSourceStage,
      );

      expect(getGrantCallIdBySlugSpy).toHaveBeenCalledWith(mockGrantCallSlug);
      expect(deriveApplicationSourceStepIdSpy).toHaveBeenCalledWith(mockGrantCallId, mockAppSourceStage);
      expect(mockWorkflowTransitionService.bulkTransitionReadyApplicationsForCall).toHaveBeenCalledWith(
        mockGrantCallId,
        mockAppStepId,
        mockTransactionalEntityManager,
      );
      expect(mockWorkflowTransitionService.transitionSingleStateToNextStep).toHaveBeenCalledWith(
        WorkflowEntityType.CALL,
        mockGrantCallId,
        mockTransactionalEntityManager,
      );
      expect(result).toEqual({
        transitionedCount: mockAppTransitionResult.transitionedCount,
        grantCallNewStage: mockUpdatedGcState.stepDefinitionCode,
      });
    });
  });

  describe('Advance Grant Call Stages', () => {
    let conditionallyAdvanceGrantCallStageSpy: jest.SpyInstance;
    const mockGrantCallSlug = 'test-call-slug';
    const mockConditionalResponse: ConditionalBulkTransitionResponseDto = {
      transitionedCount: 5,
      transitionedApplicationIds: [1, 2, 3],
      grantCallNewStage: GrantCallStageCode.COMMUNITY_VOTING,
    };

    beforeEach(() => {
      conditionallyAdvanceGrantCallStageSpy = jest
        .spyOn(grantCallTransitionService as any, 'conditionallyAdvanceGrantCallStage')
        .mockResolvedValue(mockConditionalResponse);
    });

    describe('advanceGrantCallFromScreening', () => {
      it('should call conditionallyAdvanceGrantCallStage with GA_SCREENING and return its result', async () => {
        const result = await service.advanceGrantCallFromScreening(mockGrantCallSlug);

        expect(conditionallyAdvanceGrantCallStageSpy).toHaveBeenCalledTimes(1);
        expect(conditionallyAdvanceGrantCallStageSpy).toHaveBeenCalledWith(mockGrantCallSlug, StageCode.GA_SCREENING);
        expect(result).toEqual(mockConditionalResponse);
      });
    });

    describe('advanceGrantCallFromOnboarding', () => {
      it('should call conditionallyAdvanceGrantCallStage with GA_TOWN_HALL and return its result', async () => {
        const result = await service.advanceGrantCallFromOnboarding(mockGrantCallSlug);

        expect(conditionallyAdvanceGrantCallStageSpy).toHaveBeenCalledTimes(1);
        expect(conditionallyAdvanceGrantCallStageSpy).toHaveBeenCalledWith(mockGrantCallSlug, StageCode.GA_TOWN_HALL);
        expect(result).toEqual(mockConditionalResponse);
      });
    });

    describe('advanceGrantCallFromCommunityVoting', () => {
      let getGrantCallWithGrantProgramBySlugSpy: jest.SpyInstance;
      let deriveApplicationSourceStepIdSpy: jest.SpyInstance;

      const grantCallSlug = 'test-community-vote-call';
      const grantCallId = 1;
      const applicationSourceStepId = 10;

      const mockVotingCalcResult: GrantCallVotingSummaryDto = {
        categorizationResult: {
          qualifiedApplications: [
            {
              id: 201,
              createdBy: {
                id: 123,
                email: '<EMAIL>',
              },
              title: 'Mock application #1',
              votingTopicId: '0.0.012345',
              finalVotingTopicId: '0.0.491231',
              workflowStateId: 201,
              actionTopicId: '0.0.111123',
              currentStageNetScore: 2,
              communityVotingNetScoreForTieBreak: 0,
              meetsIndividualRule: true,
            },
            {
              id: 202,
              createdBy: {
                id: 123,
                email: '<EMAIL>',
              },
              title: 'Mock application #1',
              votingTopicId: '0.0.777777',
              finalVotingTopicId: '0.0.888888',
              workflowStateId: 202,
              actionTopicId: '0.0.999999',
              currentStageNetScore: 1,
              communityVotingNetScoreForTieBreak: 0,
              meetsIndividualRule: true,
            },
          ],
          rejectedApplications: [
            {
              id: 203,
              createdBy: {
                id: 123,
                email: '<EMAIL>',
              },
              title: 'Mock application #1',
              votingTopicId: '0.0.111111',
              finalVotingTopicId: '0.0.222222',
              workflowStateId: 203,
              actionTopicId: '0.0.333333',
              currentStageNetScore: -5,
              communityVotingNetScoreForTieBreak: 0,
              meetsIndividualRule: false,
            },
          ],
        },
        allAppScoresMap: [
          {
            appId: 201,
            voteType: VotingType.COMMUNITY_VOTING,
            inFavor: 3,
            against: 1,
          },
          {
            appId: 202,
            voteType: VotingType.COMMUNITY_VOTING,
            inFavor: 2,
            against: 1,
          },
          {
            appId: 203,
            voteType: VotingType.COMMUNITY_VOTING,
            inFavor: 0,
            against: 5,
          },
        ].reduce((scoresMap, record) => {
          const appScores = scoresMap.get(record.appId) ?? {};

          appScores[record.voteType] = {
            inFavor: record.inFavor,
            against: record.against,
            netScore: record.inFavor - record.against,
          };

          scoresMap.set(record.appId, appScores);

          return scoresMap;
        }, new Map() as ApplicationScoresMap),
      };

      beforeEach(() => {
        getGrantCallWithGrantProgramBySlugSpy = jest.spyOn(
          grantCallTransitionService as any,
          'getGrantCallWithGrantProgramBySlug',
        );
        deriveApplicationSourceStepIdSpy = jest.spyOn(
          grantCallTransitionService as any,
          'deriveApplicationSourceStepId',
        );

        votingOutcomeService.processVotingResultsForGrantCall.mockReset();
        mockWorkflowTransitionService.bulkSetStatusForWorkflowStateIds.mockReset();
        mockWorkflowTransitionService.bulkTransitionReadyApplicationsForCall.mockReset();
        mockWorkflowTransitionService.transitionSingleStateToNextStep.mockReset();

        getGrantCallWithGrantProgramBySlugSpy.mockResolvedValue({
          id: grantCallId,
          grantCallSlug: mockGrantCallSlug,
          grantProgram: mockGrantProgram,
          workflowState: {
            currentStepDefinition: {
              code: StageCode.GC_FINAL_COMMUNITY_VOTING,
            },
          },
        });
        deriveApplicationSourceStepIdSpy.mockResolvedValue(applicationSourceStepId);
        votingOutcomeService.processVotingResultsForGrantCall.mockResolvedValue(mockVotingCalcResult);

        mockWorkflowTransitionService.bulkSetStatusForWorkflowStateIds.mockImplementation(async (ids) => ids.length);

        mockWorkflowTransitionService.bulkTransitionReadyApplicationsForCall.mockResolvedValue({
          transitionedCount:
            mockVotingCalcResult.categorizationResult.rejectedApplications.length +
            mockVotingCalcResult.categorizationResult.qualifiedApplications.length,
        });

        mockWorkflowTransitionService.transitionSingleStateToNextStep.mockResolvedValue({
          stepDefinitionCode: GrantCallStageCode.ONBOARDING,
          status: WorkflowStatus.IN_PROGRESS,
        });
      });

      it('should orchestrate voting outcome processing and stage advancement', async () => {
        const result = await service.advanceGrantCallFromCommunityVoting(grantCallSlug);

        expect(mockDataSource.manager.transaction).toHaveBeenCalledTimes(1);

        expect(getGrantCallWithGrantProgramBySlugSpy).toHaveBeenCalledWith(grantCallSlug);
        expect(votingOutcomeService.processVotingResultsForGrantCall).toHaveBeenCalledWith(
          grantCallId,
          VotingType.COMMUNITY_VOTING,
        );

        expect(mockWorkflowTransitionService.bulkSetStatusForWorkflowStateIds).toHaveBeenCalledWith(
          mockVotingCalcResult.categorizationResult.rejectedApplications.map((a) => a.workflowStateId),
          WorkflowStatus.REJECTED,
          mockEntityManager,
        );

        expect(mockWorkflowTransitionService.bulkSetStatusForWorkflowStateIds).toHaveBeenCalledWith(
          mockVotingCalcResult.categorizationResult.qualifiedApplications.map((a) => a.workflowStateId),
          WorkflowStatus.READY_FOR_NEXT_STEP,
          mockEntityManager,
        );

        expect(deriveApplicationSourceStepIdSpy).toHaveBeenCalledWith(grantCallId, StageCode.GA_QUALIFICATION);

        expect(mockWorkflowTransitionService.bulkTransitionReadyApplicationsForCall).toHaveBeenCalledWith(
          grantCallId,
          applicationSourceStepId,
          mockEntityManager,
        );

        expect(mockWorkflowTransitionService.transitionSingleStateToNextStep).toHaveBeenCalledWith(
          WorkflowEntityType.CALL,
          grantCallId,
          mockEntityManager,
        );

        expect(result).toEqual({
          approvedApplicationsCount: mockVotingCalcResult.categorizationResult.qualifiedApplications.length,
          rejectedApplicationsCount: mockVotingCalcResult.categorizationResult.rejectedApplications.length,
          grantCallNewStage: GrantCallStageCode.ONBOARDING,
        });
      });
    });
  });

  describe('finalizeGrantCall', () => {
    const mockGrantCallSlug = 'test-grant-call';
    const mockGrantCall = {
      id: 1,
      grantCallSlug: mockGrantCallSlug,
      workflowState: {
        currentStepDefinition: {
          isTerminal: false,
          name: 'GC_OPEN',
        },
        status: WorkflowStatus.IN_PROGRESS,
      },
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should finalize a grant call successfully', async () => {
      const mockResponse: SingleTransitionResponseDto = {
        status: WorkflowStatus.CLOSED,
        stepDefinitionCode: StageCode.GC_FINALIZED,
      };

      (grantCallRepository.findOne as jest.Mock).mockResolvedValue(mockGrantCall);
      (workflowTransitionService.updateStateStatus as jest.Mock).mockResolvedValue(mockResponse);
      (workflowTransitionService.setSpecificStage as jest.Mock).mockResolvedValue(mockResponse);

      await service.finalizeGrantCall(mockGrantCallSlug);

      expect(workflowTransitionService.updateStateStatus).toHaveBeenCalledWith(
        WorkflowEntityType.CALL,
        mockGrantCall.id,
        WorkflowStatus.CLOSED,
        expect.any(Object),
      );
      expect(workflowTransitionService.setSpecificStage).toHaveBeenCalledWith(
        WorkflowEntityType.CALL,
        mockGrantCall.id,
        StageCode.GC_FINALIZED,
        expect.any(Object),
      );
    });

    it('should throw NotFoundException when grant call not found', async () => {
      (grantCallRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(service.finalizeGrantCall(mockGrantCallSlug)).rejects.toThrow(
        `Grant Call with slug "${mockGrantCallSlug}" not found.`,
      );
    });

    it('should throw UnprocessableEntityException when grant call is in terminal state', async () => {
      const terminalGrantCall = {
        ...mockGrantCall,
        workflowState: {
          ...mockGrantCall.workflowState,
          currentStepDefinition: {
            isTerminal: true,
            name: 'GC_FINALIZED',
          },
        },
      };

      (grantCallRepository.findOne as jest.Mock).mockResolvedValue(terminalGrantCall);

      await expect(service.finalizeGrantCall(mockGrantCallSlug)).rejects.toThrow(
        `Cannot change Grant Call status from terminal step 'GC_FINALIZED'.`,
      );
    });

    it('should throw UnprocessableEntityException when grant call is in REJECTED state', async () => {
      const rejectedGrantCall = {
        ...mockGrantCall,
        workflowState: {
          ...mockGrantCall.workflowState,
          status: WorkflowStatus.REJECTED,
        },
      };

      (grantCallRepository.findOne as jest.Mock).mockResolvedValue(rejectedGrantCall);

      await expect(service.finalizeGrantCall(mockGrantCallSlug)).rejects.toThrow(
        `Cannot change Grant Call status from ${WorkflowStatus.REJECTED} status.`,
      );
    });

    it('should throw UnprocessableEntityException when grant call is in WITHDRAWN state', async () => {
      const withdrawnGrantCall = {
        ...mockGrantCall,
        workflowState: {
          ...mockGrantCall.workflowState,
          status: WorkflowStatus.WITHDRAWN,
        },
      };

      (grantCallRepository.findOne as jest.Mock).mockResolvedValue(withdrawnGrantCall);

      await expect(service.finalizeGrantCall(mockGrantCallSlug)).rejects.toThrow(
        `Cannot change Grant Call status from ${WorkflowStatus.WITHDRAWN} status.`,
      );
    });

    it('should handle workflow transition errors', async () => {
      const stageError = new Error('Workflow transition failed');
      (grantCallRepository.findOne as jest.Mock).mockResolvedValue(mockGrantCall);
      (workflowTransitionService.updateStateStatus as jest.Mock).mockRejectedValue(stageError);

      await expect(service.finalizeGrantCall(mockGrantCallSlug)).rejects.toThrow(stageError);
    });
  });
});
