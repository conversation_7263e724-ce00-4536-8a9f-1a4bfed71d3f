import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GrantCall } from './entities/grant-call.entity';
import { CreateGrantCallDto, UpdateGrantCallDto, GrantCallDetailResponseDto } from './dto';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { GrantCallCreationService } from './services/grant-call-creation.service';
import { GrantCallTransitionService } from './services/grant-call-transition.service';
import { STAGE_URL_DTO_KEYS } from './grant-call.constants';

@Injectable()
export class GrantCallService {
  private readonly logger = new Logger(GrantCallService.name);

  constructor(
    @InjectRepository(GrantCall)
    private readonly grantCallRepository: Repository<GrantCall>,
    private readonly grantCallCreationService: GrantCallCreationService,
    private readonly grantCallTransitionService: GrantCallTransitionService,
  ) {}

  async create(createGrantCallDto: CreateGrantCallDto, requestingUserId: number): Promise<{ grantCallSlug: string }> {
    return this.grantCallCreationService.create(createGrantCallDto, requestingUserId);
  }

  async update(grantCallSlug: string, updateDto: UpdateGrantCallDto): Promise<GrantCallDetailResponseDto> {
    return this.grantCallRepository.manager.transaction(async (transactionalEntityManager) => {
      const grantCallRepo = transactionalEntityManager.getRepository(GrantCall);

      const grantCall = await grantCallRepo.findOne({
        where: { grantCallSlug },
        relations: ['grantProgram', 'stageSettings', 'stageSettings.workflowStepDefinition'],
      });

      if (!grantCall) {
        throw new NotFoundException(`Grant Call with slug '${grantCallSlug}' not found`);
      }

      grantCall.name = updateDto.name;
      grantCall.description = updateDto.description;

      if (grantCall.stageSettings && grantCall.stageSettings.length > 0) {
        for (const stageSetting of grantCall.stageSettings) {
          const stageCode = stageSetting.workflowStepDefinition.code;
          const dtoKey = STAGE_URL_DTO_KEYS[stageCode];

          if (dtoKey && updateDto[dtoKey] !== undefined) {
            stageSetting.stageUrl = updateDto[dtoKey];
          }
        }
      }

      await grantCallRepo.save(grantCall);

      return {
        grantCallSlug: grantCall.grantCallSlug,
      };
    });
  }

  async transitionGrantCallToNextStep(grantCallSlug: string): Promise<SingleTransitionResponseDto> {
    return this.grantCallTransitionService.transitionGrantCallToNextStep(grantCallSlug);
  }

  async bulkTransitionReadyApplicationsAtDueDiligence(grantCallSlug: string) {
    return this.grantCallTransitionService.bulkTransitionReadyApplicationsAtDueDiligence(grantCallSlug);
  }

  advanceGrantCallFromScreening(grantCallSlug: string) {
    return this.grantCallTransitionService.advanceGrantCallFromScreening(grantCallSlug);
  }

  advanceGrantCallFromOnboarding(grantCallSlug: string) {
    return this.grantCallTransitionService.advanceGrantCallFromOnboarding(grantCallSlug);
  }

  async advanceGrantCallFromCommunityVoting(grantCallSlug: string) {
    return this.grantCallTransitionService.advanceGrantCallFromCommunityVoting(grantCallSlug);
  }

  processFinalApplicationsAndFinalizeCall(grantCallSlug: string) {
    return this.grantCallTransitionService.processFinalApplicationsAndFinalizeCall(grantCallSlug);
  }

  async finalizeGrantCall(grantCallSlug: string): Promise<SingleTransitionResponseDto> {
    return this.grantCallTransitionService.finalizeGrantCall(grantCallSlug);
  }
}
