import { InjectRepository } from '@nestjs/typeorm';
import { GrantCall } from './entities/grant-call.entity';
import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { GrantCallWithApplicationsCountDto } from './dto/grant-call-with-applications-count.dto';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';

@Injectable()
export class GrantCallRepository {
  constructor(
    @InjectRepository(GrantCall)
    private readonly repository: Repository<GrantCall>,
  ) {}

  /**
   * Fetches a single GrantCall by its slug, including related data.
   */
  async findOneBySlug(slug: string): Promise<GrantCallWithApplicationsCountDto> {
    return (
      this.repository
        .createQueryBuilder('grantCall')
        // Filter by the slug parameter
        .where('grantCall.grantCallSlug = :slug', { slug })

        // Core GrantCall fields
        .select([
          'grantCall.id',
          'grantCall.name',
          'grantCall.description',
          'grantCall.grantCallSlug',
          'grantCall.businessCategory',
          'grantCall.totalGrantAmount',
          'grantCall.categories',
          'grantCall.updatedAt',
        ])

        // Join the user who created this grant call
        .leftJoin('grantCall.createdBy', 'createdBy')
        .addSelect(['createdBy.id', 'createdBy.displayName'])

        // Join the associated grant program
        .leftJoin('grantCall.grantProgram', 'grantProgram')
        .addSelect(['grantProgram.id', 'grantProgram.name', 'grantProgram.grantProgramSlug'])

        // Join the current workflow state
        .leftJoin('grantCall.workflowState', 'workflowState')
        .addSelect([
          'workflowState.id',
          'workflowState.currentStepTransitionedAt',
          'workflowState.currentStepEndsAt',
          'workflowState.currentStepDefinition',
        ])
        // Join details of the current step definition
        .leftJoin('workflowState.currentStepDefinition', 'currentStepDefinition')
        .addSelect([
          'currentStepDefinition.code',
          'currentStepDefinition.transitionType',
          'currentStepDefinition.isTerminal',
          'currentStepDefinition.sequenceNumber',
        ])
        // And the workflow template and its steps
        .leftJoinAndSelect('workflowState.workflowTemplate', 'workflowTemplate')
        .leftJoin('workflowTemplate.steps', 'steps')
        .addSelect(['steps.id', 'steps.sequenceNumber'])

        // Join stage settings for this grant call
        .leftJoin('grantCall.stageSettings', 'stageSettings')
        .addSelect([
          'stageSettings.id',
          'stageSettings.startDate',
          'stageSettings.endDate',
          'stageSettings.durationSeconds',
          'stageSettings.stageUrl',
        ])
        // And the step-definition for each setting
        .leftJoin('stageSettings.workflowStepDefinition', 'workflowStepDefinition')
        .addSelect('workflowStepDefinition.code')

        // Join distribution rules, ordered by rank
        .leftJoin('grantCall.distributionRules', 'distributionRules')
        .addSelect(['distributionRules.id', 'distributionRules.value'])
        .orderBy('distributionRules.rank', 'ASC')

        // Count grant applications of this grant call and map to grantApplicationsCount
        .loadRelationCountAndMap('grantCall.grantApplicationsCount', 'grantCall.grantApplications')

        // Count only those grant applications whose status = IN_PROGRESS
        .loadRelationCountAndMap(
          'grantCall.inProgressGrantApplicationsCount',
          'grantCall.grantApplications',
          'grantApplications',
          (qb) =>
            qb
              .innerJoin('grantApplications.workflowState', 'appWorkflowStateForCount')
              .where('appWorkflowStateForCount.status = :readyStatus', {
                readyStatus: WorkflowStatus.IN_PROGRESS,
              }),
        )

        .getOne() as unknown as GrantCallWithApplicationsCountDto
    );
  }
}
