import { AuthModule } from '../auth/auth.module';
import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { GrantApplicationModule } from '../grant-application/grant-application.module';
import { GrantCall } from './entities/grant-call.entity';
import { Grant<PERSON>allController } from './grant-call.controller';
import { GrantCallMapper } from './grant-call.mapper';
import { GrantCallRepository } from './grant-call.repository';
import { GrantCallService } from './grant-call.service';
import { GrantProgram } from '../grant-program/entities/grant-program.entity';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VotingOutcomeModule } from '../voting-outcome/voting-outcome.module';
import { User } from '../auth/entities/user.entity';
import { VotesModule } from '../votes/votes.module';
import { WorkflowService } from '../workflow/workflow.service';
import { WorkflowState } from '../workflow/entities/workflow-state.entity';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';
import { WorkflowTemplate } from '../workflow/entities/workflow-template.entity';
import { WorkflowTransitionService } from '../workflow/workflow-transition.service';
import { GrantCallCreationService } from './services/grant-call-creation.service';
import { GrantCallTransitionService } from './services/grant-call-transition.service';
import { GrantCallQueryService } from './services/grant-call-query.service';
import { NotificationsModule } from '../notifications/notifications.module';

@Module({
  imports: [
    AuthModule,
    GrantApplicationModule,
    NotificationsModule,
    VotingOutcomeModule,
    VotesModule,
    TypeOrmModule.forFeature([
      GrantCall,
      WorkflowStepDefinition,
      WorkflowTemplate,
      WorkflowState,
      GrantProgram,
      GrantApplication,
      User,
    ]),
  ],
  controllers: [GrantCallController],
  providers: [
    GrantCallService,
    GrantCallMapper,
    WorkflowTransitionService,
    WorkflowService,
    GrantCallRepository,
    GrantCallCreationService,
    GrantCallTransitionService,
    GrantCallQueryService,
  ],
  exports: [GrantCallMapper],
})
export class GrantCallModule {}
