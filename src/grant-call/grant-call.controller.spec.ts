import { Test, TestingModule } from '@nestjs/testing';
import { GrantCallController } from './grant-call.controller';
import { GrantCallService } from './grant-call.service';
import { GrantApplicationService } from '../grant-application/grant-application.service';
import { CreateGrantCallDto, UpdateGrantCallDto } from './dto';
import { WorkflowStatus } from '../workflow/enums/workflow-status.enum';
import { SingleTransitionResponseDto } from '../workflow/dto/single-transition-response.dto';
import { StageCode } from '../workflow/enums/stage-code.enum';
import { UnprocessableEntityException } from '@nestjs/common';
import { FindOneGrantCallResponseDto } from './dto/find-one-grant-call.response.dto';
import { Role } from '../auth/role.enum';
import { BusinessCategory } from './enums/business-category.enum';
import { GrantCategory } from './enums/grant-category.enum';
import { GrantCallQueryService } from './services/grant-call-query.service';

describe('GrantCallController', () => {
  let controller: GrantCallController;
  let grantCallService: jest.Mocked<GrantCallService>;
  let grantCallQueryService: jest.Mocked<GrantCallQueryService>;

  const mockGrantCallService = {
    create: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    finalizeGrantCall: jest.fn(),
  };

  const mockGrantCallQueryService = {
    findOne: jest.fn(),
  };

  const mockGrantApplicationService = {
    findGrantApplications: jest.fn(),
  };

  const baseMockResponse: FindOneGrantCallResponseDto = {
    name: 'Test Grant Call',
    description: 'Test Description',
    businessCategory: BusinessCategory.STARTUP,
    categories: [GrantCategory.SUSTAINABILITY],
    totalGrantAmount: 100000,
    openForApplicationStart: '2024-01-01T00:00:00Z',
    openForApplicationEnd: '2024-01-15T23:59:59Z',
    communityVotingTime1: 604800,
    communityVotingTime2: 259200,
    grantDistribution: [50, 30, 20],
    screeningFormUrl: 'https://forms.clickup.com/12345/f/abc-123',
    dueDiligenceFormUrl: 'https://sharing.clickup.com/12345/f/def-456',
    grantCallSlug: 'test-grant-call',
    createdBy: {
      id: 1,
      displayName: 'Test User',
    },
    status: StageCode.GP_OPEN,
    updatedAt: new Date(),
    grantProgram: {
      name: 'Test Program',
      grantProgramSlug: 'test-program',
    },
    isAbleToChangeStageManually: true,
    workflowState: {
      id: 1,
      currentStepTransitionedAt: new Date(),
      currentStepEndsAt: new Date(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GrantCallController],
      providers: [
        {
          provide: GrantCallService,
          useValue: mockGrantCallService,
        },
        {
          provide: GrantCallQueryService,
          useValue: mockGrantCallQueryService,
        },
        {
          provide: GrantApplicationService,
          useValue: mockGrantApplicationService,
        },
      ],
    }).compile();

    controller = module.get<GrantCallController>(GrantCallController);
    grantCallService = module.get(GrantCallService);
    grantCallQueryService = module.get(GrantCallQueryService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const mockDto = new CreateGrantCallDto();
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      emailVerified: true,
      phoneNumber: null,
      isPhoneVerified: null,
      otp: null,
      otpExpiresAt: null,
      role: Role.COMMUNITY_MEMBER,
    };
    const mockResponse = {
      grantCallSlug: 'test-grant-call',
    };

    it('should create a grant call', async () => {
      mockGrantCallService.create.mockResolvedValue(mockResponse);

      const result = await controller.create(mockDto, mockUser);

      expect(result).toEqual(mockResponse);
      expect(mockGrantCallService.create).toHaveBeenCalledWith(mockDto, mockUser.id);
    });
  });

  describe('findOne', () => {
    const mockSlug = 'test-grant-call';
    const mockResponse: FindOneGrantCallResponseDto = {
      ...baseMockResponse,
      grantCallSlug: mockSlug,
    };

    it('should return a grant call', async () => {
      grantCallQueryService.findOne.mockResolvedValue(mockResponse);

      const result = await controller.findOne(mockSlug);

      expect(result).toEqual(mockResponse);
      expect(grantCallQueryService.findOne).toHaveBeenCalledWith(mockSlug);
    });
  });

  it('should have ResponseByRole decorator applied', () => {
    const findOneMethod = Reflect.getMetadata('responseByRole', controller.findOne);
    expect(findOneMethod).toBe(FindOneGrantCallResponseDto);
  });

  describe('update', () => {
    const mockSlug = 'test-grant-call';
    const mockDto = new UpdateGrantCallDto();
    const mockResponse: FindOneGrantCallResponseDto = {
      ...baseMockResponse,
      name: 'Updated Grant Call',
      description: 'Updated Description',
      grantCallSlug: mockSlug,
    };

    it('should update a grant call', async () => {
      grantCallService.update.mockResolvedValue(mockResponse);

      const result = await controller.update(mockSlug, mockDto);

      expect(result).toEqual(mockResponse);
      expect(grantCallService.update).toHaveBeenCalledWith(mockSlug, mockDto);
    });
  });

  describe('finalizeGrantCall', () => {
    const mockSlug = 'test-grant-call';
    const mockResponse: SingleTransitionResponseDto = {
      status: WorkflowStatus.CLOSED,
      stepDefinitionCode: StageCode.GC_FINALIZED,
    };

    it('should successfully finalize grant call', async () => {
      grantCallService.finalizeGrantCall.mockResolvedValue(mockResponse);

      const result = await controller.finalizeGrantCall(mockSlug);

      expect(result).toEqual(mockResponse);
      expect(grantCallService.finalizeGrantCall).toHaveBeenCalledWith(mockSlug);
    });

    it('should throw UnprocessableEntityException when grant call not found', async () => {
      grantCallService.finalizeGrantCall.mockRejectedValue(
        new UnprocessableEntityException(`Grant Call with slug "non-existent-slug" not found.`),
      );

      await expect(controller.finalizeGrantCall('non-existent-slug')).rejects.toThrow(
        `Grant Call with slug "non-existent-slug" not found.`,
      );
    });

    it('should throw UnprocessableEntityException when grant call is in terminal state', async () => {
      grantCallService.finalizeGrantCall.mockRejectedValue(
        new UnprocessableEntityException(`Cannot change Grant Call status from terminal step 'GC_FINALIZED'.`),
      );

      await expect(controller.finalizeGrantCall(mockSlug)).rejects.toThrow(
        `Cannot change Grant Call status from terminal step 'GC_FINALIZED'.`,
      );
    });

    it('should throw UnprocessableEntityException when grant call is in REJECTED state', async () => {
      const workflowError = new UnprocessableEntityException(
        `Cannot change Grant Call status from ${WorkflowStatus.REJECTED} status.`,
      );
      grantCallService.finalizeGrantCall.mockRejectedValue(workflowError);

      await expect(controller.finalizeGrantCall(mockSlug)).rejects.toThrow(workflowError);
    });
  });
});
