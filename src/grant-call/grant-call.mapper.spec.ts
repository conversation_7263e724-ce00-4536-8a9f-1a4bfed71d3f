import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { <PERSON><PERSON>allMapper } from './grant-call.mapper';
import { GrantApplication } from '../grant-application/entities/grant-application.entity';
import { GrantCallStageSetting } from './entities/grant-call-stage-setting.entity';
import { StageCode } from '../workflow/enums/stage-code.enum';
import { WorkflowStepDefinition } from '../workflow/entities/workflow-step-definition.entity';

describe('GrantCallMapper', () => {
  let mapper: GrantCallMapper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GrantCallMapper,
        {
          provide: getRepositoryToken(GrantApplication),
          useValue: {
            count: jest.fn().mockResolvedValue(0),
          },
        },
      ],
    }).compile();

    mapper = module.get<GrantCallMapper>(GrantCallMapper);
  });



  describe('extractUrlsFromSettings', () => {
    it('should extract URLs from stage settings', () => {
      const stageSettings: GrantCallStageSetting[] = [
        createStageSetting(1, StageCode.GA_SCREENING, 'https://forms.clickup.com/screening'),
        createStageSetting(2, StageCode.GA_DUE_DILIGENCE, 'https://forms.clickup.com/due-diligence'),
        createStageSetting(3, StageCode.GA_INTERVIEW, 'https://calendly.com/interview'),
        createStageSetting(4, StageCode.GA_TOWN_HALL, 'https://calendly.com/town-hall'),
        createStageSetting(5, StageCode.GC_OPEN_FOR_APPLICATIONS, null),
      ];

      const result = (mapper as any).extractUrlsFromSettings(stageSettings);

      expect(result).toEqual({
        screeningFormUrl: 'https://forms.clickup.com/screening',
        dueDiligenceFormUrl: 'https://forms.clickup.com/due-diligence',
        interviewSchedulingUrl: 'https://calendly.com/interview',
        townHallSchedulingUrl: 'https://calendly.com/town-hall',
      });
    });

    it('should handle missing stage settings', () => {
      const stageSettings: GrantCallStageSetting[] = [
        createStageSetting(1, StageCode.GA_SCREENING, 'https://forms.clickup.com/screening'),
        createStageSetting(3, StageCode.GA_INTERVIEW, null),
        createStageSetting(5, StageCode.GC_OPEN_FOR_APPLICATIONS, null),
      ];

      const result = (mapper as any).extractUrlsFromSettings(stageSettings);

      expect(result).toEqual({
        screeningFormUrl: 'https://forms.clickup.com/screening',
        dueDiligenceFormUrl: null,
        interviewSchedulingUrl: null,
        townHallSchedulingUrl: null,
      });
    });

    it('should handle empty stage settings array', () => {
      const result = (mapper as any).extractUrlsFromSettings([]);

      expect(result).toEqual({
        screeningFormUrl: null,
        dueDiligenceFormUrl: null,
        interviewSchedulingUrl: null,
        townHallSchedulingUrl: null,
      });
    });
  });


});

function createStageSetting(id: number, code: StageCode, stageUrl: string | null): GrantCallStageSetting {
  const stepDefinition = {
    id,
    code,
  } as WorkflowStepDefinition;

  return {
    id,
    workflowStepDefinition: stepDefinition,
    stageUrl,
  } as GrantCallStageSetting;
}
