import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { catchError, firstValueFrom, map, throwError } from 'rxjs';

/**
 * Service for estimating Hedera Consensus transaction fees
 * Follows the Hedera Fees calculator:
 *  @link https://hedera.com/fees
 * Constant values for calculation sourced from the hedera-fee-tool-js repo:
 *  @link https://github.com/hashgraph/hedera-fee-tool-js
 */
@Injectable()
export class HederaEstimateTransactionFeeService {
  constructor(
    private readonly configService: ConfigService,
    private httpService: HttpService,
  ) {}

  /**
   * On-chain size constants (see Constants.json):
   * @link https://github.com/hashgraph/hedera-fee-tool-js/blob/master/src/resources/Constants.json
   */
  private readonly constants = {
    BASE_TX_SIZE: 76,
    SIG_SIZE: 64,
    TOPIC_ID_SIZE: 24,
    BASE_TXRESPONSE_SIZE: 4,
    BASE_RECEIPT_SIZE: 36,
    RUNNING_HASH_SIZE: 48,
    SEQUENCE_NUMBER_SIZE: 8,
    MINUTES_3: 180,
    HOURS_1: 3600,
    BASE_RECORD_SIZE: 132,
  };

  /**
   * DEFAULT schedule coefficients for ConsensusSubmitMessage
   * From typedFeeSchedules.json:
   *  @link https://github.com/hashgraph/hedera-fee-tool-js/blob/master/src/resources/typedFeeSchedules.json
   * See Hedera fee model explanation:
   *  @link https://hedera.com/blog/hedera-fee-model-for-transactions-queries
   * Todo: in future, fetch live coefficients via on-ledger file 0.0.111:
   *    - Fetching from this on-ledger file ensures the service uses the official, network-published fee schedule,
   *    - keeping calculations in sync with dynamic fee model updates without requiring manual constant changes.
   *    - The file 0.0.111 is published and maintained on-ledger by Hedera,
   *    - ensuring decentralized and verifiable fee schedules are available to all clients.
   *    - It has an expiration timestamp and the fee schedule rarely changes; you can cache its contents in your database
   *    - and only re-fetch when the expiration time has passed, reducing the number of FileGetContentsQuery calls.
   *
   *   const client = Client.forTestnet();
   *   const contents = await new FileGetContentsQuery()
   *     .setFileId(FileId.fromString('0.0.111'))
   *     .execute(client);
   *   const schedules = FeeSchedules.fromBytes(contents).current;
   *   const entry = schedules.transactionFeeScheduleList
   *     .find(e => e.hederaFunctionality.toString() === 'ConsensusSubmitMessage');
   *   await client.close();
   *   this.scheduleCoefficients = entry.feeData; // node, network, service maps
   */
  private readonly scheduleCoefficients = {
    node: { constant: 9248442, bpt: 14785, vpt: 36963719, rbh: 10, bpr: 14785 },
    network: { constant: 147975076, bpt: 236568, vpt: 591419509, rbh: 158 },
    service: { constant: 147975076, rbh: 158 },
  };

  /**
   * Estimates the USD and HBAR fee for a Hedera ConsensusSubmitMessage transaction.
   * @param message The message payload
   * @param memo The memo text
   * @param numSigsPayer Number of payer signatures
   * @param numSigsTotal Total number of signatures
   */
  async estimateGasFee(
    message: string,
    memo: string,
    numSigsPayer = 1,
    numSigsTotal = 1,
  ): Promise<{ usd: number; hbar: number }> {
    /**
     * Fetches live cents-per-HBAR rate from Mirror Node.
     *  - The exchange rate response includes an expiration time and typically updates hourly.
     *  - Cache the rate in your database and re-fetch only after it expires to reduce Mirror Node requests.
     */
    const exchangeRate = await firstValueFrom(
      this.httpService.get(`${this.configService.get('MIRROR_NODE_URL')}/api/v1/network/exchangerate`).pipe(
        catchError((error) => {
          return throwError(
            () => new HttpException(`Failed to fetch exchangerate: ${error.message}`, HttpStatus.FAILED_DEPENDENCY),
          );
        }),
        map((response) => response.data),
      ),
    );
    const centsPerHbar = exchangeRate.current_rate.cent_equivalent / exchangeRate.current_rate.hbar_equivalent;

    // Compute message and memo sizes in bytes
    const memoSize = Buffer.byteLength(memo);
    const msgSize = Buffer.byteLength(message);

    /**
     * Usage metrics per `typedUsageFormulas.json` > ConsensusSubmitMessage.DEFAULT:
     * @link https://github.com/hashgraph/hedera-fee-tool-js/blob/master/src/resources/typedUsageFormulas.json
     *    node.bpt = BASE_TX_SIZE + memoSize + (numSigsTotal * SIG_SIZE) + TOPIC_ID_SIZE + topicMessageSize
     *    node.vpt = numSigsPayer
     *    node.bpr = BASE_TXRESPONSE_SIZE
     *    network.bpt = same bpt
     *    network.vpt = numSigsTotal
     *    network.rbh = (BASE_RECEIPT_SIZE + RUNNING_HASH_SIZE + SEQUENCE_NUMBER_SIZE) * (MINUTES_3 / HOURS_1)
     *    service.rbh = ((BASE_RECORD_SIZE + memoSize) * MINUTES_3) / HOURS_1
     */
    const bpt =
      this.constants.BASE_TX_SIZE +
      memoSize +
      numSigsTotal * this.constants.SIG_SIZE +
      this.constants.TOPIC_ID_SIZE +
      msgSize;
    const vptNode = numSigsPayer;
    const bpr = this.constants.BASE_TXRESPONSE_SIZE;
    const vptNet = numSigsTotal;
    const rbhNet =
      (this.constants.BASE_RECEIPT_SIZE + this.constants.RUNNING_HASH_SIZE + this.constants.SEQUENCE_NUMBER_SIZE) *
      (this.constants.MINUTES_3 / this.constants.HOURS_1);
    const rbhSvc = ((this.constants.BASE_RECORD_SIZE + memoSize) * this.constants.MINUTES_3) / this.constants.HOURS_1;

    // Aggregate tiny-cents across node, network, and service
    let tinycents = 0;
    // Node fees
    tinycents += this.scheduleCoefficients.node.constant * 1;
    tinycents += this.scheduleCoefficients.node.bpt * bpt;
    tinycents += this.scheduleCoefficients.node.vpt * vptNode;
    tinycents += this.scheduleCoefficients.node.bpr * bpr;
    // Network fees
    tinycents += this.scheduleCoefficients.network.constant * 1;
    tinycents += this.scheduleCoefficients.network.bpt * bpt;
    tinycents += this.scheduleCoefficients.network.vpt * vptNet;
    tinycents += this.scheduleCoefficients.network.rbh * rbhNet;
    // Service fees
    tinycents += this.scheduleCoefficients.service.constant * 1;
    tinycents += this.scheduleCoefficients.service.rbh * rbhSvc;

    // Convert tiny-cents → USD
    const usd = tinycents / (1_000 * 1e10);

    // Convert USD → ℏ
    const hbar = (usd * 100) / centsPerHbar;
    const roundedHbar = Math.ceil(hbar * 1e5) / 1e5;

    return { usd: Number(usd.toFixed(6)), hbar: Number(roundedHbar.toFixed(5)) };
  }
}
