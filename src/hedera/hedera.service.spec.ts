import { Client, PublicKey, Status, TopicCreateTransaction, TopicMessageSubmitTransaction } from '@hashgraph/sdk';
import { HttpException, Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { of, throwError } from 'rxjs';

import { AxiosResponse } from 'axios';
import { ConfigService } from '@nestjs/config';
import { HederaService } from './hedera.service';
import { HttpService } from '@nestjs/axios';
import { KeyManagementService } from '../key-management/key-management.service';
import { HederaEstimateTransactionFeeService } from './hedera-estimate-transaction-fee.service';
import { BalanceAlertService } from '../balance-alerting/balance-alert.service';

jest.mock('@hashgraph/sdk');
jest.mock('node-fetch');

const mockedConfigService = () => ({
  get: jest.fn(),
  getOrThrow: jest.fn(),
});
const mockedLogger = () => ({
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
});

const mockedKmsService = () => ({
  signMessage: jest.fn().mockResolvedValue(Buffer.from('mocked-signature')),
});

const testAccountId = 'test-account-id';
const testPublicKey = 'test-public-key';
const hederaNetwork = 'testnet';
const mirrorNodeUrl = 'http://localhost:8080';
const testHederaNodes = '0.0.1,0.0.2,0.0.3';

describe('HederaService', () => {
  let service: HederaService;
  let httpService: HttpService;
  let configService: ReturnType<typeof mockedConfigService>;
  let kmsService: ReturnType<typeof mockedKmsService>;
  let logger: ReturnType<typeof mockedLogger>;
  let mockClient: any;

  beforeEach(async () => {
    configService = mockedConfigService();
    logger = mockedLogger();
    kmsService = mockedKmsService();

    configService.get.mockReturnValueOnce(hederaNetwork); // HEDERA_NETWORK
    configService.getOrThrow.mockImplementation((key) => {
      if (key === 'HEDERA_ACCOUNT_ID') return testAccountId;
      if (key === 'MIRROR_NODE_URL') return mirrorNodeUrl;
      if (key === 'HEDERA_OPERATOR_PUBLIC_KEY_DER') return testPublicKey;
      if (key === 'HEDERA_NODES') return testHederaNodes;
      return null;
    });

    // Mock the PublicKey.fromString
    jest.spyOn(PublicKey, 'fromString').mockReturnValue({
      toString: jest.fn().mockReturnValue(testPublicKey),
    } as any);

    // Mock the client
    mockClient = {
      setOperatorWith: jest.fn().mockReturnThis(),
      forName: jest.fn().mockReturnThis(),
    };
    (Client.forName as jest.Mock).mockReturnValue(mockClient);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HederaService,
        HederaEstimateTransactionFeeService,
        { provide: ConfigService, useValue: configService },
        { provide: Logger, useValue: logger },
        { provide: HttpService, useValue: { get: jest.fn() } },
        { provide: BalanceAlertService, useValue: { ensureSufficientSystemOperatorBalance: jest.fn() } },
        { provide: KeyManagementService, useValue: kmsService },
      ],
    }).compile();

    service = module.get(HederaService);
    httpService = module.get(HttpService);
  });

  afterEach(() => {
    jest.restoreAllMocks();
    jest.clearAllMocks();
  });

  describe('createTopic', () => {
    it('should create a topic successfully', async () => {
      const mockTopicId = '0.0.12345';
      const mockReceipt = { topicId: mockTopicId, status: Status.Success };
      const mockExecuteResponse = {
        getReceipt: jest.fn().mockResolvedValue(mockReceipt),
      };

      (TopicCreateTransaction.prototype.freezeWith as jest.Mock).mockReturnThis();
      (TopicCreateTransaction.prototype.signWithOperator as jest.Mock).mockReturnThis();
      (TopicCreateTransaction.prototype.execute as jest.Mock).mockResolvedValue(mockExecuteResponse);

      const result = await service['createTopic']();

      expect(result).toEqual(mockTopicId);
      expect(TopicCreateTransaction.prototype.execute).toHaveBeenCalledWith(mockClient);
    });

    it('should throw error if topic creation transaction fails', async () => {
      (TopicCreateTransaction.prototype.freezeWith as jest.Mock).mockReturnThis();
      (TopicCreateTransaction.prototype.execute as jest.Mock).mockRejectedValue(new Error('Transaction failed'));

      await expect(service['createTopic']()).rejects.toThrow(HttpException);
    });
  });

  describe('submitMessage', () => {
    it('should submit message to topic successfully', async () => {
      const mockReceipt = { status: Status.Success };
      const mockExecuteResponse = {
        getReceipt: jest.fn().mockResolvedValue(mockReceipt),
      };

      (TopicMessageSubmitTransaction.prototype.setTopicId as jest.Mock).mockReturnThis();
      (TopicMessageSubmitTransaction.prototype.setMessage as jest.Mock).mockReturnThis();
      (TopicMessageSubmitTransaction.prototype.freezeWith as jest.Mock).mockReturnThis();
      (TopicMessageSubmitTransaction.prototype.signWithOperator as jest.Mock).mockResolvedValue({
        execute: jest.fn().mockResolvedValue(mockExecuteResponse),
      });

      await service['submitMessage']('0.0.123', 'test message');

      expect(TopicMessageSubmitTransaction.prototype.setTopicId).toHaveBeenCalled();
      expect(TopicMessageSubmitTransaction.prototype.setMessage).toHaveBeenCalled();
      expect(TopicMessageSubmitTransaction.prototype.freezeWith).toHaveBeenCalledWith(mockClient);
      expect(TopicMessageSubmitTransaction.prototype.signWithOperator).toHaveBeenCalledWith(mockClient);
    });

    it('should throw error if message submission fails', async () => {
      (TopicMessageSubmitTransaction.prototype.setTopicId as jest.Mock).mockReturnThis();
      (TopicMessageSubmitTransaction.prototype.setMessage as jest.Mock).mockReturnThis();
      (TopicMessageSubmitTransaction.prototype.freezeWith as jest.Mock).mockReturnThis();
      (TopicMessageSubmitTransaction.prototype.signWithOperator as jest.Mock).mockRejectedValue(
        new Error('Transaction failed'),
      );

      await expect(service['submitMessage']('0.0.123', 'test message')).rejects.toThrow(
        'Failed to submit message to topic 0.0.123: Transaction failed',
      );
    });
  });

  describe('getMessagesFromTopic', () => {
    it('should fetch and decode messages successfully', async () => {
      const mockMessagesResponse = {
        messages: [
          { message: Buffer.from('message1', 'utf-8').toString('base64') },
          { message: Buffer.from('message2', 'utf-8').toString('base64') },
        ],
      };

      jest.spyOn(httpService, 'get').mockReturnValue(
        of({
          data: mockMessagesResponse,
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {},
        } as AxiosResponse),
      );

      const messages = await service.getMessagesFromTopic('0.0.123');
      expect(messages).toEqual([{ message: 'message1' }, { message: 'message2' }]);
    });

    it('should return empty array if no messages are found in response', async () => {
      const mockEmptyMessagesResponse = { messages: [] };
      jest.spyOn(httpService, 'get').mockReturnValue(
        of({
          data: mockEmptyMessagesResponse,
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {},
        } as AxiosResponse),
      );

      const messages = await service.getMessagesFromTopic('0.0.123');
      expect(messages).toEqual([]); // Still returns empty array for no messages in response
    });

    it('should throw error if response is not ok', async () => {
      jest.spyOn(httpService, 'get').mockReturnValue(throwError(() => new Error('HTTP error! status: 404')));

      await expect(service.getMessagesFromTopic('0.0.123')).rejects.toThrow(HttpException);
    });
  });
});
