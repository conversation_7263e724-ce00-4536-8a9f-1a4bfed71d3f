import { ConfigModule } from '@nestjs/config';
import { HederaService } from './hedera.service';
import { HttpModule } from '@nestjs/axios';
import { KeyManagementModule } from '../key-management/key-management.module';
import { Module } from '@nestjs/common';
import { HederaEstimateTransactionFeeService } from './hedera-estimate-transaction-fee.service';
import { BalanceAlertModule } from '../balance-alerting/balance-alert.module';

@Module({
  imports: [ConfigModule, HttpModule, KeyManagementModule, BalanceAlertModule],
  providers: [HederaService, HederaEstimateTransactionFeeService],
  exports: [HederaService],
})
export class HederaModule {}
