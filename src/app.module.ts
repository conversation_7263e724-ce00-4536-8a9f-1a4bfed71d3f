import { App<PERSON>ontroller } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { BalanceAlertModule } from './balance-alerting/balance-alert.module';
import { ConditionalModule, ConfigModule } from '@nestjs/config';
import { DatabaseModule } from './database/database.module';
import { GrantApplicationModule } from './grant-application/grant-application.module';
import { GrantCallModule } from './grant-call/grant-call.module';
import { GrantProgramModule } from './grant-program/grant-program.module';
import { Module } from '@nestjs/common';
import { NotificationsModule } from './notifications/notifications.module';
import { VotesModule } from './votes/votes.module';
import { WorkflowModule } from './workflow/workflow.module';
import { SnapshotModule } from './snapshot/snapshot.module';
import { JwtAuthGuard } from './auth/guards/jwt-auth.guard';
import { APP_GUARD } from '@nestjs/core';
import { RolesGuard } from './auth/guards/roles.guard';
import { UserModule } from './user/user.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    DatabaseModule,
    AuthModule,
    GrantProgramModule,
    GrantCallModule,
    GrantApplicationModule,
    NotificationsModule,
    BalanceAlertModule,
    WorkflowModule,
    VotesModule,
    ConditionalModule.registerWhen(SnapshotModule, (env) => env.NODE_ENV !== 'production'),
    UserModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useExisting: JwtAuthGuard,
    },
    JwtAuthGuard,
    {
      provide: APP_GUARD,
      useExisting: RolesGuard,
    },
    RolesGuard,
  ],
})
export class AppModule {}
