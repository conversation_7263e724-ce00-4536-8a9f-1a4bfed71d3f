import { MigrationInterface, QueryRunner } from 'typeorm';

export class Update_User_Notification_Preference_Type1752042208143 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1) Rename the existing enum type
    await queryRunner.query(`
            ALTER TYPE "public"."user_notification_preferences_notificationtype_enum"
            RENAME TO "user_notification_preferences_notificationtype_enum_old"
        `);

    // 2) Assemble a list of types
    const labels = ['grant_application_update', 'grant_call_update'];
    const labelsSql = labels.map((l) => `'${l.replace(/'/g, "''")}'`).join(', ');

    // 3) Create a brand-new enum type
    await queryRunner.query(`
            CREATE TYPE "public"."user_notification_preferences_notification_type_enum" AS ENUM(${labelsSql})
        `);

    // 4) Wipe out existing rows so ALTER TYPE can’t fail
    await queryRunner.query(`
            DELETE FROM "public"."user_notification_preferences";
        `);

    // 5) Rebind the column to the new type
    await queryRunner.query(`
            ALTER TABLE "public"."user_notification_preferences"
            ALTER COLUMN "notificationType"
            TYPE "public"."user_notification_preferences_notification_type_enum"
            USING ("notificationType"::text)::"public"."user_notification_preferences_notification_type_enum"
        `);

    // 6) Seed notification preferences for every user with both new types
    await queryRunner.query(`
        INSERT INTO "public"."user_notification_preferences"("userId","notificationType","enabled")
        SELECT 
        u.id,
        t.notification_type,
        true
        FROM "public"."user" u
        CROSS JOIN LATERAL (
        SELECT unnest(
            enum_range(NULL::"public"."user_notification_preferences_notification_type_enum")
        ) AS notification_type
        ) AS t;
    `);

    // 7) Drop the old enum type
    await queryRunner.query(`
            DROP TYPE "public"."user_notification_preferences_notificationtype_enum_old"
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 1) Rename the current enum so we can rebuild the original
    await queryRunner.query(`
            ALTER TYPE "public"."user_notification_preferences_notification_type_enum"
            RENAME TO "user_notification_preferences_notification_type_enum_old"
        `);

    const labels = [
      'application-approved',
      'application-moved',
      'application-rejected',
      'grant-application-assignee-changed',
      'grant-call-invitation',
      'new-grant-application',
    ];
    const labelsSql = labels.map((l) => `'${l.replace(/'/g, "''")}'`).join(', ');

    // 2) Recreate the old enum
    await queryRunner.query(`
            CREATE TYPE "public"."user_notification_preferences_notificationtype_enum" AS ENUM(${labelsSql})
        `);

    // 3) Delete all rows so the ALTER TYPE can’t fail
    await queryRunner.query(`
            DELETE FROM "public"."user_notification_preferences";
        `);

    // 4) Point the column back at the recreated enum
    await queryRunner.query(`
            ALTER TABLE "public"."user_notification_preferences"
            ALTER COLUMN "notificationType"
            TYPE "public"."user_notification_preferences_notificationtype_enum"
            USING ("notificationType"::text)::"public"."user_notification_preferences_notificationtype_enum"
        `);

    // 5) Finally, drop the intermediate old type
    await queryRunner.query(`
            DROP TYPE "public"."user_notification_preferences_notification_type_enum_old"
        `);
  }
}
