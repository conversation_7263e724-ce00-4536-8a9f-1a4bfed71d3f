import { MigrationInterface, QueryRunner } from 'typeorm';

export class Add_PK_FOR_User_Notification_Preferences1752132605445 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "public"."user_notification_preferences"
            ADD CONSTRAINT "uq_user_notification_preferences_userid_type"
            UNIQUE ("userId", "notificationType")
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "public"."user_notification_preferences"
            DROP CONSTRAINT "uq_user_notification_preferences_userid_type"
        `);
  }
}
