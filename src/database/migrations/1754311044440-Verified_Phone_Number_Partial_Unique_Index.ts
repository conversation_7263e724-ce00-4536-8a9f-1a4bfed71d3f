import { MigrationInterface, QueryRunner } from 'typeorm';

export class Verified_Phone_Number_Partial_Unique_Index1754311044440 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "user"
      DROP CONSTRAINT IF EXISTS "UQ_f2578043e491921209f5dadd080";
    `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX "UQ_verified_phone_number"
      ON "user" ("phoneNumber")
      WHERE "isPhoneVerified" IS TRUE;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP INDEX IF EXISTS "UQ_verified_phone_number";
    `);

    await queryRunner.query(`
      ALTER TABLE "user"
      ADD CONSTRAINT "UQ_f2578043e491921209f5dadd080"
      UNIQUE ("phoneNumber");
    `);
  }
}
