import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1749115487096 implements MigrationInterface {
  name = 'Migration1749115487096';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "grant_application" ADD "finalVotingTopicId" character varying(255)`);
    await queryRunner.query(`CREATE TYPE "public"."VotingType" AS ENUM('community_voting', 'final_community_voting')`);
    await queryRunner.query(`ALTER TABLE "votes" ADD "voteType" "public"."VotingType"`);

    await queryRunner.query(
      `UPDATE "votes" SET "voteType" = 'community_voting'::"public"."VotingType" WHERE "voteType" IS NULL`,
    );

    await queryRunner.query(`ALTER TABLE "votes" ALTER COLUMN "voteType" SET NOT NULL`);

    await queryRunner.query(`ALTER TABLE "votes" DROP CONSTRAINT "FK_77a56e5a66022b5b611e614f17c"`);
    await queryRunner.query(`ALTER TABLE "votes" DROP CONSTRAINT "UQ_77a56e5a66022b5b611e614f17c"`);
    await queryRunner.query(
      `ALTER TABLE "votes" ADD CONSTRAINT "FK_77a56e5a66022b5b611e614f17c" FOREIGN KEY ("grantApplicationId") REFERENCES "grant_application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "votes" DROP CONSTRAINT "FK_77a56e5a66022b5b611e614f17c"`);
    await queryRunner.query(
      `ALTER TABLE "votes" ADD CONSTRAINT "UQ_77a56e5a66022b5b611e614f17c" UNIQUE ("grantApplicationId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "votes" ADD CONSTRAINT "FK_77a56e5a66022b5b611e614f17c" FOREIGN KEY ("grantApplicationId") REFERENCES "grant_application"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "votes" DROP COLUMN "voteType"`);
    await queryRunner.query(`DROP TYPE "public"."VotingType"`);
    await queryRunner.query(`ALTER TABLE "grant_application" DROP COLUMN "finalVotingTopicId"`);
  }
}
