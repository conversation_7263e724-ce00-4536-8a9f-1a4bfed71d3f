import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddClosedToWorkflowStatus1747916633087 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1) Rename the existing enum type
    await queryRunner.query(`
      ALTER TYPE "public"."WorkflowStatus"
      RENAME TO "workflow_state_status_enum_old"
    `);

    // 2) Read all of the old labels
    const oldLabels: Array<{ label: string }> = await queryRunner.query(`
      SELECT enumlabel AS label
      FROM pg_enum
      WHERE enumtypid = (
        SELECT oid
        FROM pg_type
        WHERE typname = 'workflow_state_status_enum_old'
      )
      ORDER BY enumsortorder
    `);

    // 3) Assemble a list of labels, adding CLOSED if missing
    const labels = oldLabels.map((r) => r.label);
    if (!labels.includes('CLOSED')) {
      labels.push('CLOSED');
    }
    const labelsSql = labels.map((l) => `'${l.replace(/'/g, "''")}'`).join(', ');

    // 4) Create a brand-new enum type with every label
    await queryRunner.query(`
      CREATE TYPE "public"."workflow_state_status_enum" AS ENUM(${labelsSql})
    `);

    // 5) Drop the default value first
    await queryRunner.query(`
      ALTER TABLE "workflow_state"
      ALTER COLUMN "status" DROP DEFAULT
    `);

    // 6) Rebind the column to the new type (via text cast)
    await queryRunner.query(`
      ALTER TABLE "workflow_state"
      ALTER COLUMN "status"
      TYPE "public"."workflow_state_status_enum"
      USING ("status"::text::public.workflow_state_status_enum)
    `);

    // 7) Set the default value back
    await queryRunner.query(`
      ALTER TABLE "workflow_state"
      ALTER COLUMN "status" SET DEFAULT 'IN_PROGRESS'::public.workflow_state_status_enum
    `);

    // 8) Drop the old enum type
    await queryRunner.query(`
      DROP TYPE "public"."workflow_state_status_enum_old"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 1) Rename the current enum so we can rebuild the original
    await queryRunner.query(`
      ALTER TYPE "public"."workflow_state_status_enum"
      RENAME TO "workflow_state_status_enum_old"
    `);

    // 2) Pull back all labels from that old‐named type
    const oldLabels: Array<{ label: string }> = await queryRunner.query(`
      SELECT enumlabel AS label
      FROM pg_enum
      WHERE enumtypid = (
        SELECT oid
        FROM pg_type
        WHERE typname = 'workflow_state_status_enum_old'
      )
      ORDER BY enumsortorder
    `);

    // 3) Drop CLOSED from the list
    const labels = oldLabels.map((r) => r.label).filter((l) => l !== 'CLOSED');
    const labelsSql = labels.map((l) => `'${l.replace(/'/g, "''")}'`).join(', ');

    // 4) Recreate the original enum with only the remaining labels
    await queryRunner.query(`
      CREATE TYPE "public"."WorkflowStatus" AS ENUM(${labelsSql})
    `);

    // 5) Drop the default value first
    await queryRunner.query(`
      ALTER TABLE "workflow_state"
      ALTER COLUMN "status" DROP DEFAULT
    `);

    // 6) Point the column back at the recreated enum
    await queryRunner.query(`
      ALTER TABLE "workflow_state"
      ALTER COLUMN "status"
      TYPE "public"."WorkflowStatus"
      USING ("status"::text::public.WorkflowStatus)
    `);

    // 7) Set the default value back
    await queryRunner.query(`
      ALTER TABLE "workflow_state"
      ALTER COLUMN "status" SET DEFAULT 'IN_PROGRESS'::public.WorkflowStatus
    `);

    // 8) Finally, drop the intermediate old type
    await queryRunner.query(`
      DROP TYPE "public"."workflow_state_status_enum_old"
    `);
  }
}
