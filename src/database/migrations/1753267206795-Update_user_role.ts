import { MigrationInterface, QueryRunner } from 'typeorm';

export class Update_user_role1753267206795 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // 1) Rename the existing enum type
    await queryRunner.query(`
      ALTER TYPE "user_role_enum" RENAME TO "user_role_enum_old";
    `);

    // 2) Create the new enum type
    await queryRunner.query(`
      CREATE TYPE "user_role_enum" AS ENUM (
        'coordinator',
        'community_member'
      );
    `);

    // 3) Drop the default so we can change the column type
    await queryRunner.query(`
      ALTER TABLE "user"
      ALTER COLUMN "role" DROP DEFAULT;
    `);

    // 4) Recast the column, mapping old roles to new ones
    await queryRunner.query(`
      ALTER TABLE "user"
      ALTER COLUMN "role" TYPE "user_role_enum"
      USING (
        CASE
          WHEN role = 'GRANT_PROGRAM_COORDINATOR' THEN 'coordinator'::user_role_enum
          WHEN role = 'USER'                       THEN 'community_member'::user_role_enum
        END
      );
    `);

    // 5) <PERSON>ore the default for new rows
    await queryRunner.query(`
      ALTER TABLE "user"
      ALTER COLUMN "role" SET DEFAULT 'community_member';
    `);

    // 6) Drop the old enum type
    await queryRunner.query(`
      DROP TYPE "user_role_enum_old";
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 1) Rename current enum so we can recreate the old one
    await queryRunner.query(`
      ALTER TYPE "user_role_enum" RENAME TO "user_role_enum_new";
    `);

    // 2) Re-create the original enum values
    await queryRunner.query(`
      CREATE TYPE "user_role_enum" AS ENUM (
        'GRANT_PROGRAM_COORDINATOR',
        'USER'
      );
    `);

    // 3) Drop default so we can swap back
    await queryRunner.query(`
      ALTER TABLE "user"
      ALTER COLUMN "role" DROP DEFAULT;
    `);

    // 4) Recast the column back to uppercase labels
    await queryRunner.query(`
      ALTER TABLE "user"
      ALTER COLUMN "role" TYPE "user_role_enum"
      USING (
        CASE
          WHEN role = 'coordinator' THEN 'GRANT_PROGRAM_COORDINATOR'::user_role_enum
          WHEN role = 'community_member'          THEN 'USER'::user_role_enum
        END
      );
    `);

    // 5) Restore the old default
    await queryRunner.query(`
      ALTER TABLE "user"
      ALTER COLUMN "role" SET DEFAULT 'USER';
    `);

    // 6) Drop the transient enum type
    await queryRunner.query(`
      DROP TYPE "user_role_enum_new";
    `);
  }
}
