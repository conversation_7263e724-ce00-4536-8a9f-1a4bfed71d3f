import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1753265278178 implements MigrationInterface {
  name = 'Migration1753265278178';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "snapshots" 
      ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
      "name" character varying(255) NOT NULL, 
      "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), 
      "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), 
      CONSTRAINT "PK_f5661b5fd4224d23e26a631986b" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "snapshots"`);
  }
}
