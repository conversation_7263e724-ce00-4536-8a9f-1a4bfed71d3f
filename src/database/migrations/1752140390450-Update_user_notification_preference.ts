import { MigrationInterface, QueryRunner } from 'typeorm';

export class Update_user_notification_preference1752140390450 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "public"."user_notification_preferences"
            RENAME COLUMN "enabled" TO "isEnabled"
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "public"."user_notification_preferences"
            RENAME COLUMN "isEnabled" TO "enabled"
        `);
  }
}
