import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1751523749255 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "grant_application"
            ADD COLUMN "pitchFileUrl" VARCHAR(255) NULL;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "grant_application"
            DROP COLUMN "pitchFileUrl";
        `);
  }
}
