{"name": "tha-fp-backend", "version": "0.2.2", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"script": "ts-node", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json --runInBand", "test:e2e:watch": "jest --config ./test/jest-e2e.json --runInBand --watch", "test:json": "jest --coverage --coverageReporters json-summary", "prepare": "husky", "typeorm:show": "ts-node ./node_modules/typeorm/cli.js migration:show -d ./src/database/typeorm.config.ts", "typeorm:create": "ts-node ./node_modules/typeorm/cli.js migration:create ./src/database/migrations/Migration", "typeorm:generate": "ts-node ./node_modules/typeorm/cli.js migration:generate ./src/database/migrations/Migration -d ./src/database/typeorm.config.ts", "typeorm:migrate": "ts-node ./node_modules/typeorm/cli.js migration:run -d ./src/database/typeorm.config.ts", "typeorm:revert": "ts-node ./node_modules/typeorm/cli.js migration:revert -d ./src/database/typeorm.config.ts"}, "dependencies": {"@aws-sdk/client-kms": "^3.787.0", "@aws-sdk/client-s3": "3.758.0", "@aws-sdk/client-ses": "^3.758.0", "@aws-sdk/client-sns": "^3.758.0", "@hashgraph/sdk": "2.60.1", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.11", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.11", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.11", "@nestjs/swagger": "^11.0.6", "@nestjs/typeorm": "^11.0.0", "@opentelemetry/exporter-trace-otlp-grpc": "^0.200.0", "@opentelemetry/exporter-trace-otlp-http": "^0.200.0", "@opentelemetry/instrumentation": "^0.200.0", "@opentelemetry/instrumentation-express": "^0.48.1", "@opentelemetry/instrumentation-http": "^0.200.0", "@opentelemetry/instrumentation-nestjs-core": "^0.46.0", "@opentelemetry/resources": "^2.0.0", "@opentelemetry/sdk-trace-base": "^2.0.0", "@opentelemetry/sdk-trace-node": "^2.0.0", "@opentelemetry/semantic-conventions": "^1.32.0", "axios": "^1.8.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dayjs": "^1.11.13", "google-auth-library": "^9.15.1", "handlebars": "^4.7.8", "keccak256": "^1.0.6", "multer": "^1.4.5-lts.1", "nestjs-rate-limiter": "^3.1.0", "nodemailer": "^6.10.0", "octokit": "^4.1.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "prom-client": "^15.1.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "sanitize-html": "^2.14.0", "slugify": "^1.6.6", "typeorm": "^0.3.21"}, "devDependencies": {"@eslint/js": "^9.22.0", "@jsx-email/doiuse-email": "^1.0.4", "@nestjs/cli": "^11.0.5", "@nestjs/schematics": "^11.0.2", "@nestjs/testing": "^11.0.11", "@testcontainers/localstack": "^11.5.0", "@testcontainers/postgresql": "^10.19.0", "@types/elliptic": "^6.4.18", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.13.10", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/sanitize-html": "^2.13.0", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "elliptic": "^6.6.1", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "husky": "^9.1.7", "jest": "^29.7.0", "pg": "^8.14.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "testcontainers": "^10.19.0", "ts-jest": "^29.2.6", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2", "typescript-eslint": "^8.26.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}