## Database-related variables
# This file contains the environment variables that should be used in the application. Populate the values with the
# correct information.
POSTGRES_HOST=
POSTGRES_PORT=
POSTGRES_DB=
POSTGRES_USER=
POSTGRES_PASSWORD=
# Path to a CA certificate used by the Postgres server to self-sign its TLS certificate. Leave empty if the server does
# not use TLS. You can specify a faulty path (e.g., '/') to allow unauthorized TLS connections. Meaninig that the
# connection will be made without verifying the server's certificate.
POSTGRES_CA_CERT=
# Indicates if the database schema should be updated to the state of the entitites defined in the code on application
# launch. This should definitely be false in production. It's mostly useful for local development.
POSTGRES_SYNC=
# Indicates if the database migrations should run automatically on application launch. If set to true the application
# will run all pending migrations before starting. This is useful to migrate deployed environments automatically.
POSTGRES_MIGRATIONS_RUN=

## S3-related variables
# These variables are used to configure the S3 storage provider. The application uses S3 to store files uploaded by
# users when they create a new grant program.
AWS_ENDPOINT=
AWS_REGION=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_BUCKET_NAME=

## JWT-related variables
# The secret that is input to HMAC when signing tokens. Should be a long random string.
JWT_SECRET=
# The default time after which JWT tokens will expire. Used for session tokens. Can be a string like '1d' or '15m'.
JWT_EXPIRATION_TIME=
# The time after which recovery tokens expire. These tokens are use in emails used for account recovery. 
RECOVERY_TOKEN_EXPIRATION_TIME=
# The time after which challenge tokens expire. These tokens are used to verify that a challenge signed by a Hedera
# account originated from the backend.
CHALLENGE_TOKEN_EXPIRATION_TIME=
# The number of minutes after which a one-time password (OTP) expires. Used to configure the OTP validity period in verification flows
OTP_EXPIRY_TIME_IN_MINUTES=

## Email-related variables
AWS_SES_ACCESS_KEY=
AWS_SES_SECRET_KEY=
AWS_SES_REGION=
AWS_SES_FROM_EMAIL=

## Key Management Service (KMS)
AWS_KMS_REGION=
AWS_KMS_KEY_ID=
# Set this to true if you want to preview emails instead of sending them. The emails will be saved in "temp" directory.
# This variable is only useful for development purposes and is not necessary in deployed environments.
EMAIL_PREVIEW=
EMAIL_ASSETS_URL=https://tha-dev-funding-platform-api-data-storage.s3.eu-central-2.amazonaws.com/email-assets

## Other
# The URL of the Hedera mirror node that the application should use to fetch Hedera account information.
MIRROR_NODE_URL=
# The origins that are allowed to make requests to the API. This should be a comma-separated list of origins. 
# E.g. https://fundingplatform-app.dev.hashgraph-group.com,http://localhost:3001
CORS_ORIGINS=
# The base URL of the frontend application. Or more precisely the origin of the frontend. E.g, http://localhost:3001 
# It's used to generate links in emails that refer back to the frontend application.
FRONTEND_BASE_URL=
# Log level for the application. Options are: verbose, debug, log, warn, error, fatal
LOG_LEVEL=

# The port on which the backend server will listen for incoming requests.
BACKEND_PORT=

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

LINKEDIN_API_URL=
TWITTER_API_URL=

# USE_LOCALSTACK:  Set to 'true' to use LocalStack for emulating AWS services locally during development or testing.
#                  Set to 'false' to use actual AWS services (e.g., in staging or production environments).
#                  Currently set to 'false', meaning this application will connect to real AWS services.
USE_LOCALSTACK=false

HEDERA_ACCOUNT_ID= # The Hedera account ID that will be used to sign transactions related to Hedera topics
HEDERA_OPERATOR_PUBLIC_KEY_DER= # The public key of the Hedera account that will be used to sign transactions related to Hedera topics
HEDERA_NETWORK= # The Hedera network where the topic transactions will be submitted (e.g., testnet, mainnet).
HEDERA_NODES= # A comma-separated list of Hedera nodes to use for submitting transactions (only needed for transactions signed by user via WalletConnect).

# Hedera Topic Creation Retry Configuration for Vote and Grant Application Services
# Maximum number of retries to attempt when creating a Hedera topic.
# Used in Vote and Grant Application services to ensure topic creation succeeds,
HEDERA_MAX_TOPIC_CREATION_RETRIES=
# Delay in milliseconds between each retry attempt when creating a Hedera topic.
# Used in Vote and Grant Application services to avoid overwhelming the Hedera network
# during retry attempts.
HEDERA_TOPIC_CREATION_RETRY_DELAY_MS=
# Hedera submit message Retry Configuration for Vote and Grant Application Services
# Maximum number of retries to attempt when submitting a Hedera message.
# Used in Vote and Grant Application services to ensure message submission succeeds,
HEDERA_MAX_MESSAGE_SUBMIT_RETRIES=
# Delay in milliseconds between each retry attempt when submitting a Hedera message.
# Used in Vote and Grant Application services to avoid overwhelming the Hedera network
# during retry attempts.
HEDERA_MESSAGE_SUBMIT_RETRY_DELAY_MS=
# The maximum size of the message payload that can be sent in a Hedera transaction.
HEDERA_MESSAGE_PAYLOAD_LIMIT= 


# Voting Configuration
MINIMUM_QUORUM_PERCENTAGE=30
MINIMUM_NET_VOTING_SCORE_PERCENTAGE=10
# --- Balance Alerting & Operational Minimums ---
# Minimum HBAR balance required in HEDERA_ACCOUNT_ID before attempting DLT operations.
# If balance falls below this, operations (e.g., anchoring grant app changes) will be blocked.
# This is a pre-flight check; distinct from the internal email alert thresholds (e.g., 25, 50, 100 Hbar).
HEDERA_OPERATION_MIN_BALANCE_HBAR="5"

# Fallback email for system-critical balance alerts if a user isn't found in DB for HEDERA_ACCOUNT_ID.
EMERGENCY_ALERT_EMAIL=
