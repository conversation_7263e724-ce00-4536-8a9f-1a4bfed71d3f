== ADR: Voting

[cols="1, 1,3,1, 3", id=changelog, options="header"]
|===
|Version |Status |Next steps |Last updated| Comment
|0.1 |Decided |- |03.03.2025 |-
|0.2 |Updated|-|13.03.2025 | Updated after creation of adr-007
|0.3 |Updated |- |10.04.2025 | Updated after realingnment meeting
|===

=== Context
This adr summarizes the voting activities happening on the Grant Management Platform. For each application, users will be able to cast a vote. Either they are in favor of the application or against it. Please check the adr-002 for the topics available on each application.

=== Restrictions
- users pay for their submitting the message of their vote(s)
- voting periods start and end with a dedicated message on the voting topic of each application
- only the platforms key will be allowed to push messages

=== Hardening the process
As the topics for voting will be open for anybody to submit a message containing their vote, we need to ensure that the vote really came from a kyced user and not just from somebody holding a wallet.
To prevent the misuse of the topic we will need to add a signature of the platforms private key to the body.

=== Overview

==== Possible strings for the AUDIT_MESSAGE_FOR_VOTING
The following table outlines the possible values the AUDIT_MESSAGE_FOR_VOTING can take within the sequence diagram below.
[cols="1, 2", id=options, options="header"]
|===
|Scenario |Message
|In Favor |{UserWalletAddress} voted in favor for the application.
|Against |{UserWalletAddress} voted against for the application.
|Remove vote |{UserWalletAddress} removed the vote for the application.
|In Favor changed to against|{UserWalletAddress} changes the vote from being in favor to against for the application.
|Against to in Favor changed|{UserWalletAddress} changes the vote from being against to in favor for the application.
|===

For simplicity reasons, this sequence diagram does not display the responses of any calls as they are not relevant for the understanding of the process.
The very same process will be used for the first and second community voting phase.

[plantuml, target=images/fe_component, format=svg]
----
@startuml

participant a as "Grant Call Manager"
participant p as "Project"
participant u as "User"
participant gp as "Grant Management Platform"
participant at as "Application Actions Topic"
participant vt as "Application Votes Topic"

a -> gp: Creates new Grant Call
gp -> gp: Creates new Grant Call
== Application submission phase ==
loop Happens for every new application
p -> gp: Hands in application for Grant Call
gp -> gp: Creates new Grant Call Application
gp -> at: Creates dedicated actions topic for new Grant Call Application
note right
Only the THA wallet can submit
messages for this topic
end note
gp -> gp: Update the firstVotingTopicId database field of the application
gp -> at: Submits first message that the Grant Call Application has been created
end

== Community voting phase #1 ==
loop Happens for every application of a given grant call
gp -> vt: Creates dedicated vote topic for Grant Call Application
gp -> vt: Submits the first message to the topic
note right
Community voting started
end note
loop Users can vote on all grant call applications
u -> gp: Request to vote for or against a grant call
gp -> gp: Prepares topic message for user vote
note right
{
vote: AUDIT_MESSAGE_FOR_VOTING
}
end note
gp -> u: Requests wallet signature for message
u -> u: Signs message
u -> gp: Add vote to vote topic of the application
gp -> vt: Execute transaction
vt --> gp: Response
gp -> gp: Update vote history table
end

== Community voting phase #1 ends ==
loop Happens for every application of a given grant call
gp -> vt: Updates the topic so only the THA wallet can submit messages to it
gp -> vt: Creates a message that indicates the end of the voting phase 1
note right
Community voting ended
end note
gp -> vt: Submits summary of votes

note right
Total number of votes in favor: XXX. Total number of votes against: YYY
end note

alt application has enough votes to reach next stage
gp -> vt: Submits notification that the application moves to the next stage
note right
The application reached the required threshold of votes and moves to the next stage of the process.
end note
else application has not enough votes to reach next stage
gp -> vt: Submits notification that the application does not move to the next stage
note right
The application did not reach the required threshold of votes and does not move to the next stage of the process.
end note
end

end

== Community voting phase #2 ==
loop Happens for every application of a given grant call that moves into the 2nd voting phase
gp -> vt: Creates dedicated vote topic for the second community voting of the Grant Call Application
gp -> gp: Update the secondVotingTopicId database field of the application
gp -> vt: Submits the first message to the topic
note right
Final community voting started
end note
loop Users can vote on all grant call applications with a restriction of one total upvote and one total downvote
u -> gp: Request to vote for or against a grant call application
gp -> gp: Prepares topic message for user vote
note right
{
vote: AUDIT_MESSAGE_FOR_VOTING
}
end note
gp -> u: Requests wallet signature for message
u -> u: Signs message
u -> vt: Add vote to vote topic of the application
end

== Community voting phase #2 ends ==
loop Happens for every application of a given grant call that is still active and moved into the 2nd community voting phase
gp -> vt: Updates the topic so only the THA wallet can submit messages to it
gp -> vt: Creates a message that indicates the end of the voting phase 2
note right
Final community voting ended
end note
gp -> vt: Submits summary of votes

note right
Total number of votes in favor: XXX. Total number of votes against: YYY
end note

gp -> vt: Submits a message which indicates the place of the application
note right
{
message: The application reached place number {PLACE} in the vote process.
}
end note

end

@enduml
----
