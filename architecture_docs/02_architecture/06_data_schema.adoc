== Data schema

The following diagram displays the current data schema used for the application.

[plantuml]
----
@startuml

enum user_role_enum {
  GRANT_PROGRAM_COORDINATOR
  USER
}

enum grant_category_enum {
  ON_CHAIN_FINANCE
  CONSUMER_LOYALTY
  SUSTAINABILITY
  DIGITAL_IDENTITY
  TRACEABILITY
  TELCO
  OTHER
}

enum business_category_enum {
  STARTUP
  ENTERPRISE
  GOVERNMENT
}

enum distribution_type_enum {
  PERCENTAGE
  FIXED_AMOUNT
}

enum workflow_status_enum {
  IN_PROGRESS
  READY_FOR_NEXT_STEP
  ACTION_REQUIRED
  APPROVED
  REJECTED
  WITHDRAWN
  CLOSED
}

enum stage_code_enum {
  GP_OPEN
  GP_FINALIZED
  GC_CLOSED
  GC_OPEN_FOR_APPLICATIONS
  GC_SCREENING
  GC_COMMUNITY_VOTING
  GC_ONBOARDING
  GC_FINAL_COMMUNITY_VOTING
  GC_FINALIZED
  GA_SCREENING
  GA_QUALIFICATION
  GA_INTERVIEW
  GA_DUE_DILIGENCE
  GA_TOWN_HALL
  GA_FINAL_QUALIFICATION
}

enum stage_transition_type_enum {
  MANUAL
  AUTOMATIC
}

enum workflow_entity_type_enum {
  PROGRAM
  CALL
  APPLICATION
}

enum notification_type_enum { 
  APPLICATION_APPROVED
  APPLICATION_MOVED
  APPLICATION_REJECTED
  GRANT_APPLICATION_ASSIGNEE_CHANGED
  GRANT_CALL_INVITATION
  NEW_GRANT_APPLICATION
}

enum voting_type_enum { 
  COMMUNITY_VOTING
  FINAL_COMMUNITY_VOTING
}

class User {
  +id : integer
  +email : varchar (unique)
  +addresses : text[] (not nullable)
  +displayName : varchar
  +emailVerified : boolean (default: false)
  +phoneNumber : varchar (unique, nullable)
  +isPhoneVerified : boolean (default: false)
  +phoneOtp : bigint (nullable)
  +phoneOtpExpiresAt : timestamptz (nullable)
  +otp : bigint (nullable)
  +otpExpiresAt : timestamptz (nullable)
  +createdAt : timestamptz (auto-generated)
  +role : user_role_enum (default: USER)
  +deletedAt : timestamptz (nullable, soft delete)
  +reactivatedAt : timestamptz (nullable)
  +linkedAccounts : jsonb (nullable, default: {})
}

class UserNotificationPreferences {
    +id: integer (Primary Key)
    +userId: integer (Foreign Key to User)
    +notificationType: notification_type_enum
    +enabled: boolean (default: true)
    +createdAt: timestamptz (auto-gen)
    +updatedAt: timestamptz (auto-gen)
}

class GrantApplication {
  +id : integer
  +title : varchar
  +description : varchar
  +companyName : varchar
  +companyCountry : varchar
  +categories : grant_category_enum[]
  +contactFullName : varchar
  +contactEmail : varchar
  +contactPhoneNumber : varchar
  +actionTopicId : varchar(255)
  +votingTopicId : varchar(255) (nullable)
  +finalVotingTopicId : varchar(255) (nullable)
  +workflowStateId : integer  (FK to WorkflowState)
  +createdById : integer (FK to User)
  +createdAt : timestamptz
  +updatedAt : timestamptz
  --
  +grantCallId : integer (FK to GrantCall)
}

class GrantCall {
  +id : integer
  +grantCallSlug : varchar (unique)
  +name : varchar
  +description : varchar
  +businessCategory : business_category_enum
  +categories : text[] (nullable)
  +totalGrantAmount : numeric(18,2) (nullable)
  +createdById : integer (FK to User)
  +workflowStateId : integer (nullable, FK to WorkflowState)
  +createdAt : timestamptz
  +updatedAt : timestamptz
  --
  +grantProgramId : integer (FK to GrantProgram)
}

class GrantProgram {
  +id : integer
  +grantProgramSlug : varchar (unique)
  +name : varchar
  +description : varchar
  +scope : varchar
  +budget : numeric(12,0)
  +grantorPublicProfileName : varchar
  +grantorLogoURL : varchar
  +grantorDescription : varchar
  +grantorWebsite : varchar
  +workflowStateId : integer (nullable, FK to WorkflowState)
  +createdAt : timestamptz
  +updatedAt : timestamptz
  --
  +grantProgramCoordinatorId : integer (FK to User)
}

class GrantCallStageSetting {
  +id : integer
  +grantCallId : integer (FK to GrantCall)
  +workflowStepDefinitionId : integer (FK to WorkflowStepDefinition)
  +startDate : timestamptz (nullable)
  +endDate : timestamptz (nullable)
  +durationSeconds : integer (nullable)
  +stageUrl : text (nullable)
}

class GrantDistributionRule {
  +id : integer
  +grantCallId : integer (FK to GrantCall)
  +rank : integer
  +type : distribution_type_enum
  +value : numeric(18,2)
}

class Vote {
  +id : integer
  +grantApplicationId: integer (FK to GrantApplication)
  +inFavorVotes: integer (default: 0)
  +againstVotes: integer (default: 0)
  +walletsInFavor : varchar[]
  +walletsAgainst : varchar[]
  +voteType : voting_type_enum
  +createdAt : timestamptz
  +updatedAt : timestamptz
}

class WorkflowState {
  +id : integer
  +workflowTemplateId : integer (nullable, FK to WorkflowTemplate)
  +currentStepDefinitionId : integer (nullable, FK to WorkflowStepDefinition)
  +status : workflow_status_enum (default: IN_PROGRESS)
  +currentStepTransitionedAt : timestamptz (nullable)
  +currentStepEndsAt : timestamptz (nullable)
}

class WorkflowStepDefinition {
  +id : integer
  +workflowTemplateId : integer (FK to WorkflowTemplate)
  +name : varchar(100)
  +code : stage_code_enum
  +sequenceNumber : integer
  +transitionType : stage_transition_type_enum (default: MANUAL)
  +description : text (nullable)
  +isTerminal : boolean (default: false)
}

class WorkflowTemplate {
  +id : integer
  +name : varchar(150) (unique)
  +entityType : workflow_entity_type_enum
}


' Relationships

User "1" -- "*" UserNotificationPreferences : has
User "1" -- "*" GrantApplication : "created by (createdById)"
User "1" -- "*" GrantCall : "created by (createdById)"
User "1" -- "*" GrantProgram : "coordinated by (grantProgramCoordinatorId)"


GrantProgram "1" -- "*" GrantCall : "contains"
GrantProgram "1" -- "0..1" WorkflowState : "manages status (workflowStateId)"


GrantCall "1" -- "*" GrantApplication : "has applications (grantCallId)"
GrantCall "1" -- "0..1" WorkflowState : "manages status (workflowStateId)"
GrantCall "1" -- "*" GrantCallStageSetting : "has settings (grantCallId)"
GrantCall "1" -- "*" GrantDistributionRule : "has rules (grantCallId)"


GrantApplication "1" -- "*" Vote : "has votes (grantApplicationId)"
GrantApplication "1" -- "1" WorkflowState : "manages status (workflowStateId)"


GrantCallStageSetting "*" -- "1" WorkflowStepDefinition : "uses definition (workflowStepDefinitionId)"


WorkflowTemplate "1" -- "*" WorkflowStepDefinition : "defines steps (workflowTemplateId)"

WorkflowState "0..1" -- "0..1" WorkflowTemplate : "based on template (workflowTemplateId)"
WorkflowState "0..1" -- "0..1" WorkflowStepDefinition : "at current step (currentStepDefinitionId)"


@enduml
----
